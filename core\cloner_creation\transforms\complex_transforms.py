"""
Комплексные трансформации для всех типов клонеров.

Содержит сложные функции, которые комбинируют несколько типов трансформаций:
- apply_instance_and_random_transforms: Комбинирует instance и random трансформации
- apply_unified_transforms: Применяет все типы трансформаций
- apply_linear_random_transforms: Специфичная логика для Linear клонера
"""

import bpy
from typing import Optional
from .random_transforms import (
    setup_random_position,
    setup_random_rotation,
    setup_random_scale,
    combine_base_and_random_rotation,
    combine_base_and_random_scale
)
from .instance_transforms import apply_instance_transforms
from .global_transform import apply_global_transforms

def apply_instance_and_random_transforms(nodes, links, instance_node, group_in,
                                       cloner_type: str = "LINEAR", mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет базовые и случайные трансформации БЕЗ глобальных трансформаций.

    Применяет трансформации в правильном порядке:
    1. Для Linear клонера: комбинирует Instance и Random трансформации
    2. Для других клонеров: применяет только базовые Instance трансформации

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов
        group_in: Входной узел группы
        cloner_type: Тип клонера (LINEAR, GRID, CIRCLE)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел с примененными трансформациями (БЕЗ глобальных)
    """
    try:
        current_node = instance_node
        current_socket = 'Instances'

        if cloner_type == "LINEAR":
            # Для Linear клонера комбинируем Instance и Random трансформации
            # Создаем индекс для случайных значений
            index_node = nodes.new('GeometryNodeInputIndex')
            index_node.location = (-300, -100)

            # Настраиваем случайные трансформации
            random_pos_node = setup_random_position(nodes, links, group_in, index_node, mode)
            random_rot_node = setup_random_rotation(nodes, links, group_in, index_node, mode)
            random_scale_node = setup_random_scale(nodes, links, group_in, index_node, mode)

            # Комбинируем базовые и случайные трансформации
            combined_rotation = combine_base_and_random_rotation(nodes, links, group_in, random_rot_node, mode)
            combined_scale = combine_base_and_random_scale(nodes, links, group_in, random_scale_node, mode)

            # Применяем трансформации к инстансам
            if random_pos_node:
                translate_instances = nodes.new('GeometryNodeTranslateInstances')
                translate_instances.location = (100, 200)
                links.new(current_node.outputs[current_socket], translate_instances.inputs['Instances'])
                links.new(random_pos_node.outputs['Value'], translate_instances.inputs['Translation'])
                current_node = translate_instances
                current_socket = 'Instances'

            if combined_rotation:
                rotate_instances = nodes.new('GeometryNodeRotateInstances')
                rotate_instances.location = (200, 200)
                links.new(current_node.outputs[current_socket], rotate_instances.inputs['Instances'])
                links.new(combined_rotation.outputs['Vector'], rotate_instances.inputs['Rotation'])
                current_node = rotate_instances
                current_socket = 'Instances'

            if combined_scale:
                scale_instances = nodes.new('GeometryNodeScaleInstances')
                scale_instances.location = (300, 200)
                links.new(current_node.outputs[current_socket], scale_instances.inputs['Instances'])
                links.new(combined_scale.outputs['Vector'], scale_instances.inputs['Scale'])
                current_node = scale_instances
                current_socket = 'Instances'
        else:
            # Для других клонеров применяем только базовые instance трансформации
            current_node = apply_instance_transforms(nodes, links, current_node, group_in, cloner_type, mode)

        return current_node
    except Exception as e:
        print(f"Ошибка при применении базовых и случайных трансформаций ({mode}): {e}")
        return instance_node

def apply_linear_random_transforms(nodes, links, instance_node, group_in, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет только случайные трансформации для Linear клонера.

    Используется когда базовые трансформации инстансов уже применены в instance_on_points.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов (с уже примененными базовыми трансформациями)
        group_in: Входной узел группы
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел с примененными случайными трансформациями
    """
    try:
        current_node = instance_node
        current_socket = 'Instances'

        # Создаем индекс для случайных значений
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (-300, -100)

        # Настраиваем случайные трансформации
        random_pos_node = setup_random_position(nodes, links, group_in, index_node, mode)
        random_rot_node = setup_random_rotation(nodes, links, group_in, index_node, mode)
        random_scale_node = setup_random_scale(nodes, links, group_in, index_node, mode)

        # Комбинируем базовые и случайные трансформации
        combined_rotation = combine_base_and_random_rotation(nodes, links, group_in, random_rot_node, mode)
        combined_scale = combine_base_and_random_scale(nodes, links, group_in, random_scale_node, mode)

        # Применяем трансформации к инстансам
        if random_pos_node:
            translate_instances = nodes.new('GeometryNodeTranslateInstances')
            translate_instances.location = (100, 200)
            links.new(current_node.outputs[current_socket], translate_instances.inputs['Instances'])
            links.new(random_pos_node.outputs['Value'], translate_instances.inputs['Translation'])
            current_node = translate_instances
            current_socket = 'Instances'

        if combined_rotation:
            rotate_instances = nodes.new('GeometryNodeRotateInstances')
            rotate_instances.location = (200, 200)
            links.new(current_node.outputs[current_socket], rotate_instances.inputs['Instances'])
            links.new(combined_rotation.outputs['Vector'], rotate_instances.inputs['Rotation'])
            current_node = rotate_instances
            current_socket = 'Instances'

        if combined_scale:
            scale_instances = nodes.new('GeometryNodeScaleInstances')
            scale_instances.location = (300, 200)
            links.new(current_node.outputs[current_socket], scale_instances.inputs['Instances'])
            links.new(combined_scale.outputs['Vector'], scale_instances.inputs['Scale'])
            current_node = scale_instances
            current_socket = 'Instances'

        return current_node
    except Exception as e:
        print(f"Ошибка при применении случайных трансформаций Linear клонера ({mode}): {e}")
        return instance_node

def apply_unified_transforms(nodes, links, instance_node, group_in,
                           cloner_type: str = "LINEAR", mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Универсальная функция для применения всех трансформаций.

    Применяет трансформации в правильном порядке:
    1. Базовые трансформации инстансов (Instance Rotation, Instance Scale)
    2. Случайные трансформации (только для Linear клонера)
    3. Глобальные трансформации

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов
        group_in: Входной узел группы
        cloner_type: Тип клонера (LINEAR, GRID, CIRCLE)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Финальный узел с примененными трансформациями
    """
    try:
        current_node = instance_node
        current_socket = 'Instances'

        # 1. Применяем базовые и случайные трансформации
        current_node = apply_instance_and_random_transforms(nodes, links, current_node, group_in, cloner_type, mode)
        current_socket = 'Instances'

        # 2. Для всех типов клонеров применяем глобальные трансформации
        transform = apply_global_transforms(nodes, links, current_node, current_socket, group_in, mode)
        if transform:
            current_node = transform
            current_socket = 'Geometry'

        return current_node
    except Exception as e:
        print(f"Ошибка при применении унифицированных трансформаций ({mode}): {e}")
        return instance_node
