"""
Управление цепочками клонеров коллекций.

Этот модуль отвечает за:
- Поиск предыдущих клонеров в цепочке коллекций
- Настройку свойств цепочки клонеров коллекций
- Регистрацию обновлений цепочки коллекций
- Управление связями между клонерами коллекций
"""

import bpy

def find_previous_cloner(target_collection):
    """
    Находит предыдущий клонер в цепочке коллекций.

    Args:
        target_collection: Целевая коллекция

    Returns:
        Object: Объект предыдущего клонера или None
    """
    try:
        # Проверяем, является ли целевая коллекция коллекцией клонера
        is_cloner_collection = target_collection.name.startswith("cloner_")
        previous_cloner_object = None

        if is_cloner_collection:
            # Сначала проверим, хранится ли информация о клонере в самой коллекции
            if hasattr(target_collection, "get") and target_collection.get("cloner_obj") and target_collection.get("cloner_obj") in bpy.data.objects:
                # Если есть прямая ссылка на объект клонер в коллекции
                previous_cloner_object = bpy.data.objects[target_collection.get("cloner_obj")]
            else:
                # Если нет прямой ссылки, ищем объект клонера методом перебора
                for obj in bpy.data.objects:
                    if (obj.name.startswith("Cloner_") and
                        hasattr(obj, "modifiers") and
                        any(mod.type == 'NODES' and
                            (mod.get("cloner_collection") == target_collection.name or
                             mod.get("original_collection") == target_collection.name)
                            for mod in obj.modifiers)):
                        previous_cloner_object = obj
                        break

        return previous_cloner_object
    except Exception as e:
        print(f"Ошибка при поиске предыдущего клонера: {e}")
        return None

def setup_collection_chain_properties(modifier, target_collection, cloner_obj, previous_cloner_object=None, cloner_collection=None):
    """
    Настраивает свойства цепочки клонеров коллекций.


    Args:
        modifier: Модификатор клонера
        target_collection: Целевая коллекция
        cloner_obj: Объект клонера
        previous_cloner_object: Предыдущий клонер в цепочке
        cloner_collection: Коллекция клонера (опционально)

    Returns:
        bool: True если настройка прошла успешно
    """
    try:
        # Проверяем, является ли целевая коллекция коллекцией клонера
        is_cloner_collection = target_collection.name.startswith("cloner_")

        # Устанавливаем флаги для цепочки клонеров
        if is_cloner_collection:
            modifier["is_collection_chain"] = True
            modifier["is_chained_cloner"] = True
            modifier["chain_source_collection"] = target_collection.name

            if previous_cloner_object:
                modifier["previous_cloner_object"] = previous_cloner_object.name

                # Устанавливаем связь в коллекции клонера если она передана
                if cloner_collection:
                    cloner_collection["parent_cloner_collection"] = target_collection.name
        else:
            # Сохраняем ссылку на источник
            modifier["chain_source_collection"] = target_collection.name

        # Инициализируем список следующих клонеров
        modifier["next_cloners"] = []

        return True
    except Exception as e:
        print(f"Ошибка при настройке свойств цепочки: {e}")
        return False

def register_collection_chain_update(previous_cloner_object, cloner_obj):
    """
    Регистрирует обновление цепочки клонеров коллекций.

    Args:
        previous_cloner_object: Предыдущий клонер в цепочке
        cloner_obj: Текущий клонер

    Returns:
        bool: True если регистрация прошла успешно
    """
    try:
        from ....common.utils import register_chain_update

        # Регистрируем отложенное обновление цепочки
        if hasattr(bpy.app, "timers"):
            bpy.app.timers.register(lambda: register_chain_update(previous_cloner_object, cloner_obj), first_interval=0.2)

        return True
    except Exception as e:
        print(f"Ошибка при регистрации обновления цепочки: {e}")
        return False


