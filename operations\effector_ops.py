"""
Operators for creating and managing effectors.
"""

import bpy
from bpy.types import Operator
from bpy.props import Enum<PERSON>roperty, StringProperty, BoolProperty

# Импортируем универсальную фабрику и константы
from ..core.factories.universal_factory import universal_factory
from ..core.registry import component_registry
from ..models.effectors import EFFECTOR_TYPES
from ..core.parameters import get_component_parameters, set_values_from_parameters

class EFFECTOR_OT_create_effector(bpy.types.Operator):
    """Create a new effector"""
    bl_idname = "object.create_effector"
    bl_label = "Create Effector"
    bl_options = {'REGISTER', 'UNDO'}

    effector_type: bpy.props.StringProperty(default="RANDOM")
    use_custom_group: bpy.props.BoolProperty(default=True,
                                          name="Use Custom Group",
                                          description="Create the effector in a custom node group")

    def execute(self, context):
        if not context.active_object:
            self.report({'ERROR'}, "Please select an object")
            return {'CANCELLED'}

        obj = context.active_object

        # Проверяем, есть ли уже клонеры на активном объекте или в сцене
        has_cloner = False

        # Отладочная информация
        from ..core.registry import component_registry
        cloner_group_names = component_registry.get_cloner_group_names()
        print(f"[DEBUG] Searching for cloners. Cloner group names = {cloner_group_names}")

        # Функция для определения является ли группа узлов клонером
        def is_cloner_node_group(node_group_name):
            # Получаем префиксы клонеров из реестра
            from ..core.registry import component_registry
            cloner_group_names = component_registry.get_cloner_group_names()
            cloner_prefixes = list(cloner_group_names.values())

            # Проверяем, начинается ли имя группы узлов с префикса клонера
            for prefix in cloner_prefixes:
                if node_group_name.startswith(prefix):
                    return True
            return False

        # Проверяем модификаторы активного объекта на наличие клонеров
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                if is_cloner_node_group(mod.node_group.name):
                    has_cloner = True
                    break

        # Если нет клонеров ни на текущем объекте, ни в сцене вообще, показываем сообщение
        if not has_cloner:
            self.report({'WARNING'}, "This object has no cloners. Effectors only work with cloners.")

        # Создаем группу узлов для эффектора
        try:
            # Создаем эффектор через универсальную фабрику
            node_group = universal_factory.create_effector(self.effector_type)
            if not node_group:
                self.report({'ERROR'}, f"Could not create {self.effector_type} effector node group")
                return {'CANCELLED'}

            # Создаем модификатор для эффектора
            # Получаем имя модификатора
            effector_mod_names = component_registry.get_effector_mod_names()
            mod_name = effector_mod_names.get(self.effector_type, f"{self.effector_type} Effector")

            # Создаем модификатор
            modifier = obj.modifiers.new(name=mod_name, type='NODES')
            modifier.node_group = node_group

            # Временно отключаем эффектор и устанавливаем нулевые параметры
            for socket in node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    try:
                        if socket.name == "Enable":
                            modifier[socket.identifier] = False
                        elif socket.name == "Position" or socket.name == "Rotation" or socket.name == "Scale":
                            # Устанавливаем нулевые значения для трансформаций
                            modifier[socket.identifier] = (0.0, 0.0, 0.0)
                    except:
                        pass
                        
            # Отключаем модификатор в стеке модификаторов
            modifier.show_viewport = False

            # Устанавливаем параметры эффектора через параметрическую систему
            effector_params = get_component_parameters('EFFECTOR', self.effector_type)
            if effector_params:
                set_values_from_parameters(modifier, effector_params)
                print(f"✅ Установлены параметры для {self.effector_type} эффектора через параметрическую систему")
            else:
                print(f"❌ Не найдены параметры для {self.effector_type} эффектора")

            # Оставляем эффектор выключенным до привязки к клонеру
            # Модификатор уже выключен и параметры установлены ранее
        except Exception as e:
            self.report({'ERROR'}, f"Error creating effector: {e}")
            return {'CANCELLED'}

        # Если эффектор создан без ошибок, возвращаем SUCCESS
        self.report({'INFO'}, f"Created {self.effector_type} effector")
        print(f"✅ Created {self.effector_type} effector")

        # Если нет клонеров, предупреждаем пользователя, что эффектор не будет работать без клонера
        if not has_cloner:
            self.report({'WARNING'}, "Add a cloner to see the effect of this effector")
            print("⚠️ Add a cloner to see the effect of this effector")

        # Если нет клонеров, но при этом не было ошибок создания эффектора, все равно возвращаем SUCCESS
        return {'FINISHED'}


class EFFECTOR_OT_connect_to_cloner(bpy.types.Operator):
    """Connect the selected effector to the active cloner"""
    bl_idname = "object.connect_effector_to_cloner"
    bl_label = "Connect Effector to Cloner"
    bl_options = {'REGISTER', 'UNDO'}

    effector_modifier_name: bpy.props.StringProperty()
    cloner_modifier_name: bpy.props.StringProperty()

    def execute(self, context):
        obj = context.active_object
        if not obj:
            self.report({'ERROR'}, "No active object")
            return {'CANCELLED'}

        # Проверяем, что оба модификатора существуют
        if self.effector_modifier_name not in obj.modifiers or self.cloner_modifier_name not in obj.modifiers:
            self.report({'ERROR'}, "One or both modifiers not found")
            return {'CANCELLED'}

        effector_mod = obj.modifiers[self.effector_modifier_name]
        cloner_mod = obj.modifiers[self.cloner_modifier_name]

        # Проверяем, что оба модификатора - это node groups
        if effector_mod.type != 'NODES' or cloner_mod.type != 'NODES':
            self.report({'ERROR'}, "One or both modifiers are not node groups")
            return {'CANCELLED'}

        # Проверяем, что node groups существуют
        if not effector_mod.node_group or not cloner_mod.node_group:
            self.report({'ERROR'}, "One or both node groups not found")
            return {'CANCELLED'}

        # Получаем имена групп узлов
        effector_group_name = effector_mod.node_group.name
        cloner_group_name = cloner_mod.node_group.name

        # Проверяем, что у эффектора есть сокет Enable
        enable_socket_id = None
        for socket in effector_mod.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == "Enable":
                enable_socket_id = socket.identifier
                break

        if enable_socket_id:
            # Включаем эффектор
            effector_mod[enable_socket_id] = True
            self.report({'INFO'}, f"Connected {self.effector_modifier_name} to {self.cloner_modifier_name}")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, f"No Enable socket found in {self.effector_modifier_name}")
            return {'CANCELLED'}


class EFFECTOR_OT_save_preset(bpy.types.Operator):
    """Save the current effector settings as a preset"""
    bl_idname = "object.save_effector_preset"
    bl_label = "Save Effector Preset"
    bl_options = {'REGISTER', 'UNDO'}

    modifier_name: bpy.props.StringProperty()
    preset_name: bpy.props.StringProperty(
        name="Preset Name",
        description="Name for the saved preset",
        default="My Preset"
    )

    def execute(self, context):
        obj = context.active_object
        if not obj or self.modifier_name not in obj.modifiers:
            self.report({'ERROR'}, "Modifier not found")
            return {'CANCELLED'}

        modifier = obj.modifiers[self.modifier_name]

        # Здесь код для сохранения пресета
        self.report({'INFO'}, f"Preset '{self.preset_name}' saved successfully.")
        return {'FINISHED'}


class EFFECTOR_OT_delete_effector(bpy.types.Operator):
    """Delete this effector"""
    bl_idname = "object.delete_effector"
    bl_label = "Delete Effector"
    bl_options = {'REGISTER', 'UNDO'}

    modifier_name: bpy.props.StringProperty()

    def execute(self, context):
        obj = context.active_object
        if obj and self.modifier_name in obj.modifiers:
            modifier = obj.modifiers[self.modifier_name]
            node_group = modifier.node_group

            # Удаляем модификатор
            obj.modifiers.remove(modifier)

            # Удаляем группу узлов, если она больше не используется
            if node_group and node_group.users == 0:
                bpy.data.node_groups.remove(node_group)

        return {'FINISHED'}


class EFFECTOR_OT_move_modifier(bpy.types.Operator):
    """Move modifier up or down"""
    bl_idname = "object.move_effector"
    bl_label = "Move Effector"
    bl_options = {'REGISTER', 'UNDO'}

    modifier_name: bpy.props.StringProperty()
    direction: bpy.props.EnumProperty(
        items=[
            ('UP', 'Up', 'Move up'),
            ('DOWN', 'Down', 'Move down')
        ]
    )

    def execute(self, context):
        obj = context.active_object
        if obj and self.modifier_name in obj.modifiers:
            if self.direction == 'UP':
                bpy.ops.object.modifier_move_up(modifier=self.modifier_name)
            else:
                bpy.ops.object.modifier_move_down(modifier=self.modifier_name)
        return {'FINISHED'}

# Список классов для регистрации
classes = (
    EFFECTOR_OT_create_effector,
    EFFECTOR_OT_connect_to_cloner,
    EFFECTOR_OT_save_preset,
    EFFECTOR_OT_delete_effector,
    EFFECTOR_OT_move_modifier
)

# Функции регистрации и отмены регистрации
def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)