"""
Базовый класс для построения узлов клонеров.

Содержит общую логику, которая используется всеми типами клонеров:
- Настройка интерфейса
- Создание базовых узлов
- Настройка анти-рекурсии
- Общие трансформации
"""

import bpy
from ..transforms.anti_recursion import (
    setup_anti_recursion_interface,
    create_effector_input_node,
    create_anti_recursion_nodes,
    get_anti_recursion_setting
)

class BaseNodeBuilder:
    """
    Базовый класс для построения узлов клонеров.

    Предоставляет общую функциональность для всех типов клонеров.
    """

    def __init__(self, node_group, orig_obj, cloner_type):
        """
        Инициализация построителя узлов.

        Args:
            node_group: Группа узлов для настройки
            orig_obj: Исходный объект для клонирования
            cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        """
        self.node_group = node_group
        self.orig_obj = orig_obj
        self.cloner_type = cloner_type
        self.nodes = node_group.nodes
        self.links = node_group.links

        # Узлы, которые будут созданы
        self.group_in = None
        self.group_out = None
        self.object_info = None

    def setup_interface(self):
        """
        Настраивает интерфейс node группы.

        Returns:
            bool: True если настройка прошла успешно
        """
        try:
            # Настраиваем интерфейс node группы
            self.node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

            # Настраиваем интерфейс анти-рекурсии
            use_anti_recursion = get_anti_recursion_setting()
            setup_anti_recursion_interface(self.node_group, use_anti_recursion)

            return True
        except Exception as e:
            print(f"Ошибка при настройке интерфейса: {e}")
            return False

    def create_basic_nodes(self):
        """
        Создает базовые узлы (Group Input/Output, Object Info).

        Returns:
            bool: True если создание прошло успешно
        """
        try:
            # Группы ввода/вывода
            self.group_in = self.nodes.new('NodeGroupInput')
            self.group_out = self.nodes.new('NodeGroupOutput')
            self.group_in.location = (-800, 0)
            self.group_out.location = (800, 0)

            # Узел для получения инстансов объекта
            self.object_info = self.nodes.new('GeometryNodeObjectInfo')
            self.object_info.transform_space = 'RELATIVE'
            if hasattr(self.object_info, 'instance_mode'):
                self.object_info.instance_mode = True
            self.object_info.location = (-600, 200)

            # Устанавливаем оригинальный объект как источник
            self.object_info.inputs['Object'].default_value = self.orig_obj

            # Определяем сокет вывода для разных версий Blender
            self.output_socket = 'Instances' if 'Instances' in self.object_info.outputs else 'Geometry'

            return True
        except Exception as e:
            print(f"Ошибка при создании базовых узлов: {e}")
            return False

    def setup_anti_recursion(self, output_node, output_socket_name):
        """
        Настраивает анти-рекурсию.

        Args:
            output_node: Выходной узел
            output_socket_name: Имя выходного сокета

        Returns:
            bool: True если настройка прошла успешно
        """
        try:
            # Создаем узлы анти-рекурсии и эффекторов
            use_anti_recursion = get_anti_recursion_setting()

            # Создаем узел для эффекторов
            create_effector_input_node(self.nodes, output_node)

            # Создаем узлы анти-рекурсии
            create_anti_recursion_nodes(self.nodes, self.links, output_node, output_socket_name,
                                      self.group_in, self.group_out, use_anti_recursion)

            return True
        except Exception as e:
            print(f"Ошибка при настройке анти-рекурсии: {e}")
            return False

    def setup_common_sockets(self):
        """
        Настраивает общие сокеты для всех клонеров.

        Returns:
            bool: True если настройка прошла успешно
        """
        try:
            # Добавляем общие сокеты
            rotation_socket = self.node_group.interface.new_socket("Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
            rotation_socket.default_value = (0.0, 0.0, 0.0)
            rotation_socket.subtype = 'EULER'

            scale_socket = self.node_group.interface.new_socket("Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')
            scale_socket.default_value = (1.0, 1.0, 1.0)

            # Global transform
            global_pos_socket = self.node_group.interface.new_socket("Global Position", in_out='INPUT', socket_type='NodeSocketVector')
            global_pos_socket.default_value = (0.0, 0.0, 0.0)

            global_rot_socket = self.node_group.interface.new_socket("Global Rotation", in_out='INPUT', socket_type='NodeSocketVector')
            global_rot_socket.default_value = (0.0, 0.0, 0.0)
            global_rot_socket.subtype = 'EULER'

            # Random parameters
            random_seed_socket = self.node_group.interface.new_socket("Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
            random_seed_socket.default_value = 0

            random_pos_socket = self.node_group.interface.new_socket("Random Position", in_out='INPUT', socket_type='NodeSocketVector')
            random_pos_socket.default_value = (0.0, 0.0, 0.0)

            random_rot_socket = self.node_group.interface.new_socket("Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
            random_rot_socket.default_value = (0.0, 0.0, 0.0)

            random_scale_socket = self.node_group.interface.new_socket("Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
            random_scale_socket.default_value = 0.0

            # Extra options
            center_grid_socket = self.node_group.interface.new_socket("Center Grid", in_out='INPUT', socket_type='NodeSocketBool')
            center_grid_socket.default_value = True

            pick_instance_socket = self.node_group.interface.new_socket("Pick Random Instance", in_out='INPUT', socket_type='NodeSocketBool')
            pick_instance_socket.default_value = False

            # Настраиваем интерфейс анти-рекурсии (как в оригинальном object_cloner.py строки 426-428)
            use_anti_recursion = get_anti_recursion_setting()
            setup_anti_recursion_interface(self.node_group, use_anti_recursion)

            return True
        except Exception as e:
            print(f"Ошибка при настройке общих сокетов: {e}")
            return False

    def build(self):
        """
        Основной метод построения узлов.

        Вызывает все необходимые методы в правильном порядке.
        Этот метод должен быть переопределен в дочерних классах.

        Returns:
            bool: True если построение прошло успешно
        """
        try:
            # Базовая последовательность построения
            if not self.setup_interface():
                return False

            if not self.create_basic_nodes():
                return False

            if not self.setup_common_sockets():
                return False

            return True
        except Exception as e:
            print(f"Ошибка при построении узлов: {e}")
            return False
