"""
Построители узлов для клонеров.

Содержит специализированные построители узлов для различных типов клонеров.

Содержит:
- base_builder.py: Базовый класс для построителей
- circle_builder.py: Построитель для Circle клонеров
- grid_builder.py: Построитель для Grid клонеров
- linear_builder.py: Построитель для Linear клонеров

Примечания:
- Построители для стековых клонеров (stacked_*_builder.py) удалены,
  так как их логика интегрирована в stacked_factory.py
- Папка object_utils удалена для упрощения структуры
- object_anti_recursion.py объединен с common/transforms/anti_recursion.py
  для устранения дублирования логики анти-рекурсии
"""

from .base_builder import BaseNodeBuilder

def create_node_builder(cloner_type, node_group, orig_obj):
    """
    Создает построитель узлов для указанного типа клонера.

    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        node_group: Группа узлов
        orig_obj: Исходный объект

    Returns:
        BaseNodeBuilder: Экземпляр построителя или None
    """
    try:
        # Импортируем реестр для проверки доступных типов
        from ....core.registry import component_registry

        # Проверяем, что тип клонера зарегистрирован
        if cloner_type not in component_registry._cloners:
            print(f"Неизвестный тип клонера: {cloner_type}")
            return None

        # Создаем построитель в зависимости от типа
        if cloner_type == "GRID":
            from .grid_builder import GridNodeBuilder
            return GridNodeBuilder(node_group, orig_obj)
        elif cloner_type == "LINEAR":
            from .linear_builder import LinearNodeBuilder
            return LinearNodeBuilder(node_group, orig_obj)
        elif cloner_type == "CIRCLE":
            from .circle_builder import CircleNodeBuilder
            return CircleNodeBuilder(node_group, orig_obj)
        else:
            print(f"Построитель для типа {cloner_type} не реализован")
            return None

    except Exception as e:
        print(f"Ошибка при создании построителя узлов: {e}")
        return None

def build_node_structure(cloner_type, node_group, orig_obj):
    """
    Создает и строит структуру узлов для клонера.

    Удобная функция, которая создает построитель и сразу строит узлы.

    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        node_group: Группа узлов
        orig_obj: Исходный объект

    Returns:
        bool: True если построение прошло успешно
    """
    try:
        builder = create_node_builder(cloner_type, node_group, orig_obj)
        if not builder:
            return False

        return builder.build()
    except Exception as e:
        print(f"Ошибка при построении структуры узлов: {e}")
        return False

__all__ = [
    'BaseNodeBuilder',
    'create_node_builder',
    'build_node_structure'
]

def register():
    """Register cloner node builders"""
    pass

def unregister():
    """Unregister cloner node builders"""
    pass
