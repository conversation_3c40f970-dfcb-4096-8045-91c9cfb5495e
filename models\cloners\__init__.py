"""
Cloner models for Advanced Cloners addon.
"""

# Импортируем все клонеры для автоматической регистрации через декораторы @register_cloner
from .grid import GridCloner  # noqa: F401
from .linear import LinearCloner  # noqa: F401
from .circle import CircleCloner  # noqa: F401
from .spiral import SpiralCloner  # noqa: F401
from .collection import (  # noqa: F401
    CollectionGridCloner,
    CollectionLinearCloner,
    CollectionCircleCloner
)
from .stacked import (  # noqa: F401
    StackedGridCloner,
    StackedLinearCloner,
    StackedCircleCloner
)

# Импортируем динамические константы
from ...core.common.dynamic_constants import (
    get_cloner_types,
    get_available_cloners,
    get_cloner_group_names,
    get_cloner_mod_names,
    get_cloner_node_group_prefixes,
    get_node_group_creators
)

# Экспорт функций из dynamic_constants для обратной совместимости
CLONER_TYPES = get_cloner_types
AVAILABLE_CLONERS = get_available_cloners
NODE_GROUP_CREATORS = get_node_group_creators
CLONER_GROUP_NAMES = get_cloner_group_names
CLONER_MOD_NAMES = get_cloner_mod_names
CLONER_NODE_GROUP_PREFIXES = get_cloner_node_group_prefixes




def register():
    """Register all cloner modules"""
    # Регистрация всех клонеров происходит через декораторы @register_cloner
    # Функции register() в подмодулях пустые, так как регистрация автоматическая
    pass


def unregister():
    """Unregister all cloner modules"""
    # Отмена регистрации всех клонеров происходит автоматически
    # Функции unregister() в подмодулях пустые
    pass