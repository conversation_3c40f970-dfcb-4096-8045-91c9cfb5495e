"""
Effector control elements for UI drawing.

This module provides UI drawing functions for effector controls,
including headers, expansion, and linking functionality.
"""

import bpy
from ...common.ui_utils import is_element_expanded


def get_effector_icon(modifier):
    """
    Возвращает иконку для эффектора на основе его типа.

    Args:
        modifier: Модификатор эффектора

    Returns:
        str: Имя иконки Blender
    """
    if not modifier or not modifier.node_group:
        return 'FORCE_FORCE'

    node_group_name = modifier.node_group.name

    if "RandomEffector" in node_group_name:
        return 'RNDCURVE'
    elif "NoiseEffector" in node_group_name:
        return 'FORCE_TURBULENCE'
    else:
        return 'FORCE_FORCE'


def get_linked_cloners(obj, effector_mod):
    """
    Находит все клонеры, связанные с эффектором.

    Args:
        obj: Объект с модификаторами
        effector_mod: Модификатор эффектора

    Returns:
        tuple: (regular_cloners, stacked_cloners)
    """
    regular_cloners = []
    stacked_cloners = []

    for cloner_mod in obj.modifiers:
        if (cloner_mod.type == 'NODES' and cloner_mod.node_group and
            cloner_mod.node_group.get("linked_effectors") is not None):

            linked_effectors = list(cloner_mod.node_group.get("linked_effectors", []))

            if effector_mod.name in linked_effectors:
                # Проверяем, является ли клонер стековым
                is_stacked = (cloner_mod.get("is_stacked_cloner") or
                             (cloner_mod.node_group and cloner_mod.node_group.get("is_stacked_cloner")))

                if is_stacked:
                    stacked_cloners.append(cloner_mod)
                else:
                    regular_cloners.append(cloner_mod)

    return regular_cloners, stacked_cloners


def get_available_cloners_for_linking(obj):
    """
    Возвращает список клонеров, к которым можно привязать эффектор.

    Args:
        obj: Объект с модификаторами

    Returns:
        list: Список модификаторов клонеров
    """
    cloner_mods = []

    for mod in obj.modifiers:
        if (mod.type == 'NODES' and mod.node_group and
            (mod.node_group.get("linked_effectors") is not None or
             mod.node_group.get("is_stacked_cloner") or
             mod.get("is_stacked_cloner"))):
            cloner_mods.append(mod)

    return cloner_mods


def draw_effector_ui(context, layout, obj, mod):
    """
    Отображает интерфейс для эффектора
    """
    # Создаем бокс для каждого эффектора
    box = layout.box()

    # Отображаем заголовок эффектора
    draw_effector_header(context, box, obj, mod)

    # Получаем состояние раскрытия
    expanded = is_element_expanded(context, obj.name, mod.name, "effector_expanded_states")

    # Если эффектор развернут, показываем его параметры
    if expanded and mod.node_group and hasattr(mod.node_group, 'interface'):
        draw_effector_expanded_ui(context, box, obj, mod)


def draw_effector_header(context, layout, obj, mod):
    """
    Отображает заголовок эффектора с элементами управления
    """
    row = layout.row()

    # Получаем иконку для эффектора
    icon = get_effector_icon(mod)

    # Получаем состояние раскрытия
    expanded = is_element_expanded(context, obj.name, mod.name, "effector_expanded_states")

    # Кнопка раскрытия/сворачивания
    op = row.operator("object.toggle_effector_expanded", text="",
                     icon='TRIA_DOWN' if expanded else 'TRIA_RIGHT', emboss=False)
    op.obj_name = obj.name
    op.modifier_name = mod.name

    # Название эффектора
    row.label(text=f"{mod.name}", icon=icon)

    # Кнопка видимости
    row.prop(mod, "show_viewport", text="",
            icon='HIDE_OFF' if mod.show_viewport else 'HIDE_ON', emboss=False)

    # Кнопки управления
    draw_effector_control_buttons(row, mod)


def draw_effector_control_buttons(layout, mod):
    """
    Отображает кнопки управления эффектором (перемещение, удаление)
    """
    ctrl_row = layout.row(align=True)

    # Кнопки перемещения вверх/вниз
    op = ctrl_row.operator("object.move_effector", text="", icon="TRIA_UP")
    op.modifier_name = mod.name
    op.direction = 'UP'

    op = ctrl_row.operator("object.move_effector", text="", icon="TRIA_DOWN")
    op.modifier_name = mod.name
    op.direction = 'DOWN'

    # Кнопка удаления
    op = ctrl_row.operator("object.delete_effector", text="", icon="X")
    op.modifier_name = mod.name


def draw_effector_expanded_ui(context, layout, obj, mod):
    """
    Отображает расширенный интерфейс для развернутого эффектора
    """
    # Отображаем связи с клонерами
    draw_effector_links(layout, obj, mod)

    # Отображаем параметры эффектора
    from .parameter_display import draw_effector_socket_parameters
    draw_effector_socket_parameters(layout, mod)


def draw_effector_links(layout, obj, mod):
    """
    Отображает информацию о связях эффектора с клонерами
    """
    # Находим связанные клонеры
    regular_cloners, stacked_cloners = get_linked_cloners(obj, mod)

    # Информация о связанных клонерах
    if regular_cloners or stacked_cloners:
        link_box = layout.box()
        row = link_box.row()
        row.label(text="Linked to:", icon='LINKED')

        # Список обычных клонеров
        if regular_cloners:
            links_text = ", ".join([cloner.name for cloner in regular_cloners])
            row = link_box.row()
            row.label(text=f"Regular: {links_text}")

        # Список стековых клонеров
        if stacked_cloners:
            links_text = ", ".join([cloner.name for cloner in stacked_cloners])
            row = link_box.row()
            row.label(text=f"Stacked: {links_text}")

    # Кнопка автопривязки для несвязанных эффекторов
    if not regular_cloners and not stacked_cloners:
        _draw_auto_link_button(layout, obj, mod)


def _draw_auto_link_button(layout, obj, mod):
    """
    Отображает кнопку автопривязки эффектора к клонерам
    """
    # Проверяем наличие клонеров, к которым можно привязаться
    cloner_mods = get_available_cloners_for_linking(obj)

    if cloner_mods:
        link_op = layout.operator("object.auto_link_effector", text="Link to Cloners", icon='LINKED')
        link_op.effector_name = mod.name


def draw_effector_visibility_toggle(layout, mod):
    """
    Отображает переключатель видимости эффектора
    """
    layout.prop(mod, "show_viewport", text="Visible",
               icon='HIDE_OFF' if mod.show_viewport else 'HIDE_ON')


def draw_effector_strength_control(layout, mod):
    """
    Отображает быстрый контроль силы эффектора
    """
    from ...common.ui_utils import display_socket_prop

    # Пытаемся найти параметр Strength
    if mod.node_group and hasattr(mod.node_group, 'interface'):
        for socket in mod.node_group.interface.items_tree:
            if (socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and
                socket.name == "Strength"):
                display_socket_prop(layout, mod, "Strength")
                break


def register():
    """Register effector controls components"""
    pass


def unregister():
    """Unregister effector controls components"""
    pass
