"""
Automatic interface builder for Blender Geometry Node groups.

This module provides functionality to automatically create node group interfaces
based on parameter definitions, eliminating the need for manual interface creation
in each component's interface_builder.py file.
"""

import bpy
from typing import List, Optional, Dict, Any
from .parameter_definition import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType
)


class InterfaceBuilder:
    """
    Builds Blender node group interfaces automatically from parameter definitions.

    This class takes parameter definitions and creates the corresponding
    input/output sockets in a Blender node group interface.
    """

    def __init__(self, node_group: bpy.types.GeometryNodeTree):
        """
        Initialize interface builder for a specific node group.

        Args:
            node_group: The Blender node group to build interface for
        """
        self.node_group = node_group
        self.created_sockets = {}  # Track created sockets by name

    def build_from_parameter_set(self, parameter_set: ComponentParameterSet) -> bool:
        """
        Build complete interface from a component parameter set.

        Args:
            parameter_set: Complete parameter set for the component

        Returns:
            bool: True if interface was built successfully
        """
        try:
            print(f"Building interface for {parameter_set.component_type}.{parameter_set.component_id}")

            # Sort groups by UI order
            sorted_groups = sorted(parameter_set.groups, key=lambda g: g.ui_order)

            # Build interface for each group
            for group in sorted_groups:
                if not self.build_from_parameter_group(group):
                    print(f"Failed to build interface for group: {group.name}")
                    return False

            print(f"Successfully built interface with {len(self.created_sockets)} sockets")

            # Create basic nodes (Group Input and Group Output) if they don't exist
            self._ensure_basic_nodes()

            return True

        except Exception as e:
            print(f"Error building interface from parameter set: {e}")
            return False

    def build_from_parameter_group(self, group: ParameterGroup) -> bool:
        """
        Build interface from a single parameter group.

        Args:
            group: Parameter group to build interface from

        Returns:
            bool: True if group interface was built successfully
        """
        try:
            print(f"Building interface for group: {group.name}")

            # Sort parameters by UI order
            sorted_params = sorted(group.parameters, key=lambda p: p.ui_order)

            # Create socket for each parameter
            for param in sorted_params:
                if not self.create_socket_from_parameter(param):
                    print(f"Failed to create socket for parameter: {param.name}")
                    return False

            return True

        except Exception as e:
            print(f"Error building interface from parameter group {group.name}: {e}")
            return False

    def create_socket_from_parameter(self, param: ParameterDefinition) -> bool:
        """
        Create a single socket from parameter definition.

        Args:
            param: Parameter definition to create socket from

        Returns:
            bool: True if socket was created successfully
        """
        try:
            # Определяем, является ли параметр критическим сокетом (Geometry)
            is_critical_socket = param.name == "Geometry" and param.param_type.value == "NodeSocketGeometry"
            
            # Пропускаем скрытые параметры, кроме критических сокетов
            if param.is_hidden and not is_critical_socket:
                print(f"Skipping hidden parameter: {param.name}")
                return True

            # Если это критический сокет, выводим специальное сообщение
            if is_critical_socket and param.is_hidden:
                print(f"Creating critical hidden socket: {param.name} - required for effector functionality")

            # Определяем направление сокета
            in_out = 'INPUT' if param.is_input else 'OUTPUT'
            
            # Используем составной ключ для отслеживания сокетов (имя + направление)
            socket_key = f"{param.name}_{in_out}"

            # Проверяем, существует ли уже такой сокет
            if socket_key in self.created_sockets:
                print(f"Socket {param.name} ({in_out}) already exists, skipping")
                return True

            # Создаем сокет
            socket = self.node_group.interface.new_socket(
                name=param.name,
                in_out=in_out,
                socket_type=param.get_blender_socket_type()
            )

            # Устанавливаем значение по умолчанию для входных сокетов
            if param.is_input and hasattr(socket, 'default_value') and param.default_value is not None:
                try:
                    socket.default_value = param.default_value
                except (TypeError, ValueError) as e:
                    print(f"Warning: Could not set default value for {param.name}: {e}")

            # Применяем ограничения и свойства
            param.apply_constraints_to_socket(socket)

            # Устанавливаем описание, если оно есть
            if param.description and hasattr(socket, 'description'):
                socket.description = param.description

            # Отслеживаем созданный сокет по составному ключу
            self.created_sockets[socket_key] = socket

            print(f"Created {in_out.lower()} socket: {param.name} ({param.get_blender_socket_type()})")
            return True

        except Exception as e:
            print(f"Error creating socket for parameter {param.name}: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_created_socket(self, name: str, in_out: str = None) -> Optional[Any]:
        """
        Get a socket that was created by this builder.

        Args:
            name: Name of the socket
            in_out: Optional direction ('INPUT' or 'OUTPUT'). If not provided, will try both.

        Returns:
            The socket object or None if not found
        """
        if in_out:
            # Если направление указано, используем составной ключ
            socket_key = f"{name}_{in_out}"
            return self.created_sockets.get(socket_key)
        else:
            # Если направление не указано, пробуем оба варианта
            input_key = f"{name}_INPUT"
            output_key = f"{name}_OUTPUT"
            
            # Сначала проверяем старый формат ключа (для обратной совместимости)
            if name in self.created_sockets:
                return self.created_sockets.get(name)
            
            # Затем пробуем входной и выходной сокеты
            if input_key in self.created_sockets:
                return self.created_sockets.get(input_key)
            elif output_key in self.created_sockets:
                return self.created_sockets.get(output_key)
            
            return None

    def get_created_socket_names(self) -> List[str]:
        """
        Get list of all socket names created by this builder.
        Returns only the base names without direction suffix.
        """
        socket_names = set()
        for key in self.created_sockets.keys():
            # Проверяем, есть ли в ключе подчеркивание (разделитель в составном ключе)
            if "_INPUT" in key or "_OUTPUT" in key:
                # Убираем суффикс направления
                base_name = key.split("_")[0]
                socket_names.add(base_name)
            else:
                # Старый формат ключа
                socket_names.add(key)
        
        return list(socket_names)

    def _ensure_basic_nodes(self) -> None:
        """
        Ensure that basic nodes (Group Input and Group Output) exist in the node group.
        This is required for the node group to function properly.
        """
        try:
            nodes = self.node_group.nodes

            # Check if Group Input exists
            group_input = None
            for node in nodes:
                if node.bl_idname == 'NodeGroupInput':
                    group_input = node
                    break

            if not group_input:
                group_input = nodes.new('NodeGroupInput')
                print("Created Group Input node")

            # Check if Group Output exists
            group_output = None
            for node in nodes:
                if node.bl_idname == 'NodeGroupOutput':
                    group_output = node
                    break

            if not group_output:
                group_output = nodes.new('NodeGroupOutput')
                print("Created Group Output node")

        except Exception as e:
            print(f"Error ensuring basic nodes: {e}")


def build_interface_from_parameters(
    node_group: bpy.types.GeometryNodeTree,
    parameter_set: ComponentParameterSet
) -> bool:
    """
    Convenience function to build interface from parameter set.

    Args:
        node_group: The node group to build interface for
        parameter_set: Parameter set defining the interface

    Returns:
        bool: True if interface was built successfully
    """
    builder = InterfaceBuilder(node_group)
    return builder.build_from_parameter_set(parameter_set)


def build_interface_from_groups(
    node_group: bpy.types.GeometryNodeTree,
    groups: List[ParameterGroup]
) -> bool:
    """
    Convenience function to build interface from parameter groups.

    Args:
        node_group: The node group to build interface for
        groups: List of parameter groups to build interface from

    Returns:
        bool: True if interface was built successfully
    """
    builder = InterfaceBuilder(node_group)

    # Sort groups by UI order
    sorted_groups = sorted(groups, key=lambda g: g.ui_order)

    # Build interface for each group
    for group in sorted_groups:
        if not builder.build_from_parameter_group(group):
            return False

    return True


def create_socket_from_definition(
    node_group: bpy.types.GeometryNodeTree,
    param: ParameterDefinition
) -> Optional[Any]:
    """
    Create a single socket from parameter definition.

    Args:
        node_group: The node group to create socket in
        param: Parameter definition

    Returns:
        The created socket or None if creation failed
    """
    builder = InterfaceBuilder(node_group)
    if builder.create_socket_from_parameter(param):
        return builder.get_created_socket(param.name)
    return None
