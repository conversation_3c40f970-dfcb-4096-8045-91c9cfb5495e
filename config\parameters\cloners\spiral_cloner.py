"""
Spiral Cloner Parameter Definitions

This module defines all parameters for the Spiral Cloner component using the new
unified parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

<PERSON>pi<PERSON> Clone<PERSON> creates instances in a spiral pattern with configurable radius, height, and turns.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    get_standard_parameter_set
)


# Basic Spiral Cloner parameters
SPIRAL_BASIC_GROUP = ParameterGroup(
    name="basic",
    description="Basic spiral cloner settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Count",
            param_type=ParameterType.INT,
            default_value=20,
            min_value=3,
            max_value=1000,
            description="Number of instances along the spiral",
            ui_group="Basic Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Radius",
            param_type=ParameterType.FLOAT,
            default_value=2.0,
            min_value=0.0,
            description="Radius of the spiral",
            ui_group="Basic Settings",
            ui_order=2
        ),
        ParameterDefinition(
            name="Height",
            param_type=ParameterType.FLOAT,
            default_value=5.0,
            description="Total height of the spiral",
            ui_group="Basic Settings",
            ui_order=3
        ),
        ParameterDefinition(
            name="Turns",
            param_type=ParameterType.FLOAT,
            default_value=3.0,
            min_value=0.1,
            description="Number of complete turns in the spiral",
            ui_group="Basic Settings",
            ui_order=4
        )
    ]
)

# Input/Output sockets - using standard cloner IO parameters
# SPIRAL_IO_GROUP removed - now using get_standard_parameter_set("cloner_io")

# Complete parameter set for Spiral Cloner
SPIRAL_CLONER_PARAMETERS = ComponentParameterSet(
    component_type="CLONER",
    component_id="SPIRAL",
    description="Spiral Cloner parameter set - creates instances in a spiral pattern",
    version="1.0",
    groups=[
        get_standard_parameter_set("cloner_io"),  # Using standard cloner IO parameters
        SPIRAL_BASIC_GROUP,
        get_standard_parameter_set("global_transform"),
        get_standard_parameter_set("instance"),
        get_standard_parameter_set("random"),
        get_standard_parameter_set("effector_control")
    ]
)
