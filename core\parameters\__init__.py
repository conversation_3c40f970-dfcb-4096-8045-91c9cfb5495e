"""
Unified Parameter System for Advanced Cloners

This module provides a centralized system for defining, creating, and managing
parameters for all component types (cloners, effectors, fields).

Key features:
- Declarative parameter definitions
- Automatic interface creation
- Automatic value setting
- Automatic UI generation
- Integration with existing systems
"""

from .parameter_definition import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    ParameterSubtype
)

from .parameter_registry import (
    parameter_registry,
    register_component_parameters,
    get_component_parameters
)

from .standard_sets import (
    STANDARD_GLOBAL_TRANSFORM,
    STANDARD_RANDOM,
    STANDARD_INSTANCE,
    get_standard_parameter_set
)

from .interface_builder import (
    InterfaceBuilder,
    build_interface_from_parameters,
    build_interface_from_groups,
    create_socket_from_definition
)

from .value_setter import (
    ValueSetter,
    set_values_from_parameters,
    set_values_from_groups,
    set_single_parameter_value
)

from .ui_generator import (
    UIGenerator,
    generate_ui_from_parameters,
    generate_ui_from_groups
)

from .auto_registration import (
    register_all_parameter_definitions,
    unregister_all_parameter_definitions
)

__all__ = [
    'ParameterDefinition',
    'ParameterGroup',
    'ComponentParameterSet',
    'ParameterType',
    'ParameterSubtype',
    'parameter_registry',
    'register_component_parameters',
    'get_component_parameters',
    'STANDARD_GLOBAL_TRANSFORM',
    'STANDARD_RANDOM',
    'STANDARD_INSTANCE',
    'get_standard_parameter_set',
    'InterfaceBuilder',
    'build_interface_from_parameters',
    'build_interface_from_groups',
    'create_socket_from_definition',
    'ValueSetter',
    'set_values_from_parameters',
    'set_values_from_groups',
    'set_single_parameter_value',
    'UIGenerator',
    'generate_ui_from_parameters',
    'generate_ui_from_groups',
    'register_all_parameter_definitions',
    'unregister_all_parameter_definitions'
]
