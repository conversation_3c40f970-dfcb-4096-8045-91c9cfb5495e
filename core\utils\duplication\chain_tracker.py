"""
Chain tracking utilities for cloner hierarchies.

This module handles tracking of cloner chains and hierarchies,
managing relationships between cloners and their duplicates.
"""

import bpy
from typing import Dict, List, Optional, Union
from .collection_manager import create_cloner_collection

# Dictionary to track cloner hierarchy chains
# Maps duplicate object name to original object with cloner modifiers
cloner_hierarchy_map = {}


def get_or_create_duplicate_for_cloner(obj, cloner_modifier):
    """
    Gets or creates a duplicate for use with a cloner.
    If the cloner modifier already uses a duplicate, returns it.

    Args:
        obj (bpy.types.Object): Original object with cloner modifier
        cloner_modifier (bpy.types.Modifier): Cloner modifier

    Returns:
        bpy.types.Object: Duplicate object for use with cloner
    """
    # Import here to avoid circular imports
    from . import get_mesh_duplicate

    # Check if modifier has duplicate information
    if "duplicate_obj" in cloner_modifier and cloner_modifier["duplicate_obj"] in bpy.data.objects:
        # Return existing duplicate
        return bpy.data.objects[cloner_modifier["duplicate_obj"]]

    # Create new collection name for duplicate
    collection_name = f"cloner_{cloner_modifier.name.replace(' ', '_').lower()}"

    # Check if collection already exists
    if "duplicate_collection" in cloner_modifier and cloner_modifier["duplicate_collection"] in bpy.data.collections:
        collection = bpy.data.collections[cloner_modifier["duplicate_collection"]]
    else:
        collection = create_cloner_collection(collection_name)
        cloner_modifier["duplicate_collection"] = collection.name

    # Проверяем специальный случай: клонер меша после клонера коллекции
    new_source_type = cloner_modifier.get("source_type", "OBJECT")

    # Если это первый модификатор объекта, просто создаем дубликат как обычно
    if len(obj.modifiers) <= 1:
        # Стандартное поведение - не скрываем оригинал, чтобы он был доступен для геометрии
        duplicate = get_mesh_duplicate(obj, collection, hide_original=False)
    else:
        # Проверяем, есть ли предыдущий клонер коллекции
        has_collection_cloner = False
        original_collection = None

        for mod in obj.modifiers:
            if mod != cloner_modifier and mod.type == 'NODES' and mod.node_group:
                # Если это клонер коллекции
                if mod.get("source_type", "") == "COLLECTION":
                    has_collection_cloner = True
                    # Пытаемся найти коллекцию
                    if "collection_to_clone" in bpy.context.scene:
                        original_collection = bpy.context.scene.collection_to_clone
                    break

        # Если переходим с клонера коллекции на клонер меша
        if has_collection_cloner and new_source_type == "OBJECT" and original_collection:
            # Вместо дубликата пустого объекта, создаем новый объект с геометрией
            print(f"Detected COLLECTION->OBJECT cloner chain. Creating mesh object for cloning.")

            # Получаем объект коллекции из имени
            coll_obj = None
            if original_collection in bpy.data.collections:
                coll_obj = bpy.data.collections[original_collection]

            # Создаем куб как стандартный объект по умолчанию
            bpy.ops.mesh.primitive_cube_add(size=1.0, location=(0, 0, 0))
            temp_obj = bpy.context.active_object

            # Создаем дубликат на основе этого объекта
            duplicate = get_mesh_duplicate(temp_obj, collection, hide_original=False)

            # Применяем преобразования оригинального объекта
            duplicate.matrix_world = obj.matrix_world.copy()

            # Удаляем временный объект
            mesh_data = temp_obj.data
            bpy.data.objects.remove(temp_obj)

            # Пробуем удалить меш, если он больше не используется
            if mesh_data.users == 0:
                bpy.data.meshes.remove(mesh_data)
        else:
            # Стандартное поведение если нет специального случая - не скрываем оригинал
            duplicate = get_mesh_duplicate(obj, collection, hide_original=False)

    # Save duplicate information in modifier
    cloner_modifier["duplicate_obj"] = duplicate.name

    # Also store the relationship in the duplicate object to original modifier
    duplicate["source_modifier"] = cloner_modifier.name
    duplicate["source_object"] = obj.name
    duplicate["source_type"] = new_source_type  # Сохраняем тип клонера

    # Track hierarchy relationship for cloner chain
    # Save both the original and the cloner modifier name for later access
    if obj.name not in cloner_hierarchy_map:
        cloner_hierarchy_map[obj.name] = []

    # Add this duplicate to the hierarchy chain
    cloner_hierarchy_map[obj.name].append({
        "duplicate": duplicate.name,
        "modifier": cloner_modifier.name,
        "source_type": new_source_type
    })

    # If this object is itself a duplicate created by a cloner, maintain the chain
    if "original_obj" in obj and obj["original_obj"] in bpy.data.objects:
        original_obj_name = obj["original_obj"]
        if original_obj_name in cloner_hierarchy_map:
            # Create entry for the new duplicate if it doesn't exist
            if duplicate.name not in cloner_hierarchy_map:
                cloner_hierarchy_map[duplicate.name] = []

            # Copy the chain from the parent (original) object
            cloner_hierarchy_map[duplicate.name] = cloner_hierarchy_map[original_obj_name].copy()

    return duplicate


def update_hierarchy_map_entry(obj_name, modifier_name, duplicate_name, source_type):
    """
    Updates or creates an entry in the hierarchy map.

    Args:
        obj_name (str): Name of the object with the cloner modifier
        modifier_name (str): Name of the cloner modifier
        duplicate_name (str): Name of the duplicate object
        source_type (str): Type of the cloner source
    """
    if obj_name not in cloner_hierarchy_map:
        cloner_hierarchy_map[obj_name] = []

    # Check if entry already exists
    for entry in cloner_hierarchy_map[obj_name]:
        if entry["modifier"] == modifier_name:
            entry["duplicate"] = duplicate_name
            entry["source_type"] = source_type
            return

    # Add new entry
    cloner_hierarchy_map[obj_name].append({
        "duplicate": duplicate_name,
        "modifier": modifier_name,
        "source_type": source_type
    })


def remove_hierarchy_map_entry(obj_name, modifier_name=None, duplicate_name=None):
    """
    Removes an entry from the hierarchy map.

    Args:
        obj_name (str): Name of the object
        modifier_name (str, optional): Name of the modifier to remove
        duplicate_name (str, optional): Name of the duplicate to remove
    """
    if obj_name not in cloner_hierarchy_map:
        return

    if modifier_name:
        cloner_hierarchy_map[obj_name] = [
            entry for entry in cloner_hierarchy_map[obj_name]
            if entry["modifier"] != modifier_name
        ]
    elif duplicate_name:
        cloner_hierarchy_map[obj_name] = [
            entry for entry in cloner_hierarchy_map[obj_name]
            if entry["duplicate"] != duplicate_name
        ]

    # Remove empty entries
    if not cloner_hierarchy_map[obj_name]:
        del cloner_hierarchy_map[obj_name]


def get_hierarchy_chain_for_object(obj_name):
    """
    Gets the hierarchy chain for a specific object.

    Args:
        obj_name (str): Name of the object

    Returns:
        list: List of hierarchy entries for the object
    """
    return cloner_hierarchy_map.get(obj_name, [])


def copy_hierarchy_chain(source_obj_name, target_obj_name):
    """
    Copies the hierarchy chain from one object to another.

    Args:
        source_obj_name (str): Name of the source object
        target_obj_name (str): Name of the target object
    """
    if source_obj_name in cloner_hierarchy_map:
        cloner_hierarchy_map[target_obj_name] = cloner_hierarchy_map[source_obj_name].copy()


def get_cloner_chain_for_object(obj):
    """
    Gets the complete chain of cloners that led to this object.

    Args:
        obj (bpy.types.Object): Object to get cloner chain for

    Returns:
        list: List of dictionaries with information about each cloner in the chain
              Each dict contains:
              - "object": The object with the cloner modifier
              - "modifier": The cloner modifier name
    """
    chain = []
    current_obj = obj
    processed_modifiers = set()  # Для предотвращения дублирования модификаторов в цепочке
    processed_collections = set()  # Для отслеживания уже обработанных коллекций

    # Special handling for collection cloners - check if this object might be a collection cloner
    # (usually it's a specially created empty with a specific name pattern)
    if obj.name.startswith("Cloner_") or "original_obj" in obj:
        # This is likely a collection or object cloner object
        for mod in obj.modifiers:
            # Пропускаем уже обработанные модификаторы
            mod_key = f"{obj.name}|{mod.name}"
            if mod_key in processed_modifiers:
                continue

            if (mod.type == 'NODES' and
                hasattr(mod, "node_group") and
                mod.node_group and
                ("CollectionCloner_" in mod.node_group.name or
                 "ObjectCloner_" in mod.node_group.name or
                "original_collection" in mod)):

                # Отмечаем модификатор как обработанный
                processed_modifiers.add(mod_key)

                # Определяем тип клонера
                is_collection = "CollectionCloner_" in mod.node_group.name or "original_collection" in mod

                # This is a collection cloner
                chain.append({
                    "object": obj.name,
                    "modifier": mod.name,
                    "is_collection_cloner": is_collection
                })

                # Добавляем информацию о коллекции в processed_collections, если это клонер коллекции
                if is_collection and "original_collection" in mod:
                    coll_name = mod["original_collection"]
                    processed_collections.add(coll_name)

                # Изменённая логика для цепочки клонеров - проверяем, является ли этот модификатор частью цепочки
                is_in_chain = ("is_collection_chain" in mod or "is_chained_cloner" in mod or "chain_source_collection" in mod)

                if is_in_chain:
                    # Пытаемся найти предыдущий объект в цепочке
                    prev_obj_name = None

                    # Предпочтительно используем "previous_cloner_object" если оно есть
                    if "previous_cloner_object" in mod:
                        prev_obj_name = mod["previous_cloner_object"]

                    # Если не нашли предыдущий объект, но есть информация об исходной коллекции,
                    # пытаемся найти объект клонера, создавший эту коллекцию
                    elif "chain_source_collection" in mod:
                        source_coll_name = mod["chain_source_collection"]

                        # Пропускаем уже обработанные коллекции для избежания циклов
                        if source_coll_name in processed_collections:
                            continue

                        processed_collections.add(source_coll_name)

                        # Ищем объект клонера, создавший эту коллекцию
                        for source_obj in bpy.data.objects:
                            if (source_obj.name.startswith("Cloner_") and
                                hasattr(source_obj, "modifiers")):
                                for source_mod in source_obj.modifiers:
                                    if (source_mod.type == 'NODES' and
                                        (source_mod.get("cloner_collection") == source_coll_name or
                                         source_mod.get("original_collection") == source_coll_name)):
                                        prev_obj_name = source_obj.name
                                        break
                                if prev_obj_name:
                                    break

                    # Process chain if previous object found
                    _process_cloner_chain(chain, prev_obj_name, processed_modifiers, processed_collections)

    # Process direct cloners and hierarchy chain
    _process_direct_cloners_and_hierarchy(chain, current_obj, processed_modifiers)

    # Reverse the chain to get it in creation order,
    # with the most recent additions at the end (our directly applied cloners)
    chain.reverse()
    return chain


def _process_cloner_chain(chain, prev_obj_name, processed_modifiers, processed_collections):
    """Helper function to process cloner chain recursively."""
    # Если нашли предыдущий объект, обрабатываем его
    while prev_obj_name and prev_obj_name in bpy.data.objects:
        prev_obj = bpy.data.objects[prev_obj_name]

        # Find the cloner modifier in this object
        for prev_mod in prev_obj.modifiers:
            prev_mod_key = f"{prev_obj.name}|{prev_mod.name}"
            if prev_mod_key in processed_modifiers:
                continue

            if (prev_mod.type == 'NODES' and
                hasattr(prev_mod, "node_group") and
                prev_mod.node_group and
                ("CollectionCloner_" in prev_mod.node_group.name or
                 "ObjectCloner_" in prev_mod.node_group.name or
                "original_collection" in prev_mod)):

                # Отмечаем модификатор как обработанный
                processed_modifiers.add(prev_mod_key)

                # Определяем тип клонера
                is_prev_collection = "CollectionCloner_" in prev_mod.node_group.name or "original_collection" in prev_mod

                # Add this cloner to the chain
                chain.append({
                    "object": prev_obj.name,
                    "modifier": prev_mod.name,
                    "is_collection_cloner": is_prev_collection
                })

                # Добавляем информацию о коллекции в processed_collections
                if is_prev_collection and "original_collection" in prev_mod:
                    coll_name = prev_mod["original_collection"]
                    processed_collections.add(coll_name)

                # Проверяем, есть ли у этого модификатора информация о предыдущем клонере
                is_prev_in_chain = ("is_collection_chain" in prev_mod or
                                  "is_chained_cloner" in prev_mod or
                                  "chain_source_collection" in prev_mod)

                # Получаем имя следующего объекта в цепочке
                next_prev_obj_name = None

                if is_prev_in_chain:
                    if "previous_cloner_object" in prev_mod:
                        next_prev_obj_name = prev_mod["previous_cloner_object"]
                    elif "chain_source_collection" in prev_mod:
                        source_coll_name = prev_mod["chain_source_collection"]

                        # Пропускаем уже обработанные коллекции для избежания циклов
                        if source_coll_name in processed_collections:
                            break

                        processed_collections.add(source_coll_name)

                        # Ищем объект клонера, создавший эту коллекцию
                        for source_obj in bpy.data.objects:
                            if (source_obj.name.startswith("Cloner_") and
                                hasattr(source_obj, "modifiers")):
                                for source_mod in source_obj.modifiers:
                                    if (source_mod.type == 'NODES' and
                                        (source_mod.get("cloner_collection") == source_coll_name or
                                         source_mod.get("original_collection") == source_coll_name)):
                                        next_prev_obj_name = source_obj.name
                                        break
                                if next_prev_obj_name:
                                    break

                # Продолжаем цепочку, если нашли следующий объект
                if next_prev_obj_name:
                    prev_obj_name = next_prev_obj_name
                    break
                else:
                    # Выходим из цикла, если не нашли следующий объект
                    prev_obj_name = None
                    break
            else:
                # Если не нашли подходящий модификатор, продолжаем поиск
                continue
        else:
            # Если не нашли ни одного подходящего модификатора в объекте, завершаем поиск
            prev_obj_name = None


def _process_direct_cloners_and_hierarchy(chain, current_obj, processed_modifiers):
    """Helper function to process direct cloners and hierarchy chain."""
    # First check for cloners applied directly to this object
    if current_obj.modifiers:
        for mod in current_obj.modifiers:
            # Пропускаем уже обработанные модификаторы
            mod_key = f"{current_obj.name}|{mod.name}"
            if mod_key in processed_modifiers:
                continue

            if (mod.type == 'NODES' and
                hasattr(mod, "node_group") and
                mod.node_group and
                ("is_chained_cloner" in mod or "chain_source_collection" in mod or
                 (mod.node_group and "ObjectCloner_" in mod.node_group.name))):

                # Отмечаем модификатор как обработанный
                processed_modifiers.add(mod_key)

                # This is a cloner applied directly to a clone
                chain.append({
                    "object": current_obj.name,
                    "modifier": mod.name,
                    "is_chained_cloner": True,
                    "is_collection_cloner": "original_collection" in mod
                })

    # Now traverse up the hierarchy chain for cloners that created duplicates
    while "original_obj" in current_obj:
        original_name = current_obj["original_obj"]
        if original_name in bpy.data.objects:
            original_obj = bpy.data.objects[original_name]

            # If we have direct source info, use it
            if "source_modifier" in current_obj and "source_object" in current_obj:
                source_mod_name = current_obj["source_modifier"]
                source_obj_name = current_obj["source_object"]

                # Verify that the source object and modifier still exist
                if source_obj_name in bpy.data.objects:
                    source_obj = bpy.data.objects[source_obj_name]
                    if source_mod_name in source_obj.modifiers:
                        # Пропускаем уже обработанные модификаторы
                        mod_key = f"{source_obj_name}|{source_mod_name}"
                        if mod_key not in processed_modifiers:
                            mod = source_obj.modifiers[source_mod_name]
                            is_collection = False

                            # Определяем тип клонера
                            if mod.node_group:
                                is_collection = "CollectionCloner_" in mod.node_group.name or "original_collection" in mod

                            processed_modifiers.add(mod_key)
                            chain.append({
                                "object": source_obj_name,
                                "modifier": source_mod_name,
                                "is_collection_cloner": is_collection
                            })

                        # Move up the chain to the source object
                        current_obj = source_obj
                        continue

            # Fallback: Find the modifier on the original object that created this duplicate
            found = False
            for mod in original_obj.modifiers:
                # Пропускаем уже обработанные модификаторы
                mod_key = f"{original_obj.name}|{mod.name}"
                if mod_key in processed_modifiers:
                    continue

                if mod.type == 'NODES' and hasattr(mod, "node_group") and mod.node_group:
                    if "duplicate_obj" in mod and mod["duplicate_obj"] == current_obj.name:
                        # Определяем тип клонера
                        is_collection = False
                        if mod.node_group:
                            is_collection = "CollectionCloner_" in mod.node_group.name or "original_collection" in mod

                        processed_modifiers.add(mod_key)
                        chain.append({
                            "object": original_obj.name,
                            "modifier": mod.name,
                            "is_collection_cloner": is_collection,
                            "is_chained_cloner": "is_chained_cloner" in mod or "chain_source_collection" in mod or
                                                (mod.node_group and "ObjectCloner_" in mod.node_group.name)
                        })
                        found = True
                        break

            # Move up the chain
            if not found:
                # Even if we don't find the exact modifier, we still want to continue up the chain
                print(f"Warning: Could not find modifier that created {current_obj.name}")

            current_obj = original_obj
        else:
            # Break if original no longer exists
            break


def register():
    """Register chain tracker components"""
    pass


def unregister():
    """Unregister chain tracker components"""
    pass
