"""
Connection management utilities for effector system.

This module handles safe creation and restoration of node connections
in the effector system, preventing self-connections and managing
anti-recursion structures.
"""

import bpy
from ...factories.universal_factory import UniversalComponentFactory
from ...registry import component_registry


def safe_link_new(links, from_socket, to_socket):
    """
    Безопасное создание связи с проверкой на самоподключение.

    Args:
        links: Коллекция связей node_group.links
        from_socket: Исходящий сокет
        to_socket: Входящий сокет

    Returns:
        bool: True если связь создана, False если заблокирована
    """
    # Проверяем, что сокеты принадлежат разным узлам
    if hasattr(from_socket, 'node') and hasattr(to_socket, 'node'):
        if from_socket.node == to_socket.node:
            print(f"[SAFE_LINK] Заблокировано самоподключение узла: {from_socket.node.name}")
            return False

        # Дополнительная проверка по имени (на случай если узлы разные объекты, но одинаковые)
        if hasattr(from_socket.node, 'name') and hasattr(to_socket.node, 'name'):
            if from_socket.node.name == to_socket.node.name:
                print(f"[SAFE_LINK] Заблокировано самоподключение по имени: {from_socket.node.name}")
                return False

    try:
        links.new(from_socket, to_socket)
        if hasattr(from_socket, 'node') and hasattr(to_socket, 'node'):
            print(f"[SAFE_LINK] Создана связь: {from_socket.node.name}.{from_socket.name} -> {to_socket.node.name}.{to_socket.name}")
        return True
    except Exception as e:
        print(f"[SAFE_LINK] Ошибка создания связи: {e}")
        return False


def get_cloner_template(cloner_type):
    """
    Получает эталонную структуру клонера заданного типа
    
    Args:
        cloner_type: Тип клонера (GRID, LINEAR, и т.д.)
    
    Returns:
        Эталонная нод-группа клонера или None, если не удалось создать
    """
    try:
        # Используем фабрику для создания эталонной структуры
        template = UniversalComponentFactory.create_cloner(cloner_type, use_custom_group=True)
        print(f"[DEBUG] Создан шаблон клонера типа {cloner_type}")
        return template
    except Exception as e:
        print(f"[DEBUG] Ошибка при создании шаблона клонера: {e}")
        return None


def find_output_connection(template_group):
    """
    Находит правильное соединение с выходным узлом в эталонном клонере
    
    Args:
        template_group: Эталонная группа узлов клонера
    
    Returns:
        Кортеж (node_name, output_name) для соединения с выходом
    """
    if not template_group:
        return None, None
        
    # Находим Group Output в шаблоне
    group_output = None
    for node in template_group.nodes:
        if node.type == 'GROUP_OUTPUT':
            group_output = node
            break
            
    if not group_output:
        return None, None
        
    # Находим связи, ведущие к Group Output
    for link in template_group.links:
        if link.to_node == group_output:
            from_node = link.from_node
            from_socket = link.from_socket
            return from_node.name, from_socket.name
            
    return None, None


def restore_direct_connection_improved(node_group):
    """
    Улучшенная функция восстановления прямых связей.
    Корректно работает с новой системой анти-рекурсии.
    Использует фабрику клонеров для получения эталонной структуры.

    Args:
        node_group: Группа узлов для восстановления связей
    """
    print("[DEBUG] Начинаем восстановление связей после удаления эффекторов")
    nodes = node_group.nodes
    links = node_group.links
    
    # Определяем тип клонера из метаданных
    cloner_type = node_group.get("component_id", None)
    if not cloner_type:
        # Если component_id не найден, пытаемся определить по имени
        if "Linear" in node_group.name:
            cloner_type = "LINEAR"
        elif "Grid" in node_group.name:
            cloner_type = "GRID"
        elif "Circle" in node_group.name:
            cloner_type = "CIRCLE"
        else:
            # Если не можем определить тип, используем по умолчанию
            cloner_type = "LINEAR"
    
    print(f"[DEBUG] Определен тип клонера: {cloner_type}")
    
    # Получаем эталонную структуру клонера
    template_group = get_cloner_template(cloner_type)
    if template_group:
        # Находим правильное соединение в шаблоне
        template_node_name, template_output_name = find_output_connection(template_group)
        print(f"[DEBUG] Найдено эталонное соединение: {template_node_name}.{template_output_name} -> Group Output")
        
        # Удаляем ненужный шаблон
        bpy.data.node_groups.remove(template_group)
    else:
        # Используем стандартные значения для клонеров
        template_node_name = "Realize Instances"
        template_output_name = "Geometry"

    # Находим Group Output в текущей группе
    group_output = None
    for node in nodes:
        if node.type == 'GROUP_OUTPUT':
            group_output = node
            break

    if not group_output:
        print("[DEBUG] Не найден выходной узел")
        return False

    # Находим вход Geometry у Group Output
    geometry_input = None
    for input in group_output.inputs:
        if input.name in ['Geometry', 'Output']:
            geometry_input = input
            break
    
    if not geometry_input:
        print("[DEBUG] Не найден Geometry вход у Group Output")
        return False

    # Удаляем все существующие связи с выходным узлом
    links_to_remove = [link for link in links if link.to_node == group_output]
    for link in links_to_remove:
        links.remove(link)
    
    # Сначала ищем узел с именем из шаблона
    # Проверяем точное совпадение имени
    match_node = nodes.get(template_node_name)
    if match_node:
        # Ищем выход по имени
        output_socket = None
        for output in match_node.outputs:
            if output.name == template_output_name:
                output_socket = output
                break
                
        if output_socket:
            # Создаем связь с выходным узлом
            links.new(output_socket, geometry_input)
            print(f"[DEBUG] Создана связь согласно шаблону: {match_node.name}.{output_socket.name} -> Group Output.{geometry_input.name}")
            return True
    
    # Если не нашли точное совпадение, ищем узлы, начинающиеся с имени шаблона
    for node in nodes:
        if node.name.startswith(template_node_name):
            # Ищем выход по имени
            for output in node.outputs:
                if output.name == template_output_name:
                    # Создаем связь с выходным узлом
                    links.new(output, geometry_input)
                    print(f"[DEBUG] Создана связь с похожим узлом: {node.name}.{output.name} -> Group Output.{geometry_input.name}")
                    return True
    
    # Если не нашли ничего по шаблону, ищем любой подходящий узел с геометрией
    print("[DEBUG] Попытка найти любой подходящий узел с геометрией")
    
    # Список узлов, которые не должны использоваться для соединения
    excluded_nodes = ["Object Info", "Collection Info", "Group Input"]
    
    # Список узлов в порядке приоритета
    priority_nodes = [
        "Realize Instances", "Join Geometry", "Instance on Points", 
        "Transform", "Mesh to Points", "Distribute Points on Faces"
    ]
    
    # Сначала ищем узлы в порядке приоритета
    for priority_node_name in priority_nodes:
        for node in nodes:
            if (node != group_output and 
                node.name.startswith(priority_node_name) and 
                node.name not in excluded_nodes):
                
                for output in node.outputs:
                    if output.type == 'GEOMETRY':
                        links.new(output, geometry_input)
                        print(f"[DEBUG] Создана связь с приоритетным узлом: {node.name}.{output.name} -> Group Output.{geometry_input.name}")
                        return True
    
    # Теперь ищем любой узел с геометрией, кроме исключенных
    for node in nodes:
        if (node != group_output and 
            not node.name.startswith('Effector_') and 
            node.name not in excluded_nodes and
            not any(node.name.startswith(prefix) for prefix in excluded_nodes)):
            
            for output in node.outputs:
                if output.type == 'GEOMETRY':
                    links.new(output, geometry_input)
                    print(f"[DEBUG] Создана запасная связь: {node.name}.{output.name} -> Group Output.{geometry_input.name}")
                    return True
    
    # Крайний случай - ищем Group Input и пытаемся его связать
    group_input = None
    for node in nodes:
        if node.type == 'GROUP_INPUT':
            group_input = node
            break
    
    if group_input:
        for output in group_input.outputs:
            if output.type == 'GEOMETRY':
                links.new(output, geometry_input)
                print(f"[DEBUG] Создана аварийная связь: Group Input -> Group Output")
                return True

    print("[DEBUG] Не удалось найти никакой узел для связи с выходом")
    return False


def restore_anti_recursion_connections(node_group, anti_recursion_switch):
    """
    Восстанавливает связи для системы с анти-рекурсией.

    Args:
        node_group: Группа узлов
        anti_recursion_switch: Узел Switch для анти-рекурсии
    """
    nodes = node_group.nodes
    links = node_group.links

    # Находим подходящий исходный узел
    source_candidates = []

    # Ищем узлы трансформации, инстансирования и другие подходящие
    for node in nodes:
        if (node.type != 'GROUP_OUTPUT' and node.type != 'GROUP_INPUT' and
            node != anti_recursion_switch and not node.name.startswith('Effector_')):

            # Проверяем типы узлов, которые обычно являются источниками
            if ('Transform' in node.bl_idname or
                'Instance' in node.bl_idname or
                'Scale' in node.bl_idname or
                'Rotate' in node.bl_idname or
                'Translate' in node.bl_idname):

                for output in node.outputs:
                    if output.name in ['Geometry', 'Instances']:
                        source_candidates.append((node, output))

    # Выбираем лучший кандидат (обычно последний в цепочке)
    if source_candidates:
        source_node, source_output = source_candidates[-1]

        print(f"[DEBUG] Выбран исходный узел: {source_node.name} с выходом {source_output.name}")

        # Удаляем существующие связи к False входу
        links_to_remove = [link for link in links
                          if (link.to_node == anti_recursion_switch and
                              link.to_socket == anti_recursion_switch.inputs[False])]
        for link in links_to_remove:
            links.remove(link)

        # Подключаем исходный узел к False входу
        links.new(source_output, anti_recursion_switch.inputs[False])

        print("[DEBUG] Восстановлены связи для анти-рекурсии")
    else:
        print("[DEBUG] Не найдены подходящие исходные узлы")


def restore_direct_output_connection(node_group, group_output):
    """
    Восстанавливает прямую связь с выходным узлом.

    Args:
        node_group: Группа узлов
        group_output: Выходной узел группы
    """
    nodes = node_group.nodes
    links = node_group.links

    # Ищем подходящий исходный узел
    for node in nodes:
        if (node.type != 'GROUP_OUTPUT' and node.type != 'GROUP_INPUT' and
            not node.name.startswith('Effector_')):

            for output in node.outputs:
                if output.name in ['Geometry', 'Instances']:
                    # Создаем прямую связь к выходу
                    links.new(output, group_output.inputs['Geometry'])
                    print(f"[DEBUG] Восстановлена прямая связь: {node.name}.{output.name} -> Output")
                    return

    print("[DEBUG] Не удалось найти узел для восстановления прямой связи")


def restore_connections_bypassing_realize(node_group, realize_connections):
    """
    Восстанавливает связи, минуя удаленные Realize узлы.

    Args:
        node_group: Группа узлов
        realize_connections: Список связей удаленных Realize узлов
    """
    links = node_group.links

    # Группируем связи по входам и выходам
    inputs_to_realize = []  # Что было подключено К Realize узлу
    outputs_from_realize = []  # Что было подключено ОТ Realize узла

    for connection in realize_connections:
        if len(connection) == 3:
            if hasattr(connection[2], 'node'):  # to_socket
                # Это входящая связь К Realize узлу
                from_node, from_socket, to_socket = connection
                inputs_to_realize.append((from_node, from_socket))
            else:
                # Это исходящая связь ОТ Realize узла
                from_socket, to_node, to_socket = connection
                outputs_from_realize.append((to_node, to_socket))

    # Соединяем входы напрямую с выходами, минуя Realize
    for from_node, from_socket in inputs_to_realize:
        for to_node, to_socket in outputs_from_realize:
            if safe_link_new(links, from_socket, to_socket):
                print(f"[DEBUG] Восстановлена связь, минуя Realize: {from_node.name} -> {to_node.name}")
            else:
                print(f"[DEBUG] Не удалось восстановить связь, минуя Realize")


# Алиас для обратной совместимости
restore_direct_connection = restore_direct_connection_improved
