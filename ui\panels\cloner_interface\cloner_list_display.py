"""
Модуль отображения списка клонеров.

Содержит функции для отображения и управления списком клонеров,
прикрепленных к выбранному объекту.
"""

import bpy
from ...common.ui_utils import is_element_expanded
from ...common.cloner_drawing.settings_panels import (
    draw_collection_cloner_settings
    # Removed unused imports: draw_grid_cloner_settings, draw_linear_cloner_settings, draw_circle_cloner_settings
    # All cloners now use the unified draw_collection_cloner_settings function
)
# Removed unused import: draw_common_cloner_settings
# from ...common.cloner_drawing.common_controls import draw_common_cloner_settings

from ....core.registry import component_registry


def draw_cloners_list(context, layout):
    """
    Отображает список всех клонеров, прикрепленных к активному объекту.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
    """
    obj = context.active_object

    cloner_count = 0
    cloners_found = []
    collection_cloners_found = []
    stacked_cloners_found = []

    # Check if there are any cloners to display
    for modifier in obj.modifiers:
        if not (modifier.type == 'NODES' and modifier.node_group):
            continue

        # Check if this is a cloner - modified logic to support custom groups
        is_cloner = False
        is_collection_cloner = False
        is_stacked_cloner = modifier.get("is_stacked_cloner", False)  # Добавляем проверку стековых клонеров
        node_group_name = modifier.node_group.name
        cloner_type = None

        # Проверяем тип клонера
        # Стековые клонеры
        if is_stacked_cloner:
            is_cloner = True
            cloner_type = modifier.get("cloner_type", "GRID")  # Получаем тип из свойства
            cloner_count += 1
            stacked_cloners_found.append((modifier.name, node_group_name, cloner_type))
        # Клонеры коллекций
        elif "CollectionCloner_" in node_group_name:
            is_cloner = True
            is_collection_cloner = True

            # Extract cloner type from the name (format: CollectionCloner_TYPE_name...)
            parts = node_group_name.split('_')
            if len(parts) > 1:
                cloner_type = parts[1]  # Extract type (GRID, LINEAR, CIRCLE)
                cloner_count += 1
                collection_cloners_found.append((modifier.name, node_group_name, cloner_type))
        # Проверка объектных клонеров
        elif "ObjectCloner_" in node_group_name:
            is_cloner = True
            is_collection_cloner = True  # используем тот же вид UI, что и для коллекций

            # Extract cloner type from the name (format: ObjectCloner_TYPE_name...)
            parts = node_group_name.split('_')
            if len(parts) > 1:
                cloner_type = parts[1]  # Extract type (GRID, LINEAR, CIRCLE)
                cloner_count += 1
                collection_cloners_found.append((modifier.name, node_group_name, cloner_type))
        else:
            # Check if node_group_name contains any of the cloner prefixes
            cloner_group_names = component_registry.get_cloner_group_names()
            for c_type, prefix in cloner_group_names.items():
                # Get the first part of the name (before any dots)
                parts = node_group_name.split('.')
                if parts[0] == prefix:
                    is_cloner = True
                    cloner_type = c_type
                    cloner_count += 1
                    # Treat standard cloners the same as collection cloners for UI purposes
                    is_collection_cloner = True
                    collection_cloners_found.append((modifier.name, node_group_name, cloner_type))
                    break

        if not is_cloner:
            continue

    # Show the cloners found if any
    if cloners_found or collection_cloners_found or stacked_cloners_found:
        layout.separator()
        layout.label(text=f"Cloners: {cloner_count}")

        # Draw each regular cloner
        for modifier_name, node_group_name, cloner_type in cloners_found:
            draw_cloner_box(context, layout, obj, modifier_name, node_group_name, cloner_type, False)

        # Draw each collection cloner
        for modifier_name, node_group_name, cloner_type in collection_cloners_found:
            draw_cloner_box(context, layout, obj, modifier_name, node_group_name, cloner_type, True)

        # Draw each stacked cloner
        for modifier_name, node_group_name, cloner_type in stacked_cloners_found:
            draw_stacked_cloner_box(context, layout, obj, modifier_name, node_group_name, cloner_type)


def draw_cloner_box(context, layout, obj, modifier_name, node_group_name, cloner_type, is_collection_cloner):
    """
    Отображает блок UI для отдельного клонера.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
        obj: Активный объект
        modifier_name: Имя модификатора клонера
        node_group_name: Имя группы узлов
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        is_collection_cloner: Является ли клонером коллекции
    """
    modifier = obj.modifiers.get(modifier_name)
    if not modifier:
        return

    # Get icon based on type
    icon = "MESH_GRID"  # default
    if cloner_type == "LINEAR":
        icon = "SORTSIZE"
    elif cloner_type == "CIRCLE":
        icon = "MESH_CIRCLE"

    # Create box for each cloner
    box = layout.box()
    row = box.row()

    # Get expanded state from our property system
    expanded = is_element_expanded(context, obj.name, modifier_name, "cloner_expanded_states")

    # Cloner label + expand button
    # First expansion button
    op = row.operator("object.toggle_cloner_expanded", text="", icon='TRIA_DOWN' if expanded else 'TRIA_RIGHT', emboss=False)
    op.obj_name = obj.name
    op.modifier_name = modifier_name

    # Then cloner name
    main_label = f"{modifier.name} [{node_group_name}]"
    if is_collection_cloner:
        if "CollectionCloner_" in node_group_name:
            main_label += " (Collection)"
        elif "ObjectCloner_" in node_group_name:
            main_label += " (Object)"
    row.label(text=main_label, icon=icon)

    # Visibility toggle
    row.prop(modifier, "show_viewport", text="", icon='HIDE_OFF' if modifier.show_viewport else 'HIDE_ON', emboss=False)

    # Up/Down/Delete buttons
    row = row.row(align=True)
    op = row.operator("object.move_cloner", text="", icon="TRIA_UP")
    op.modifier_name = modifier.name
    op.direction = 'UP'

    op = row.operator("object.move_cloner", text="", icon="TRIA_DOWN")
    op.modifier_name = modifier.name
    op.direction = 'DOWN'

    op = row.operator("object.delete_cloner", text="", icon="X")
    op.modifier_name = modifier.name

    # Draw expanded modifier properties
    if expanded:
        # Check if this is a standard object cloner (not a collection or object cloner)
        is_standard_object_cloner = not is_collection_cloner

        # Use unified settings function for all cloner types
        # All cloners now use the same draw_collection_cloner_settings function
        # which internally calls generate_ui_from_parameters() with the correct parameter set
        draw_collection_cloner_settings(box, modifier, cloner_type)

        # REMOVED: draw_common_cloner_settings() call to prevent parameter duplication
        # The new automatic UI generation system already includes all common parameters:
        # - Global Transform, Instance, Random, Effector Control
        # draw_common_cloner_settings(box, modifier, context)


def draw_stacked_cloner_box(context, layout, obj, modifier_name, node_group_name, cloner_type):
    """
    Отображает блок UI для стекового клонера.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
        obj: Активный объект
        modifier_name: Имя модификатора клонера
        node_group_name: Имя группы узлов
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
    """
    from ...common.ui_utils import get_stacked_cloner_info
    modifier = obj.modifiers.get(modifier_name)
    if not modifier:
        return

    # Получаем информацию о стековом клонере
    is_stacked, stacked_type = get_stacked_cloner_info(modifier)

    # Если тип определен в стековом клонере, используем его
    if is_stacked and stacked_type:
        cloner_type = stacked_type

    # Get icon based on type
    icon = "MESH_GRID"  # default
    if cloner_type == "LINEAR":
        icon = "SORTSIZE"
    elif cloner_type == "CIRCLE":
        icon = "MESH_CIRCLE"

    # Create box for each cloner
    box = layout.box()
    row = box.row()

    # Get expanded state from our property system
    expanded = is_element_expanded(context, obj.name, modifier_name, "cloner_expanded_states")

    # Cloner label + expand button
    # First expansion button
    op = row.operator("object.toggle_cloner_expanded", text="", icon='TRIA_DOWN' if expanded else 'TRIA_RIGHT', emboss=False)
    op.obj_name = obj.name
    op.modifier_name = modifier_name

    # Используем такой же формат названия, как у обычных клонеров
    main_label = f"{cloner_type.title()} Object Cloner"
    row.label(text=main_label, icon=icon)

    # Visibility toggle
    row.prop(modifier, "show_viewport", text="", icon='HIDE_OFF' if modifier.show_viewport else 'HIDE_ON', emboss=False)

    # Up/Down/Delete buttons
    row = row.row(align=True)
    op = row.operator("object.move_cloner", text="", icon="TRIA_UP")
    op.modifier_name = modifier.name
    op.direction = 'UP'

    op = row.operator("object.move_cloner", text="", icon="TRIA_DOWN")
    op.modifier_name = modifier.name
    op.direction = 'DOWN'

    op = row.operator("object.delete_cloner", text="", icon="X")
    op.modifier_name = modifier.name

    # Draw expanded modifier properties
    if expanded:
        # Стековые клонеры используют collection cloner settings для правильного отображения
        # The new system (generate_ui_from_parameters) already includes ALL parameters
        draw_collection_cloner_settings(box, modifier, cloner_type)

        # REMOVED: draw_common_cloner_settings() call to prevent parameter duplication
        # The new automatic UI generation system already includes all common parameters:
        # - Global Transform, Instance, Random, Effector Control
        # draw_common_cloner_settings(box, modifier, context)
