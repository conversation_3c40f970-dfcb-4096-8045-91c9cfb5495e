"""
Update and synchronization utilities for effector system.

This module handles the main update logic for cloners with effectors,
manages the synchronization between standard and stacked cloners,
and provides anti-recursion support.
"""

import bpy
from ....models.effectors import EFFECTOR_NODE_GROUP_PREFIXES
from .connection_management import restore_direct_connection_improved


def get_effector_modifiers(obj):
    """
    Получает список всех модификаторов-эффекторов на объекте.

    Args:
        obj: Объек<PERSON> Blender, для которого нужно найти эффекторы

    Returns:
        list: Список имен модификаторов-эффекторов
    """
    if not obj or not hasattr(obj, 'modifiers'):
        return []

    effector_mods = []

    for mod in obj.modifiers:
        # Проверка на Geometry Nodes модификатор с группой
        if mod.type == 'NODES' and mod.node_group:
            # Проверка на эффектор по префиксу имени группы
            from ...registry import component_registry
            effector_group_names = component_registry.get_effector_group_names()
            if any(group_name in mod.node_group.name for group_name in effector_group_names.values()):
                effector_mods.append(mod.name)
            # Дополнительная проверка для пользовательских эффекторов
            elif "Effector" in mod.node_group.name:
                effector_mods.append(mod.name)
            # Проверка на наличие флага эффектора в метаданных
            elif mod.get("is_effector", False):
                effector_mods.append(mod.name)

    return effector_mods


def update_cloner_with_effectors(obj, cloner_mod):
    """
    Улучшенная функция обновления клонера с эффекторами.
    Работает корректно с новой системой анти-рекурсии.

    Args:
        obj: Объект, содержащий модификатор
        cloner_mod: Модификатор клонера с нод-группой
    """
    if not cloner_mod or not cloner_mod.node_group:
        print("[DEBUG] update_cloner_with_effectors: Модификатор не имеет нод-группы")
        return

    # Проверяем, является ли клонер коллекционным
    is_collection_cloner = False
    if "source_type" in cloner_mod and cloner_mod["source_type"] == "COLLECTION":
        is_collection_cloner = True
    elif cloner_mod.node_group.get("is_collection_cloner", False):
        is_collection_cloner = True

    # Для клонеров коллекций НЕ применяем стандартную логику эффекторов
    if is_collection_cloner:
        print(f"[DEBUG] Пропускаем обновление клонера коллекции {cloner_mod.name} - эффекторы не поддерживаются")
        # Просто игнорируем обновление для клонеров коллекций
        # Это предотвратит нарушение их node group структуры
        return

    # Проверяем, является ли клонер стековым
    mod_is_stacked = cloner_mod.get("is_stacked_cloner", False)
    node_is_stacked = cloner_mod.node_group.get("is_stacked_cloner", False)
    is_stacked_cloner = mod_is_stacked or node_is_stacked

    # Для стековых клонеров используем существующую логику
    if is_stacked_cloner:
        print(f"[DEBUG] Обработка стекового клонера {cloner_mod.name}")
        # Импортируем здесь, чтобы избежать циклической зависимости
        from .stacked_cloner_integration import update_stacked_cloner_with_effectors
        return update_stacked_cloner_with_effectors(obj, cloner_mod)

    # Для обычных клонеров используем улучшенную логику
    return update_standard_cloner_with_effectors(obj, cloner_mod)


def update_standard_cloner_with_effectors(obj, cloner_mod):
    """
    Обновляет обычный (не стековый) клонер с эффекторами.
    Заменяет проблемные узлы анти-рекурсии на эффекторы.

    Args:
        obj: Объект с модификатором
        cloner_mod: Модификатор клонера
    """
    node_group = cloner_mod.node_group
    linked_effectors = node_group.get("linked_effectors", [])
    print(f"[DEBUG] Связанные эффекторы: {linked_effectors}")

    # Проверяем валидность списка эффекторов
    valid_linked_effectors = []
    for eff_name in linked_effectors:
        eff_mod = obj.modifiers.get(eff_name)
        if eff_mod and eff_mod.type == 'NODES' and eff_mod.node_group:
            # Проверяем, что это действительно эффектор
            is_effector = any(eff_mod.node_group.name.startswith(p) for p in EFFECTOR_NODE_GROUP_PREFIXES)
            if is_effector:
                valid_linked_effectors.append(eff_name)
                print(f"[DEBUG] Валидный эффектор: {eff_name}")

    # Обновляем список эффекторов
    if len(valid_linked_effectors) != len(linked_effectors):
        print(f"[DEBUG] Обновляем список эффекторов с {len(linked_effectors)} на {len(valid_linked_effectors)}")
        node_group["linked_effectors"] = valid_linked_effectors
        linked_effectors = valid_linked_effectors

    # Находим ключевые узлы
    nodes = node_group.nodes
    links = node_group.links

    group_output = None
    anti_recursion_switch = None
    final_realize_switch = None

    for node in nodes:
        if node.type == 'GROUP_OUTPUT':
            group_output = node
        elif node.name == "Anti-Recursion Switch":
            anti_recursion_switch = node
        elif node.name == "Final Realize Switch":
            final_realize_switch = node

    if not group_output:
        print("[DEBUG] Не найден выходной узел")
        return

    # Используем Final Realize Switch если он есть, иначе Anti-Recursion Switch
    target_switch = final_realize_switch if final_realize_switch else anti_recursion_switch

    # Если нет эффекторов, восстанавливаем прямые связи и Realize узел для анти-рекурсии
    if not linked_effectors:
        print("[DEBUG] Нет эффекторов, восстанавливаем прямые связи и Realize узел")
        # Удаляем старые узлы эффекторов
        old_effector_nodes = [n.name for n in nodes if n.name.startswith('Effector_')]
        print(f"[DEBUG] Старые узлы эффекторов для удаления: {old_effector_nodes}")
        
        for node_name in old_effector_nodes:
            if node_name in nodes:
                nodes.remove(nodes[node_name])
                print(f"[DEBUG] Удален старый узел эффектора: {node_name}")

        # Восстанавливаем Realize узел для анти-рекурсии, если его нет
        restore_realize_node_for_anti_recursion(node_group, target_switch)

        restore_direct_connection_improved(node_group)
        # Включаем видимость всех отвязанных эффекторов
        # Теперь old_effector_nodes это список строк, а не объектов
        for node_name in old_effector_nodes:
            eff_name = node_name.replace('Effector_', '')
            eff_mod = obj.modifiers.get(eff_name)
            if eff_mod:
                eff_mod.show_render = True
                print(f"[DEBUG] Включена видимость для эффектора: {eff_name}")
        return

    # Применяем эффекторы: импортируем здесь для избежания циклической зависимости
    from .effector_linking import replace_problematic_nodes_with_effectors
    replace_problematic_nodes_with_effectors(obj, node_group, linked_effectors)

    print("[DEBUG] Цепочка эффекторов создана успешно")


def restore_realize_node_for_anti_recursion(node_group, switch_node=None):
    """
    Восстанавливает Realize узел для анти-рекурсии, если его нет и есть Switch узел.
    ОБНОВЛЕНО: Поддерживает новую структуру с Final Realize Switch.

    Args:
        node_group: Группа узлов клонера
        switch_node: Узел Switch (может быть None, тогда ищем автоматически)
    """
    nodes = node_group.nodes
    links = node_group.links

    # Если Switch узел не передан, ищем его
    if not switch_node:
        for node in nodes:
            if node.name in ["Anti-Recursion Switch", "Final Realize Switch"]:
                switch_node = node
                break

    if not switch_node:
        print("[DEBUG] Switch узел не найден, Realize узел не нужен")
        return

    # Определяем тип Switch узла и соответствующий Realize узел
    is_final_realize_switch = switch_node.name == "Final Realize Switch"
    realize_node_name = "Final Realize Instances" if is_final_realize_switch else "Anti-Recursion Realize"

    # Проверяем, есть ли уже Realize узел
    realize_node = None
    for node in nodes:
        if node.name == realize_node_name:
            realize_node = node
            break

    if realize_node:
        print(f"[DEBUG] {realize_node_name} узел уже существует")
        return

    # Создаем новый Realize узел
    realize_node = nodes.new('GeometryNodeRealizeInstances')
    realize_node.name = realize_node_name
    realize_node.location = (switch_node.location.x - 150, switch_node.location.y + 100)

    # Находим что подключено к False входу Switch (обычно это прямой путь)
    false_input_source = None
    false_input_socket = None

    for link in links:
        if link.to_node == switch_node and link.to_socket.name == 'False':
            false_input_source = link.from_node
            false_input_socket = link.from_socket
            break

    if false_input_source and false_input_socket:
        # Импортируем функцию здесь
        from .connection_management import safe_link_new

        # Подключаем источник к Realize узлу
        safe_link_new(links, false_input_socket, realize_node.inputs['Geometry'])
        # Подключаем Realize узел к True входу Switch
        safe_link_new(links, realize_node.outputs['Geometry'], switch_node.inputs['True'])
        print(f"[DEBUG] Восстановлен {realize_node_name} узел для анти-рекурсии")
    else:
        print("[DEBUG] Не найден источник для подключения Realize узла")
