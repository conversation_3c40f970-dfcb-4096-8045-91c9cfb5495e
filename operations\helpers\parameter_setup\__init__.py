"""
Модуль настройки параметров.

UPDATED: Клонеры и эффекторы мигрированы на новую унифицированную систему параметров.
Используйте core.parameters.set_values_from_parameters() для новых проектов.

Содержит функции для настройки параметров различных компонентов:
- field_params: Параметры полей (Sphere) - пока legacy
"""

# Модуль effector_params был удален, так как эффекторы переведены на новую систему параметров

try:
    from .field_params import (
        setup_sphere_field_params,
        setup_field_params
    )
except ImportError:
    pass

# Публичный API - только функции для полей
# Функции клонеров и эффекторов удалены после миграции на новую систему
__all__ = [
    # REMOVED: 'setup_grid_cloner_params', 'setup_linear_cloner_params', 'setup_circle_cloner_params'
    # REMOVED: 'setup_random_effector_params', 'setup_noise_effector_params', 'setup_effector_params'
    # Use: core.parameters.set_values_from_parameters() with parameter definitions
    'setup_sphere_field_params',
    'setup_field_params'
]

def register():
    """Register parameter setup components"""
    pass

def unregister():
    """Unregister parameter setup components"""
    pass
