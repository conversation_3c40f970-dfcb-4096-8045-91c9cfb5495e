"""
Утилиты для автоматической регистрации модулей аддона.
"""

import inspect
import importlib
import pkgutil
from typing import Type, List, Any, Optional

# Список файлов, которые нужно игнорировать при регистрации
DEPRECATED_FILES = [
    # Список очищен
]

# Список модулей, которые нужно пропускать при автоматической регистрации
SKIP_MODULES = [
    'core',  # Пропускаем operations.core - этот модуль не существует
    '__pycache__',  # Пропускаем кэш Python
]

def auto_register_modules(package_path: str, base_class: Optional[Type] = None) -> List[Any]:
    """
    Автоматически регистрирует все модули из указанного пакета и его подпакетов.

    Args:
        package_path: Путь к пакету (например, 'advanced_cloners.src.effectors')
        base_class: Если указан, регистрирует только классы-наследники этого класса

    Returns:
        Список зарегистрированных модулей или классов
    """
    registered_items = []

    try:
        # Импортируем пакет
        package = importlib.import_module(package_path)

        # Перебираем все модули и подпакеты в пакете
        for _, name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + '.'):
            # Пропускаем файлы из списка исключений
            if name in DEPRECATED_FILES:
                print(f"Skipping excluded module: {name}")
                continue

            # Пропускаем модули из списка исключений
            module_name = name.split('.')[-1]  # Получаем последнюю часть имени модуля
            if module_name in SKIP_MODULES:
                print(f"Skipping module: {name}")
                continue

            # Дополнительная проверка для избежания импорта несуществующих модулей
            # Пропускаем если это operations.core (который не существует на верхнем уровне)
            if (name == 'advanced_cloners.operations.core' or
                name.endswith('.operations.core') or
                'operations.core' in name):
                print(f"Skipping operations.core module: {name}")
                continue

            if is_pkg:
                # Если это подпакет, рекурсивно регистрируем его
                try:
                    sub_package = importlib.import_module(name)

                    # Проверяем, есть ли у подпакета методы register/unregister
                    if hasattr(sub_package, 'register'):
                        sub_package.register()
                        registered_items.append(sub_package)
                        print(f"Registered sub-package: {name}")
                    else:
                        # Если у подпакета нет методов register/unregister, рекурсивно обрабатываем его
                        sub_items = auto_register_modules(name, base_class)
                        registered_items.extend(sub_items)
                except Exception as e:
                    print(f"Error registering sub-package {name}: {e}")

            else:  # Если это модуль
                try:
                    # Импортируем модуль
                    module = importlib.import_module(name)

                    # Если указан базовый класс, находим все классы-наследники
                    if base_class:
                        for obj_name, obj in inspect.getmembers(module):
                            if inspect.isclass(obj) and issubclass(obj, base_class) and obj != base_class:
                                # Регистрируем найденный класс, если у него есть метод register
                                if hasattr(obj, 'register'):
                                    obj.register()
                                    registered_items.append(obj)
                                    print(f"Registered class: {obj.__name__}")
                    else:
                        # Проверяем, есть ли в модуле что-то для регистрации
                        has_registrable_content = False

                        # Проверяем наличие классов Blender (операторы, панели, свойства)
                        for obj_name, obj in inspect.getmembers(module):
                            if inspect.isclass(obj):
                                # Проверяем, является ли это классом Blender
                                if (hasattr(obj, 'bl_idname') or hasattr(obj, 'bl_label') or hasattr(obj, 'bl_rna') or
                                    # Проверяем наследование от базовых классов Blender
                                    (hasattr(obj, '__bases__') and any(
                                        base.__name__ in ['Operator', 'Panel', 'PropertyGroup', 'UIList', 'Menu', 'Header']
                                        for base in obj.__bases__
                                    ))):
                                    has_registrable_content = True
                                    break

                        # Регистрируем сам модуль, если у него есть метод register и есть что регистрировать
                        if hasattr(module, 'register'):
                            if has_registrable_content:
                                module.register()
                                registered_items.append(module)
                                print(f"Registered module: {name}")
                            else:
                                # Модуль содержит только утилиты, не требует регистрации
                                print(f"Module {name} contains utilities only, no registration needed")
                        elif has_registrable_content:
                            print(f"Module {name} has registrable content but no register function")

                except Exception as e:
                    print(f"Error registering module {name}: {e}")

    except Exception as e:
        print(f"Error during auto-registration: {e}")

    return registered_items


def auto_unregister_modules(package_path: str, base_class: Optional[Type] = None) -> None:
    """
    Автоматически отменяет регистрацию всех модулей из указанного пакета и его подпакетов.

    Args:
        package_path: Путь к пакету
        base_class: Если указан, отменяет регистрацию только классов-наследников этого класса
    """
    try:
        # Импортируем пакет
        package = importlib.import_module(package_path)

        # Перебираем все модули и подпакеты в пакете в обратном порядке
        modules = list(pkgutil.iter_modules(package.__path__, package.__name__ + '.'))
        modules.reverse()

        for _, name, is_pkg in modules:
            # Пропускаем файлы из списка исключений
            if name in DEPRECATED_FILES:
                print(f"Skipping excluded module: {name}")
                continue

            # Пропускаем модули из списка исключений
            module_name = name.split('.')[-1]  # Получаем последнюю часть имени модуля
            if module_name in SKIP_MODULES:
                print(f"Skipping module: {name}")
                continue

            # Дополнительная проверка для избежания импорта несуществующих модулей
            # Пропускаем если это operations.core (который не существует на верхнем уровне)
            if (name == 'advanced_cloners.operations.core' or
                name.endswith('.operations.core') or
                'operations.core' in name):
                print(f"Skipping operations.core module during unregistration: {name}")
                continue

            if is_pkg:
                # Если это подпакет, рекурсивно отменяем его регистрацию
                try:
                    sub_package = importlib.import_module(name)

                    # Проверяем, есть ли у подпакета методы register/unregister
                    if hasattr(sub_package, 'unregister'):
                        sub_package.unregister()
                        print(f"Unregistered sub-package: {name}")
                    else:
                        # Если у подпакета нет методов register/unregister, рекурсивно обрабатываем его
                        auto_unregister_modules(name, base_class)
                except Exception as e:
                    print(f"Error unregistering sub-package {name}: {e}")

            else:  # Если это модуль
                try:
                    # Импортируем модуль
                    module = importlib.import_module(name)

                    # Если указан базовый класс, находим все классы-наследники
                    if base_class:
                        for obj_name, obj in inspect.getmembers(module):
                            if inspect.isclass(obj) and issubclass(obj, base_class) and obj != base_class:
                                # Отменяем регистрацию найденного класса
                                if hasattr(obj, 'unregister'):
                                    obj.unregister()
                                    print(f"Unregistered class: {obj.__name__}")
                    else:
                        # Проверяем, есть ли в модуле что-то для отмены регистрации
                        has_registrable_content = False

                        # Проверяем наличие классов Blender (операторы, панели, свойства)
                        for obj_name, obj in inspect.getmembers(module):
                            if inspect.isclass(obj):
                                # Проверяем, является ли это классом Blender
                                if (hasattr(obj, 'bl_idname') or hasattr(obj, 'bl_label') or hasattr(obj, 'bl_rna') or
                                    # Проверяем наследование от базовых классов Blender
                                    (hasattr(obj, '__bases__') and any(
                                        base.__name__ in ['Operator', 'Panel', 'PropertyGroup', 'UIList', 'Menu', 'Header']
                                        for base in obj.__bases__
                                    ))):
                                    has_registrable_content = True
                                    break

                        # Отменяем регистрацию самого модуля, если у него есть метод unregister и есть что отменять
                        if hasattr(module, 'unregister'):
                            if has_registrable_content:
                                module.unregister()
                                print(f"Unregistered module: {name}")
                            else:
                                # Модуль содержит только утилиты, не требует отмены регистрации
                                print(f"Module {name} contains utilities only, no unregistration needed")
                        elif has_registrable_content:
                            print(f"Module {name} has registrable content but no unregister function")

                except Exception as e:
                    print(f"Error unregistering module {name}: {e}")

    except Exception as e:
        print(f"Error during auto-unregistration: {e}")