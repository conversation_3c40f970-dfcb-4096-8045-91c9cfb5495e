"""
Основной модуль интерфейса эффекторов.

Содержит главную панель UI для эффекторов и соответствующие функции.
"""

import bpy
from bpy.types import Panel
from ...common.ui_constants import (
    UI_EFFECTOR_PANEL_CATEGORY, UI_EFFECTOR_PANEL_REGION,
    UI_SCALE_Y_LARGE
)

from ....models.effectors import EFFECTOR_TYPES
from ....core.registry import component_registry

from .effector_display import draw_effector_ui


class EFFECTOR_PT_main_panel(Panel):
    """Panel for effectors"""
    bl_label = "Effectors"
    bl_idname = "EFFECTOR_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = UI_EFFECTOR_PANEL_REGION
    bl_category = UI_EFFECTOR_PANEL_CATEGORY

    def draw(self, context):
        layout = self.layout
        obj = context.active_object

        # Кнопки создания эффекторов
        grid = layout.grid_flow(columns=2, even_columns=True)
        grid.scale_y = UI_SCALE_Y_LARGE

        for eff_id, eff_name, _, eff_icon in EFFECTOR_TYPES:
            # Убираем слово "Effector" из названия кнопки
            button_name = eff_name.replace(" Effector", "")
            op = grid.operator("object.create_effector", text=button_name, icon=eff_icon)
            if op:  # Проверяем, что оператор создался успешно
                op.effector_type = eff_id

        if not obj:
            layout.label(text="Select an object")
            return

        # Находим все эффекторы на объекте
        eff_mods = []
        effector_group_names = component_registry.get_effector_group_names()
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                if any(group_name in mod.node_group.name for group_name in effector_group_names.values()):
                    eff_mods.append(mod)

        # Если есть эффекторы, показываем их количество
        if eff_mods:
            layout.separator()
            layout.label(text=f"Effectors: {len(eff_mods)}")

            # Отображаем каждый эффектор используя функцию из effector_utils
            for mod in eff_mods:
                draw_effector_ui(context, layout, obj, mod)


# Функции регистрации и отмены регистрации
def register():
    bpy.utils.register_class(EFFECTOR_PT_main_panel)

def unregister():
    bpy.utils.unregister_class(EFFECTOR_PT_main_panel)
