"""
Random Effector Parameter Definitions

This module defines all parameters for the Random Effector component using the unified
parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

Random Effector applies random transformations to objects with configurable ranges.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    ParameterSubtype,
    get_standard_parameter_set
)

# Basic Random Effector parameters
RANDOM_EFFECTOR_BASIC_GROUP = ParameterGroup(
    name="random_settings",
    description="Random Effector specific settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Uniform Scale",
            param_type=ParameterType.BOOL,
            default_value=True,
            description="Use the same random value for all scale axes",
            ui_group="Random Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Seed",
            param_type=ParameterType.INT,
            default_value=0,
            min_value=0,
            max_value=10000,
            description="Seed for random number generation",
            ui_group="Random Settings",
            ui_order=2
        )
    ]
)

# Standard transformation parameters group
# We'll use a standard set for transformation parameters
# This would typically include Position, Rotation, Scale

# Complete parameter set for Random Effector
RANDOM_EFFECTOR_PARAMETERS = ComponentParameterSet(
    component_type="EFFECTOR",
    component_id="RANDOM",
    description="Random Effector parameter set - applies random transformations",
    version="1.0",
    groups=[
        get_standard_parameter_set("effector_io"),  # Standard effector input/output
        get_standard_parameter_set("effector_base"),  # Base effector parameters (Enable, Strength)
        get_standard_parameter_set("transform"),  # Standard transform parameters
        RANDOM_EFFECTOR_BASIC_GROUP,
        get_standard_parameter_set("field_control")  # Field influence parameters
    ]
)