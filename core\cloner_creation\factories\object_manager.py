"""
Управление объектами и коллекциями для клонеров.

Этот модуль отвечает за:
- Создание объектов клонеров
- Создание и управление коллекциями
- Настройку видимости
- Создание модификаторов
"""

import bpy
import bmesh
from ....core.factories.universal_factory import universal_factory
from ..common.naming import (
    generate_cloner_name,
    generate_collection_name,
    generate_modifier_name
)
from ..common.visibility import setup_cloner_visibility, setup_collection_visibility

def create_cloner_object(orig_obj, cloner_type):
    """
    Создает объект клонера.

    Args:
        orig_obj: Исходный объект
        cloner_type: Тип клонера

    Returns:
        object: Созданный объект клонера или None
    """
    try:
        # Создаем уникальное имя для клонер-объекта
        cloner_name = generate_cloner_name(orig_obj.name, cloner_type, "object")

        # Создаем пустой объект для клонера
        mesh = bpy.data.meshes.new(f"{cloner_name}_Mesh")
        cloner_obj = bpy.data.objects.new(cloner_name, mesh)

        # Настраиваем видимость
        setup_cloner_visibility(cloner_obj)

        return cloner_obj
    except Exception as e:
        print(f"Ошибка при создании объекта клонера: {e}")
        return None

def create_cloner_collection(context, orig_obj, cloner_type):
    """
    Создает коллекцию для клонера.

    Args:
        context: Контекст Blender
        orig_obj: Исходный объект
        cloner_type: Тип клонера

    Returns:
        collection: Созданная коллекция или None
    """
    try:
        # Создаем коллекцию для клонера
        cloner_collection_name = generate_collection_name(orig_obj.name, cloner_type, "object")
        cloner_collection = bpy.data.collections.new(cloner_collection_name)
        bpy.context.scene.collection.children.link(cloner_collection)

        # Настраиваем видимость коллекции
        setup_collection_visibility(context, cloner_collection)

        return cloner_collection
    except Exception as e:
        print(f"Ошибка при создании коллекции клонера: {e}")
        return None

def create_modifier(cloner_obj, cloner_type):
    """
    Создает модификатор для клонера.

    Args:
        cloner_obj: Объект клонера
        cloner_type: Тип клонера

    Returns:
        modifier: Созданный модификатор или None
    """
    try:
        # Получаем базовое имя модификатора
        base_mod_name = universal_factory.get_cloner_mod_name(cloner_type)

        # Создаем уникальное имя для модификатора
        modifier_name = generate_modifier_name(cloner_obj, cloner_type, "object")

        # Создаем модификатор на клонер-объекте
        modifier = cloner_obj.modifiers.new(name=modifier_name, type='NODES')

        return modifier
    except Exception as e:
        print(f"Ошибка при создании модификатора клонера: {e}")
        return None

def add_object_to_collection(cloner_obj, cloner_collection):
    """
    Добавляет объект клонера в коллекцию.

    Args:
        cloner_obj: Объект клонера
        cloner_collection: Коллекция клонера

    Returns:
        bool: True если добавление прошло успешно
    """
    try:
        # Добавляем клонер-объект в коллекцию
        cloner_collection.objects.link(cloner_obj)
        return True
    except Exception as e:
        print(f"Ошибка при добавлении объекта в коллекцию: {e}")
        return False
