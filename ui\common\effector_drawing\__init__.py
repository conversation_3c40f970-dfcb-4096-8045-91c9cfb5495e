"""
Effector drawing utilities for Advanced Cloners addon.

This module provides UI drawing functions for effector interfaces,
including parameter displays, controls, and field connections.
"""

# Import all functionality from sub-modules for backward compatibility
from .effector_controls import (
    draw_effector_ui,
    draw_effector_expanded_ui,
    draw_effector_header,
    draw_effector_links,
    get_effector_icon,
    get_linked_cloners,
    get_available_cloners_for_linking
)

from .parameter_display import (
    draw_effector_socket_parameters,
    draw_effect_parameters,
    draw_transform_parameters,
    draw_field_parameters,
    draw_other_parameters
)

# Utility functions


def get_effector_type_from_modifier(modifier):
    """
    Определяет тип эффектора из модификатора.

    Args:
        modifier: Модификатор эффектора

    Returns:
        str: Тип эффектора (RANDOM, NOISE, CUSTOM)
    """
    if not modifier or not modifier.node_group:
        return "UNKNOWN"

    node_group_name = modifier.node_group.name

    if "RandomEffector" in node_group_name:
        return "RANDOM"
    elif "NoiseEffector" in node_group_name:
        return "NOISE"
    elif "Effector" in node_group_name:
        return "CUSTOM"

    return "UNKNOWN"


def is_effector_modifier(modifier):
    """
    Проверяет, является ли модификатор эффектором.

    Args:
        modifier: Модификатор для проверки

    Returns:
        bool: True если это эффектор
    """
    if not modifier or modifier.type != 'NODES':
        return False

    if not modifier.node_group:
        return False

    return "Effector" in modifier.node_group.name


def register():
    """Register effector drawing components"""
    pass


def unregister():
    """Unregister effector drawing components"""
    pass
