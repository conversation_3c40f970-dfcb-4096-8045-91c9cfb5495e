"""
Noise Effector Logic Builder

This module implements the core logic for the Noise Effector.
It creates the node graph that applies noise-based transformations to instances.
"""

import bpy


def create_logic_group(name_suffix=""):
    """
    Create a logic group for the Noise Effector that contains the core functionality.
    
    Args:
        name_suffix: Optional suffix for the node group name
        
    Returns:
        The created node group
    """
    # Создаем новую группу узлов
    logic_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=f"NoiseEffectorLogic{name_suffix}")

    # --- Настройка интерфейса ---
    # Выходы
    logic_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Входы
    logic_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    logic_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
    logic_group.interface.new_socket(name="Strength", in_out='INPUT', socket_type='NodeSocketFloat')

    # Параметры трансформации
    logic_group.interface.new_socket(name="Position", in_out='INPUT', socket_type='NodeSocketVector')
    logic_group.interface.new_socket(name="Symmetric Translation", in_out='INPUT', socket_type='NodeSocketBool')
    logic_group.interface.new_socket(name="Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    logic_group.interface.new_socket(name="Symmetric Rotation", in_out='INPUT', socket_type='NodeSocketBool')
    logic_group.interface.new_socket(name="Scale", in_out='INPUT', socket_type='NodeSocketVector')
    logic_group.interface.new_socket(name="Uniform Scale", in_out='INPUT', socket_type='NodeSocketBool')

    # Параметры шума
    logic_group.interface.new_socket(name="Noise Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    logic_group.interface.new_socket(name="Noise Detail", in_out='INPUT', socket_type='NodeSocketFloat')
    logic_group.interface.new_socket(name="Noise Roughness", in_out='INPUT', socket_type='NodeSocketFloat')
    logic_group.interface.new_socket(name="Noise Lacunarity", in_out='INPUT', socket_type='NodeSocketFloat')
    logic_group.interface.new_socket(name="Noise Distortion", in_out='INPUT', socket_type='NodeSocketFloat')
    logic_group.interface.new_socket(name="Noise Position", in_out='INPUT', socket_type='NodeSocketVector')
    logic_group.interface.new_socket(name="Noise XYZ Scale", in_out='INPUT', socket_type='NodeSocketVector')
    logic_group.interface.new_socket(name="Speed", in_out='INPUT', socket_type='NodeSocketFloat')
    logic_group.interface.new_socket(name="Seed", in_out='INPUT', socket_type='NodeSocketInt')

    # --- Создание узлов ---
    nodes = logic_group.nodes
    links = logic_group.links

    # Базовые узлы
    group_input = nodes.new('NodeGroupInput')
    group_input.location = (-800, 0)
    
    group_output = nodes.new('NodeGroupOutput')
    group_output.location = (800, 0)
    
    # Позиционирование узлов
    group_input.location = (-800, 0)
    group_output.location = (800, 0)

    # Проверка условия включения
    check_enabled = nodes.new('GeometryNodeSwitch')
    check_enabled.input_type = 'GEOMETRY'
    check_enabled.location = (600, 0)
    
    # Получение позиции, вращения и масштаба
    get_position = nodes.new('GeometryNodeInputPosition')
    get_position.location = (-700, -200)
    
    # Генерация шума
    noise_texture = nodes.new('ShaderNodeTexNoise')
    noise_texture.location = (-400, -200)
    noise_texture.noise_dimensions = '4D'  # Используем 4D шум для анимации
    
    # Вектор преобразования для шума
    transform_vector = nodes.new('ShaderNodeVectorMath')
    transform_vector.operation = 'MULTIPLY'
    transform_vector.location = (-550, -200)
    
    # Вектор смещения для шума
    offset_vector = nodes.new('ShaderNodeVectorMath')
    offset_vector.operation = 'ADD'
    offset_vector.location = (-250, -200)
    
    # Получение времени для анимации
    time_node = nodes.new('GeometryNodeInputScene')
    time_node.location = (-700, -300)
    
    # Масштабирование времени
    multiply_time = nodes.new('ShaderNodeMath')
    multiply_time.operation = 'MULTIPLY'
    multiply_time.location = (-550, -300)
    
    # Узел для трансформации
    transform_node = nodes.new('GeometryNodeTransform')
    transform_node.location = (400, 0)
    
    # Узлы для преобразования шума в трансформацию
    noise_to_position = nodes.new('ShaderNodeVectorMath')
    noise_to_position.operation = 'MULTIPLY'
    noise_to_position.location = (0, 100)
    
    noise_to_rotation = nodes.new('ShaderNodeVectorMath')
    noise_to_rotation.operation = 'MULTIPLY'
    noise_to_rotation.location = (0, 0)
    
    noise_to_scale = nodes.new('ShaderNodeVectorMath')
    noise_to_scale.operation = 'MULTIPLY'
    noise_to_scale.location = (0, -100)
    
    # Обработка симметричного шума
    symmetric_position = nodes.new('ShaderNodeVectorMath')
    symmetric_position.operation = 'MULTIPLY'
    symmetric_position.location = (200, 100)
    
    symmetric_rotation = nodes.new('ShaderNodeVectorMath')
    symmetric_rotation.operation = 'MULTIPLY'
    symmetric_rotation.location = (200, 0)
    
    # Обработка одинакового масштаба по осям
    uniform_scale_switch = nodes.new('GeometryNodeSwitch')
    uniform_scale_switch.input_type = 'VECTOR'
    uniform_scale_switch.location = (200, -100)

    # Вектор для унифицированного масштаба (все оси одинаковые)
    make_uniform_vector = nodes.new('ShaderNodeCombineXYZ')
    make_uniform_vector.location = (100, -150)

    # Константы для управления симметрией
    symmetric_vector = nodes.new('ShaderNodeVectorMath')
    symmetric_vector.operation = 'MULTIPLY'
    symmetric_vector.inputs[1].default_value = (-1.0, -1.0, -1.0)
    symmetric_vector.location = (100, 150)

    # --- Связывание узлов ---
    # Соединяем входы
    links.new(group_input.outputs['Geometry'], check_enabled.inputs[False])
    links.new(group_input.outputs['Enable'], check_enabled.inputs['Switch'])
    
    # Настройки шума
    links.new(get_position.outputs['Position'], transform_vector.inputs[0])
    links.new(group_input.outputs['Noise XYZ Scale'], transform_vector.inputs[1])
    
    links.new(transform_vector.outputs['Vector'], offset_vector.inputs[0])
    links.new(group_input.outputs['Noise Position'], offset_vector.inputs[1])
    
    links.new(offset_vector.outputs['Vector'], noise_texture.inputs['Vector'])
    links.new(group_input.outputs['Noise Scale'], noise_texture.inputs['Scale'])
    links.new(group_input.outputs['Noise Detail'], noise_texture.inputs['Detail'])
    links.new(group_input.outputs['Noise Roughness'], noise_texture.inputs['Roughness'])
    links.new(group_input.outputs['Noise Lacunarity'], noise_texture.inputs['Lacunarity'])
    links.new(group_input.outputs['Noise Distortion'], noise_texture.inputs['Distortion'])
    
    # Анимация шума
    links.new(time_node.outputs['Frame'], multiply_time.inputs[0])
    links.new(group_input.outputs['Speed'], multiply_time.inputs[1])
    links.new(multiply_time.outputs['Value'], noise_texture.inputs['W'])
    
    # Управление сидом
    links.new(group_input.outputs['Seed'], noise_texture.inputs['W Offset'])
    
    # Преобразование шума в трансформации
    links.new(noise_texture.outputs['Fac'], noise_to_position.inputs[0])
    links.new(group_input.outputs['Position'], noise_to_position.inputs[1])
    links.new(group_input.outputs['Strength'], noise_to_position.inputs[2])
    
    links.new(noise_texture.outputs['Fac'], noise_to_rotation.inputs[0])
    links.new(group_input.outputs['Rotation'], noise_to_rotation.inputs[1])
    links.new(group_input.outputs['Strength'], noise_to_rotation.inputs[2])
    
    links.new(noise_texture.outputs['Fac'], noise_to_scale.inputs[0])
    links.new(group_input.outputs['Scale'], noise_to_scale.inputs[1])
    links.new(group_input.outputs['Strength'], noise_to_scale.inputs[2])
    
    # Обработка симметрии
    links.new(noise_to_position.outputs['Vector'], symmetric_position.inputs[0])
    links.new(group_input.outputs['Symmetric Translation'], check_enabled.inputs['Switch'])
    
    # Для симметричной трансляции
    links.new(noise_to_position.outputs['Vector'], symmetric_vector.inputs[0])
    links.new(symmetric_vector.outputs['Vector'], symmetric_position.inputs[1])
    
    links.new(noise_to_rotation.outputs['Vector'], symmetric_rotation.inputs[0])
    links.new(group_input.outputs['Symmetric Rotation'], check_enabled.inputs['Switch'])
    
    # Обработка унифицированного масштаба
    links.new(noise_to_scale.outputs['Vector'], uniform_scale_switch.inputs[False])
    links.new(group_input.outputs['Uniform Scale'], uniform_scale_switch.inputs['Switch'])
    
    # Создаем унифицированный вектор масштаба
    links.new(noise_to_scale.outputs['Vector'].x, make_uniform_vector.inputs['X'])
    links.new(noise_to_scale.outputs['Vector'].x, make_uniform_vector.inputs['Y'])
    links.new(noise_to_scale.outputs['Vector'].x, make_uniform_vector.inputs['Z'])
    links.new(make_uniform_vector.outputs['Vector'], uniform_scale_switch.inputs[True])
    
    # Соединяем выходы трансформации
    links.new(symmetric_position.outputs['Vector'], transform_node.inputs['Translation'])
    links.new(symmetric_rotation.outputs['Vector'], transform_node.inputs['Rotation'])
    links.new(uniform_scale_switch.outputs['Output'], transform_node.inputs['Scale'])
    
    # Соединяем геометрию
    links.new(group_input.outputs['Geometry'], transform_node.inputs['Geometry'])
    links.new(transform_node.outputs['Geometry'], check_enabled.inputs[True])
    links.new(check_enabled.outputs['Output'], group_output.inputs['Geometry'])

    return logic_group