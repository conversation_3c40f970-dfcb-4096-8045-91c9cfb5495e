"""
Управление цепочками клонеров.

Этот модуль отвечает за:
- Настройку свойств цепочки клонеров
- Регистрацию обновлений цепочки
- Поиск источника цепочки
- Обработку связей между клонерами
"""

import bpy
from ....core.utils.duplication.chain_tracker import get_cloner_chain_for_object
from ....operations.helpers.common.utils import register_chain_update

def setup_chain_properties(modifier, orig_obj, cloner_obj):
    """
    Настраивает свойства цепочки клонеров.

    Args:
        modifier: Модификатор клонера
        orig_obj: Исходный объект
        cloner_obj: Объект клонера

    Returns:
        bool: True если настройка прошла успешно
    """
    try:
        # СНАЧАЛА обрабатываем связь для цепочки клонеров
        if "original_obj" in orig_obj or orig_obj.name.startswith("Cloner_"):
            # Это означает, что текущий объект уже является результатом клонирования
            modifier["is_object_chain"] = True
            modifier["previous_cloner_object"] = orig_obj.name
            modifier["is_chained_cloner"] = True

            # Сохраняем источник цепочки - ищем исходный объект
            if "chain_source_object" in orig_obj:
                # Если у предыдущего клонера есть chain_source_object, используем его
                modifier["chain_source_object"] = orig_obj["chain_source_object"]
                print(f"CHAIN DEBUG: Найден chain_source_object в предыдущем клонере: {orig_obj['chain_source_object']}")
            elif "original_object" in orig_obj:
                # Если есть original_object, используем его как источник
                modifier["chain_source_object"] = orig_obj["original_object"]
                print(f"CHAIN DEBUG: Используем original_object как chain_source_object: {orig_obj['original_object']}")
            else:
                # Fallback - пытаемся найти исходный объект по имени
                source_name = find_chain_source_by_name(orig_obj.name)
                modifier["chain_source_object"] = source_name
                print(f"CHAIN DEBUG: Вычислили chain_source_object из имени: {source_name}")

            # Регистрируем обновление цепочки
            if hasattr(bpy.app, "timers"):
                bpy.app.timers.register(lambda: register_chain_update(orig_obj, cloner_obj), first_interval=0.2)
        else:
            # Если это первый клонер, сохраняем ссылку на источник
            modifier["chain_source_object"] = orig_obj.name

        # Инициализируем список следующих клонеров
        modifier["next_cloners"] = []

        return True
    except Exception as e:
        print(f"Ошибка при настройке свойств цепочки: {e}")
        return False

def find_chain_source_by_name(obj_name):
    """
    Находит источник цепочки по имени объекта.

    Args:
        obj_name: Имя объекта в цепочке

    Returns:
        str: Имя источника цепочки
    """
    try:
        # Убираем префиксы "Cloner_" чтобы найти исходный объект
        source_name = obj_name
        while source_name.startswith("Cloner_"):
            source_name = source_name[7:]  # Убираем "Cloner_"
            if "_" in source_name:
                # Убираем суффиксы типа "_GRID", "_LINEAR" и т.д.
                parts = source_name.split("_")
                if parts[-1] in ["GRID", "LINEAR", "CIRCLE"]:
                    source_name = "_".join(parts[:-1])
        return source_name
    except Exception as e:
        print(f"Ошибка при поиске источника цепочки: {e}")
        return obj_name

def find_previous_cloner(target_collection):
    """
    Находит предыдущий клонер в цепочке коллекций.

    Args:
        target_collection: Целевая коллекция

    Returns:
        Object: Объект предыдущего клонера или None
    """
    try:
        # Проверяем, является ли целевая коллекция коллекцией клонера
        is_cloner_collection = target_collection.name.startswith("cloner_")
        previous_cloner_object = None

        if is_cloner_collection:
            # Ищем объект клонера, который создал эту коллекцию
            for obj in bpy.data.objects:
                if obj.type == 'MESH' and obj.name.startswith("Cloner_"):
                    # Проверяем модификаторы объекта
                    for modifier in obj.modifiers:
                        if modifier.type == 'NODES' and hasattr(modifier, 'get'):
                            cloner_collection_name = modifier.get("cloner_collection", "")
                            if cloner_collection_name == target_collection.name:
                                previous_cloner_object = obj
                                break
                    if previous_cloner_object:
                        break

        return previous_cloner_object

    except Exception as e:
        print(f"Ошибка при поиске предыдущего клонера: {e}")
        return None

def setup_collection_chain_properties(modifier, target_collection, cloner_obj, previous_cloner_object=None, cloner_collection=None):
    """
    Настраивает свойства цепочки для клонеров коллекций.

    Args:
        modifier: Модификатор клонера
        target_collection: Целевая коллекция
        cloner_obj: Объект клонера
        previous_cloner_object: Предыдущий клонер в цепочке (опционально)
        cloner_collection: Коллекция клонера (опционально)

    Returns:
        bool: True если настройка прошла успешно
    """
    try:
        # Проверяем, является ли целевая коллекция коллекцией клонера
        is_cloner_collection = target_collection.name.startswith("cloner_")

        # Используем переданный previous_cloner_object или ищем его
        if previous_cloner_object is None and is_cloner_collection:
            # Ищем предыдущий клонер в цепочке
            previous_cloner_object = find_previous_cloner(target_collection)

        if previous_cloner_object:
            # Это цепочка клонеров коллекций
            modifier["is_collection_chain"] = True
            modifier["previous_cloner_object"] = previous_cloner_object.name
            modifier["is_chained_cloner"] = True

            # Сохраняем источник цепочки
            if "chain_source_collection" in previous_cloner_object:
                modifier["chain_source_collection"] = previous_cloner_object["chain_source_collection"]
            elif "target_collection" in previous_cloner_object:
                modifier["chain_source_collection"] = previous_cloner_object["target_collection"]
            else:
                modifier["chain_source_collection"] = target_collection.name

            # Регистрируем обновление цепочки
            if hasattr(bpy.app, "timers"):
                bpy.app.timers.register(lambda: register_chain_update(previous_cloner_object, cloner_obj), first_interval=0.2)
        else:
            # Если это первый клонер коллекции, сохраняем ссылку на источник
            modifier["chain_source_collection"] = target_collection.name

        # Инициализируем список следующих клонеров
        modifier["next_cloners"] = []

        return True
    except Exception as e:
        print(f"Ошибка при настройке свойств цепочки коллекций: {e}")
        return False

def register_collection_chain_update(target_collection, cloner_obj):
    """
    Регистрирует обновление цепочки для клонеров коллекций.

    Args:
        target_collection: Целевая коллекция
        cloner_obj: Объект клонера

    Returns:
        bool: True если регистрация прошла успешно
    """
    try:
        # Используем общую функцию register_chain_update
        return register_chain_update(target_collection, cloner_obj)
    except Exception as e:
        print(f"Ошибка при регистрации обновления цепочки коллекций: {e}")
        return False
