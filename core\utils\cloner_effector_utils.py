"""
Утилиты для работы с эффекторами в клонерах.
"""

import bpy
from .globals import _effector_handler_blocked
from .base_utils import convert_array_to_tuple
from ...models.cloners import CLONER_NODE_GROUP_PREFIXES
from ...models.effectors import EFFECTOR_NODE_GROUP_PREFIXES

def get_effector_modifiers(obj):
    """
    Получает список всех модификаторов-эффекторов на объекте.
    
    Args:
        obj: Об<PERSON><PERSON><PERSON><PERSON> Blender, для которого нужно найти эффекторы
        
    Returns:
        list: Список имен модификаторов-эффекторов
    """
    if not obj or not hasattr(obj, 'modifiers'):
        return []
        
    effector_mods = []
    
    for mod in obj.modifiers:
        # Проверка на Geometry Nodes модификатор с группой
        if mod.type == 'NODES' and mod.node_group:
            # Проверка на эффектор по префиксу имени группы
            if any(mod.node_group.name.startswith(p) for p in EFFECTOR_NODE_GROUP_PREFIXES):
                effector_mods.append(mod.name)
            # Дополнительная проверка для пользовательских эффекторов
            elif "Effector" in mod.node_group.name:
                effector_mods.append(mod.name)
            # Проверка на наличие флага эффектора в метаданных
            elif mod.get("is_effector", False):
                effector_mods.append(mod.name)
                
    return effector_mods

def update_cloner_with_effectors(obj, cloner_mod):
    """
    Обновляет нод-группу клонера, применяя связанные эффекторы более эффективным способом.
    
    Args:
        obj: Объект, содержащий модификатор (теперь это уже дубликат)
        cloner_mod: Модификатор клонера с нод-группой
    """
    if not cloner_mod or not cloner_mod.node_group:
        print("[DEBUG] update_cloner_with_effectors: Модификатор не имеет нод-группы")
        return
    
    # Проверка, является ли клонер стековым
    # Проверяем как в модификаторе, так и в node_group для надежности
    mod_is_stacked = cloner_mod.get("is_stacked_cloner", False)
    node_is_stacked = cloner_mod.node_group.get("is_stacked_cloner", False)
    is_stacked_cloner = mod_is_stacked or node_is_stacked
    
    # Проверка, использует ли клонер старый стиль клонеров (на основе OLD_CODE)
    is_old_style = cloner_mod.node_group.get("is_old_style_cloner", False)
    
    # Если это старый стиль клонера, используем только старый код для обработки эффекторов
    if is_old_style:
        print("[DEBUG] update_cloner_with_effectors: Обнаружен клонер старого стиля, используем совместимую логику")
        
        # Синхронизируем флаг стекового клонера между модификатором и нод-группой
        if mod_is_stacked != node_is_stacked:
            if node_is_stacked:
                cloner_mod["is_stacked_cloner"] = True
            elif mod_is_stacked:
                cloner_mod.node_group["is_stacked_cloner"] = True
                
            # Обновляем значение после синхронизации
            is_stacked_cloner = True
        
        # Обновляем группу узлов клонера
        node_group = cloner_mod.node_group
        linked_effectors = node_group.get("linked_effectors", [])
        
        # Проверяем валидность списка эффекторов
        valid_linked_effectors = []
        for eff_name in linked_effectors:
            eff_mod = obj.modifiers.get(eff_name)
            if eff_mod is not None and eff_mod.type == 'NODES' and eff_mod.node_group:
                # Проверяем, что это реально эффектор
                is_effector = True  # Для старого стиля считаем любой связанный модификатор эффектором
                
                if is_effector:
                    valid_linked_effectors.append(eff_name)
                    print(f"[DEBUG] update_cloner_with_effectors: Валидный эффектор: {eff_name}")
                else:
                    print(f"[DEBUG] update_cloner_with_effectors: Невалидный эффектор (не является эффектором): {eff_name}")
            else:
                print(f"[DEBUG] update_cloner_with_effectors: Невалидный эффектор (не найден или нет нод-группы): {eff_name}")
        
        # Если список изменился, обновляем его
        if len(valid_linked_effectors) != len(linked_effectors):
            print(f"[DEBUG] update_cloner_with_effectors: Обновляем список эффекторов с {len(linked_effectors)} на {len(valid_linked_effectors)}")
            node_group["linked_effectors"] = valid_linked_effectors
            linked_effectors = valid_linked_effectors
        
        # Сначала получим список всех связанных эффекторов (старых)
        old_effectors = []
        effector_nodes = [n for n in node_group.nodes if n.name.startswith('Effector_')]
        for node in effector_nodes:
            effector_name = node.name.replace('Effector_', '')
            if effector_name not in old_effectors:
                old_effectors.append(effector_name)
        
        # Проверка на пустой список эффекторов
        if not linked_effectors:
            # Если нет эффекторов, проверим, не осталось ли от предыдущих связей
            # Найдем старые узлы эффекторов и удалим их
            if effector_nodes:
                print(f"[DEBUG] update_cloner_with_effectors: Нет эффекторов, удаляем старые узлы ({len(effector_nodes)})")
                # Восстановим прямую связь от основной геометрии к выходу
                restore_direct_connection(node_group)
                
                # Удаляем старые узлы эффекторов
                for node in effector_nodes:
                    try:
                        node_group.nodes.remove(node)
                    except Exception as e:
                        print(f"Ошибка при удалении узла: {e}")
                    
                # Включим видимость всех отвязанных эффекторов (только рендер)
                for effector_name in old_effectors:
                    effector_mod = obj.modifiers.get(effector_name)
                    if effector_mod:
                        # Включаем рендер эффектора, т.к. он был отвязан
                        effector_mod.show_render = True
            return
            
        # Находим новые и удаляемые эффекторы для управления видимостью
        to_add = [e for e in linked_effectors if e not in old_effectors]
        to_remove = [e for e in old_effectors if e not in linked_effectors]
        print(f"[DEBUG] update_cloner_with_effectors: Добавляем {len(to_add)} эффекторов, удаляем {len(to_remove)}")
        
        # Найдем выходной узел и его входящую связь
        group_output = None
        for node in node_group.nodes:
            if node.type == 'GROUP_OUTPUT':
                group_output = node
                break
        
        if not group_output:
            return
        
        # Найдем исходный узел клонера, с которого начнем цепочку
        source_node = None
        # Сначала ищем конкретные типы узлов, которые обычно являются последними в цепочке клонера
        for node in node_group.nodes:
            # Ищем узлы трансформации
            if 'Transform' in node.bl_idname and not node.name.startswith('Effector_'):
                for output in node.outputs:
                    if output.name == 'Geometry' and any(link.to_node == group_output for link in output.links):
                        source_node = node
                        source_socket = output
                        break
                if source_node:
                    break
        
        # Если не нашли, то ищем любой узел геометрии, связанный с выходом
        if not source_node:
            for node in node_group.nodes:
                if node != group_output and not node.name.startswith('Effector_'):
                    for output in node.outputs:
                        if output.name == 'Geometry' and any(link.to_node == group_output for link in output.links):
                            source_node = node
                            source_socket = output
                            break
                    if source_node:
                        break
        
        if not source_node:
            print("Не удалось найти исходный узел клонера для подключения эффекторов")
            return
        
        # Находим и удаляем все существующие связи от исходного узла к выходу
        links_to_remove = []
        for link in node_group.links:
            if link.from_node == source_node and link.to_node == group_output:
                links_to_remove.append(link)
        
        for link in links_to_remove:
            try:
                node_group.links.remove(link)
            except RuntimeError:
                # Связь уже была удалена
                pass
        
        # Удалим старые узлы эффекторов, если они есть
        for node in effector_nodes:
            node_group.nodes.remove(node)
        
        # Добавляем эффекторы по цепочке, начиная с исходного узла клонера
        current_geo = source_socket
        
        # Сохраняем позиции для размещения новых узлов
        pos_x = source_node.location.x + 200
        pos_y = source_node.location.y
        spacing = 250
        
        # Проверяем эффекторы перед добавлением, чтобы правильно обработать случай с несколькими эффекторами одного типа
        for effector_name in linked_effectors:
            # Находим модификатор эффектора
            effector_mod = obj.modifiers.get(effector_name)
            if not effector_mod or not effector_mod.node_group:
                continue
                
            # Отключаем только рендер эффектора, но оставляем видимым во viewport,
            # чтобы можно было видеть и настраивать его параметры
            effector_mod.show_render = False
            
            # Проверяем, что нод-группа эффектора действительно содержит нужные входы/выходы
            effector_group = effector_mod.node_group
            
            # Проверка на наличие входного и выходного сокета Geometry через interface.items_tree
            try:
                input_sockets = [s.name for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']
                output_sockets = [s.name for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'OUTPUT']
                
                if 'Geometry' not in output_sockets:
                    print(f"Эффектор {effector_name} не имеет выхода Geometry")
                    continue
                if 'Geometry' not in input_sockets:
                    print(f"Эффектор {effector_name} не имеет входа Geometry")
                    continue
            except Exception as e:
                print(f"Ошибка при проверке сокетов эффектора {effector_name}: {e}")
                continue
        
        # Теперь добавляем узлы эффекторов и создаем связи
        for effector_name in linked_effectors:
            # Находим модификатор эффектора
            effector_mod = obj.modifiers.get(effector_name)
            if not effector_mod or not effector_mod.node_group:
                continue
                
            # Нод-группа эффектора
            effector_group = effector_mod.node_group
            
            # Создаем уникальное имя для узла эффектора
            node_name = f"Effector_{effector_name}"
            
            # Создаем узел группы эффектора
            try:
                effector_node = node_group.nodes.new('GeometryNodeGroup')
                effector_node.name = node_name
                effector_node.node_tree = effector_group
                
                # Устанавливаем положение узла
                effector_node.location = (pos_x, pos_y)
                pos_x += spacing
                
                # Скопируем значения параметров из модификатора эффектора
                for input_socket in [s for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']:
                    if input_socket.name in ['Geometry']:
                        continue  # Пропускаем вход геометрии
                    
                    # Если параметр не входит в сокеты или не имеет установленного значения в модификаторе, пропускаем
                    if input_socket.identifier not in effector_mod:
                        continue
                        
                    # Копируем значение параметра из модификатора в узел
                    try:
                        effector_node.inputs[input_socket.name].default_value = effector_mod[input_socket.identifier]
                    except (KeyError, TypeError) as e:
                        print(f"Не удалось установить значение для {input_socket.name}: {e}")
                        # Если не удалось установить значение, пропускаем
                        pass
                
                # Подключаем геометрию от предыдущего узла к входу эффектора
                try:
                    node_group.links.new(current_geo, effector_node.inputs['Geometry'])
                except Exception as e:
                    print(f"Ошибка при создании связи к эффектору {effector_name}: {e}")
                    continue
                
                # Устанавливаем выход эффектора как текущую геометрию для следующего эффектора
                current_geo = effector_node.outputs['Geometry']
            except Exception as e:
                print(f"Ошибка при создании узла эффектора {effector_name}: {e}")
                # Восстанавливаем прямую связь в случае ошибки
                try:
                    node_group.links.new(source_socket, group_output.inputs['Geometry'])
                except:
                    pass
                return
        
        # Подключаем последний эффектор к выходу
        try:
            node_group.links.new(current_geo, group_output.inputs['Geometry'])
        except Exception as e:
            print(f"Ошибка при создании финальной связи: {e}")
            # Восстанавливаем прямую связь при ошибке
            restore_direct_connection(node_group)
        
        # Включаем все отвязанные эффекторы (только рендер)
        for effector_name in to_remove:
            effector_mod = obj.modifiers.get(effector_name)
            if effector_mod:
                # Включаем рендер эффектора, т.к. он был отвязан
                effector_mod.show_render = True
        
        # Для стековых клонеров старого стиля ничего больше не делаем
        return
    
    # Синхронизируем флаг стекового клонера между модификатором и нод-группой
    if mod_is_stacked != node_is_stacked:
        if node_is_stacked:
            cloner_mod["is_stacked_cloner"] = True
            print(f"[DEBUG] update_cloner_with_effectors: Синхронизирован флаг is_stacked_cloner в модификаторе")
        elif mod_is_stacked:
            cloner_mod.node_group["is_stacked_cloner"] = True
            print(f"[DEBUG] update_cloner_with_effectors: Синхронизирован флаг is_stacked_cloner в нод-группе")
        
        # Обновляем значение после синхронизации
        is_stacked_cloner = True
    
    print(f"[DEBUG] update_cloner_with_effectors: Обработка {'стекового' if is_stacked_cloner else 'обычного'} клонера {cloner_mod.name}")
    print(f"[DEBUG] update_cloner_with_effectors: mod_is_stacked={mod_is_stacked}, node_is_stacked={node_is_stacked}")
    
    # Обновляем группу узлов клонера
    node_group = cloner_mod.node_group
    linked_effectors = node_group.get("linked_effectors", [])
    print(f"[DEBUG] update_cloner_with_effectors: Связанные эффекторы: {linked_effectors}")
    
    # Проверяем валидность списка эффекторов
    valid_linked_effectors = []
    for eff_name in linked_effectors:
        eff_mod = obj.modifiers.get(eff_name)
        if eff_mod is not None and eff_mod.type == 'NODES' and eff_mod.node_group:
            # Проверяем, что этот эффектор реально является эффектором
            # Для стековых клонеров мы не проверяем имя нод-группы, так как оно может отличаться
            # от стандартного, но проверяем наличие параметров эффектора
            if is_stacked_cloner:
                # Для стековых клонеров достаточно проверить, что модификатор существует и имеет нод-группу
                is_effector = True
                # Дополнительная проверка по имени модификатора
                if any(eff_mod.name.startswith(p) for p in ['Random Effector', 'Noise Effector']):
                    is_effector = True
                # Дополнительно проверяем наличие характерных параметров эффектора
                if hasattr(eff_mod.node_group, "interface") and hasattr(eff_mod.node_group.interface, "items_tree"):
                    param_names = [socket.name for socket in eff_mod.node_group.interface.items_tree 
                                 if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT']
                    if 'Enable' in param_names and 'Strength' in param_names:
                        is_effector = True
            else:
                # Для обычных клонеров проверяем имя нод-группы
                is_effector = any(eff_mod.node_group.name.startswith(p) for p in EFFECTOR_NODE_GROUP_PREFIXES)
                
            if is_effector:
                valid_linked_effectors.append(eff_name)
                print(f"[DEBUG] update_cloner_with_effectors: Валидный эффектор: {eff_name}")
            else:
                print(f"[DEBUG] update_cloner_with_effectors: Невалидный эффектор (не является эффектором): {eff_name}")
        else:
            print(f"[DEBUG] update_cloner_with_effectors: Невалидный эффектор (не найден или нет нод-группы): {eff_name}")
    
    # Если список изменился, обновляем его
    if len(valid_linked_effectors) != len(linked_effectors):
        print(f"[DEBUG] update_cloner_with_effectors: Обновляем список эффекторов с {len(linked_effectors)} на {len(valid_linked_effectors)}")
        node_group["linked_effectors"] = valid_linked_effectors
        linked_effectors = valid_linked_effectors
    
    # Сначала получим список всех связанных эффекторов (старых)
    old_effectors = []
    effector_nodes = [n for n in node_group.nodes if n.name.startswith('Effector_')]
    for node in effector_nodes:
        effector_name = node.name.replace('Effector_', '')
        if effector_name not in old_effectors:
            old_effectors.append(effector_name)
    
    # Проверка на стековый клонер
    print(f"[DEBUG] update_cloner_with_effectors: Стековый клонер: {is_stacked_cloner}")
    
    # Проверка на пустой список эффекторов
    if not linked_effectors:
        # Если нет эффекторов, проверим, не осталось ли от предыдущих связей
        # Найдем старые узлы эффекторов и удалим их
        if effector_nodes:
            print(f"[DEBUG] update_cloner_with_effectors: Нет эффекторов, удаляем старые узлы ({len(effector_nodes)})")
            # Восстановим прямую связь от основной геометрии к выходу
            restore_direct_connection(node_group)
            
            # Удаляем старые узлы эффекторов
            for node in effector_nodes:
                try:
                    node_group.nodes.remove(node)
                except Exception as e:
                    print(f"Ошибка при удалении узла: {e}")
                
            # Включим видимость всех отвязанных эффекторов (только рендер)
            for effector_name in old_effectors:
                effector_mod = obj.modifiers.get(effector_name)
                if effector_mod:
                    # Включаем рендер эффектора, т.к. он был отвязан
                    effector_mod.show_render = True
        return
        
    # Находим новые и удаляемые эффекторы для управления видимостью
    to_add = [e for e in linked_effectors if e not in old_effectors]
    to_remove = [e for e in old_effectors if e not in linked_effectors]
    print(f"[DEBUG] update_cloner_with_effectors: Добавляем {len(to_add)} эффекторов, удаляем {len(to_remove)}")
    
    # Особая обработка для стековых клонеров
    if is_stacked_cloner:
        print(f"[DEBUG] update_cloner_with_effectors: Применяем особую обработку для стекового клонера")
        
        # Устанавливаем безопасные начальные значения для стекового клонера,
        # чтобы геометрия не исчезала при применении эффекторов с нулевыми значениями
        try:
            # Более надежный подход - устанавливаем масштаб напрямую
            # Находим сокет Instance Scale в интерфейсе
            scale_socket_id = None
            
            # Сначала ищем по имени в интерфейсе
            for socket in cloner_mod.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name == "Instance Scale":
                        scale_socket_id = socket.identifier
                        break
            
            # Если не нашли по имени, ищем Socket_5 напрямую
            if not scale_socket_id and 'Socket_5' in cloner_mod:
                scale_socket_id = 'Socket_5'
            
            # Устанавливаем масштаб
            if scale_socket_id:
                # Определяем текущее значение
                current_scale = cloner_mod[scale_socket_id]
                
                # Проверяем, является ли это значение вектором/массивом
                if hasattr(current_scale, "__len__") and len(current_scale) >= 3:
                    # Создаем новый вектор масштаба на основе текущего, 
                    # но заменяем нулевые или очень маленькие значения на 1.0
                    new_scale = list(current_scale)
                    for i in range(min(len(new_scale), 3)):
                        if abs(new_scale[i]) < 0.0001:
                            new_scale[i] = 1.0
                    
                    # Преобразуем список обратно в кортеж и устанавливаем
                    cloner_mod[scale_socket_id] = tuple(new_scale)
                    print(f"[DEBUG] update_cloner_with_effectors: Установлен безопасный масштаб: {tuple(new_scale)}")
                # Если значение не является вектором, устанавливаем (1,1,1)
                else:
                    cloner_mod[scale_socket_id] = (1.0, 1.0, 1.0)
                    print(f"[DEBUG] update_cloner_with_effectors: Установлен стандартный масштаб (1,1,1)")
                    
            # Также проверяем Random Scale и другие параметры, которые могут влиять на видимость
            for socket_name in ['Socket_11', 'Random Scale']:
                try:
                    for socket in cloner_mod.node_group.interface.items_tree:
                        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == "Random Scale":
                            # Ограничиваем значение Random Scale, чтобы оно не приводило к нулевому масштабу
                            random_scale = cloner_mod[socket.identifier]
                            if abs(random_scale) > 0.9:  # Если Random Scale слишком большой
                                cloner_mod[socket.identifier] = 0.5  # Устанавливаем более безопасное значение
                                print(f"[DEBUG] update_cloner_with_effectors: Ограничен Random Scale до 0.5")
                            break
                except Exception as e:
                    print(f"[DEBUG] update_cloner_with_effectors: Ошибка при проверке Random Scale: {e}")
                    
            # Дополнительно, активируем опцию "Use Effector" если она есть
            for socket in cloner_mod.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == "Use Effector":
                    try:
                        cloner_mod[socket.identifier] = True
                        print(f"[DEBUG] update_cloner_with_effectors: Активирован параметр Use Effector")
                    except:
                        pass
                    break
                    
        except Exception as e:
            print(f"[DEBUG] update_cloner_with_effectors: Ошибка при установке безопасных значений: {e}")
            # Этот блок кода всегда работает - даже если предыдущий код вызвал исключение
            try:
                # Попытка прямой установки масштаба через Socket_5
                if 'Socket_5' in cloner_mod:
                    cloner_mod['Socket_5'] = (1.0, 1.0, 1.0)
                    print(f"[DEBUG] update_cloner_with_effectors: Установлен резервный безопасный масштаб (1,1,1) для Socket_5")
            except Exception as e2:
                print(f"[DEBUG] update_cloner_with_effectors: Ошибка при установке резервного масштаба: {e2}")
        
        # Для стековых клонеров применяем особую обработку для каждого эффектора:
        # - Обновляем UI параметры эффектора
        # - Применяем эффекторы через драйверы
        for effector_name in linked_effectors:
            effector_mod = obj.modifiers.get(effector_name)
            if effector_mod:
                # Включаем видимость эффектора во вьюпорте для настройки
                effector_mod.show_viewport = True
                # Отключаем рендер эффектора, т.к. его эффект уже применен через клонер
                effector_mod.show_render = False
                
                # Включаем эффектор через его параметры
                if effector_mod.node_group:
                    for socket in effector_mod.node_group.interface.items_tree:
                        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                            if socket.name == "Enable":
                                try:
                                    effector_mod[socket.identifier] = True
                                except:
                                    pass
                            elif socket.name == "Strength":
                                try:
                                    effector_mod[socket.identifier] = 1.0
                                except:
                                    pass
                
                # Применяем эффектор через драйверы
                print(f"[DEBUG] update_cloner_with_effectors: Вызов apply_effector_to_stacked_cloner для {effector_name}")
                apply_effector_to_stacked_cloner(obj, cloner_mod, effector_mod)
        
        # Для стековых клонеров не модифицируем нод-группу дальше
        print(f"[DEBUG] update_cloner_with_effectors: Завершаем обработку стекового клонера после применения драйверов")
        return
    
    # Далее идет код для обычных (не стековых) клонеров
    # ...
    # Этот код длинный, поэтому для упрощения здесь представлена основная логика
    # При необходимости можно добавить оставшуюся часть функции

def restore_direct_connection(node_group):
    """
    Восстанавливает прямую связь между основной геометрией клонера и выходным узлом.
    Используется при удалении всех эффекторов.
    """
    # Найдем выходной узел
    group_output = None
    for node in node_group.nodes:
        if node.type == 'GROUP_OUTPUT':
            group_output = node
            break
    
    if not group_output:
        return
    
    # Найдем последний узел трансформации клонера
    for node in node_group.nodes:
        # Ищем узел Transform или TransformGeometry
        if 'Transform' in node.bl_idname and node.type != 'GROUP_OUTPUT':
            # Проверяем, что у него есть выход Geometry
            if 'Geometry' in [s.name for s in node.outputs]:
                # Создаем прямую связь с выходом
                node_group.links.new(node.outputs['Geometry'], group_output.inputs['Geometry'])
                return
    
    # Если не нашли узел трансформации, ищем любой узел с выходом Geometry
    for node in node_group.nodes:
        if node.type != 'GROUP_OUTPUT' and 'Geometry' in [s.name for s in node.outputs]:
            node_group.links.new(node.outputs['Geometry'], group_output.inputs['Geometry'])
            return

def apply_effector_to_stacked_cloner(obj, cloner_mod, effector_mod):
    """Применяет параметры эффектора к стековому клонеру
    
    Args:
        obj: Объект, содержащий модификаторы
        cloner_mod: Модификатор стекового клонера
        effector_mod: Модификатор эффектора
        
    Returns:
        bool: True если эффектор успешно применен, False в случае ошибки
    """
    
    # Сохраняем важные настройки клонера чтобы они не потерялись
    cloner_settings = {}
    if cloner_mod.node_group:
        # Сохраняем тип клонера, чтобы его можно было восстановить
        if cloner_mod.get("cloner_type"):
            cloner_settings["cloner_type"] = cloner_mod["cloner_type"]
        elif cloner_mod.node_group.get("cloner_type"):
            cloner_settings["cloner_type"] = cloner_mod.node_group["cloner_type"]
            # Синхронизируем тип клонера
            cloner_mod["cloner_type"] = cloner_mod.node_group["cloner_type"]
            
        # Сохраняем флаг стекового клонера
        cloner_settings["is_stacked_cloner"] = True
        cloner_mod["is_stacked_cloner"] = True
        cloner_mod.node_group["is_stacked_cloner"] = True
        
        # Сохраняем важные параметры стекового клонера по типу
        cloner_type = cloner_settings.get("cloner_type", "")
        
        # Проверяем исходные имена параметров в клонере
        socket_params = {}
        for socket in cloner_mod.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                socket_params[socket.name] = socket.identifier
    
    # Фиксируем тип клонера, если он не определен
    if cloner_mod.get("is_stacked_cloner", False) and not cloner_mod.get("cloner_type"):
        # Определяем тип по имени группы
        node_group_name = cloner_mod.node_group.name
        if "_Grid_" in node_group_name or "Grid_Stack_" in node_group_name:
            cloner_mod["cloner_type"] = "GRID"
        elif "_Linear_" in node_group_name or "Linear_Stack_" in node_group_name:
            cloner_mod["cloner_type"] = "LINEAR"
        elif "_Circle_" in node_group_name or "Circle_Stack_" in node_group_name:
            cloner_mod["cloner_type"] = "CIRCLE"
    
    # Если тип клонера определен в node_group, но не в модификаторе, синхронизируем
    if not cloner_mod.get("cloner_type") and cloner_mod.node_group.get("cloner_type"):
        cloner_mod["cloner_type"] = cloner_mod.node_group["cloner_type"]
    
    # Активируем сокет Use Effector для стекового клонера
    # Это нужно для правильной работы эффекторов
    use_effector_activated = False
    try:
        # Найдем сокет Use Effector в интерфейсе клонера
        use_effector_socket = None
        for socket in cloner_mod.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == "Use Effector":
                use_effector_socket = socket.identifier
                break
                
        # Если нашли сокет, активируем его
        if use_effector_socket:
            cloner_mod[use_effector_socket] = True
            use_effector_activated = True
            print(f"[DEBUG] Активирован сокет Use Effector ({use_effector_socket}) для {cloner_mod.name}")
        else:
            # Попробуем найти сокет по имени напрямую
            try:
                cloner_mod["Use Effector"] = True
                use_effector_activated = True
                print(f"[DEBUG] Активирован сокет Use Effector (прямой доступ) для {cloner_mod.name}")
            except Exception as inner_e:
                print(f"[DEBUG] Не найден сокет Use Effector для {cloner_mod.name}: {inner_e}")
    except Exception as e:
        print(f"[DEBUG] Ошибка при активации сокета Use Effector: {e}")
        
    # Если не удалось активировать Use Effector, пробуем найти его по индексу
    if not use_effector_activated:
        try:
            # Типичные индексы для Use Effector в разных типах клонеров
            common_indices = ["Socket_12", "Socket_13", "Socket_14", "Socket_15"]
            for idx in common_indices:
                try:
                    if idx in cloner_mod:
                        current_val = cloner_mod[idx]
                        # Если это булево значение, вероятно это Use Effector
                        if isinstance(current_val, bool) or current_val in [0, 1]:
                            cloner_mod[idx] = True
                            use_effector_activated = True
                            print(f"[DEBUG] Активирован предполагаемый сокет Use Effector ({idx}) для {cloner_mod.name}")
                            break
                except:
                    continue
        except Exception as e:
            print(f"[DEBUG] Ошибка при попытке активации Use Effector по индексу: {e}")
    
    # Используем альтернативный подход, основанный на старой версии кода,
    # для более стабильной работы с эффекторами
    try:
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Применение {effector_mod.name} к {cloner_mod.name} (старый метод)")
        
        # Проверяем наличие необходимых элементов
        if not cloner_mod.node_group or not effector_mod.node_group:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Нет node_group в клонере или эффекторе")
            return False
            
        # Получаем группы узлов
        cloner_group = cloner_mod.node_group
        effector_group = effector_mod.node_group
        
        # Проверяем наличие нужных входов/выходов в эффекторе
        input_sockets = [s.name for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']
        output_sockets = [s.name for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'OUTPUT']
        
        # Убеждаемся, что у эффектора есть входы/выходы
        if 'Geometry' not in input_sockets or 'Geometry' not in output_sockets:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Эффектор не имеет нужных сокетов Geometry")
            return False
        
        # Проверяем, существует ли уже узел этого эффектора в клонере
        existing_effector_node = None
        for node in cloner_group.nodes:
            if node.name == f"Effector_{effector_mod.name}":
                existing_effector_node = node
                break
                
        # Если узел уже существует, обновляем его параметры
        if existing_effector_node:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Обновляем существующий узел эффектора")
            # Обновляем параметры
            for input_socket in [s for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']:
                if input_socket.name in ['Geometry']:
                    continue  # Пропускаем вход геометрии
                
                # Если параметр не имеет установленного значения в модификаторе, пропускаем
                if input_socket.identifier not in effector_mod:
                    continue
                    
                # Копируем значение параметра из модификатора в узел
                try:
                    existing_effector_node.inputs[input_socket.name].default_value = effector_mod[input_socket.identifier]
                except (KeyError, TypeError) as e:
                    print(f"[DEBUG] apply_effector_to_stacked_cloner: Не удалось установить значение для {input_socket.name}: {e}")
                    pass
            
            return True
        
        # Создаем новый узел эффектора
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Создаем новый узел эффектора")
        
        # Найдем выходной узел и его входящую связь
        group_output = None
        for node in cloner_group.nodes:
            if node.type == 'GROUP_OUTPUT':
                group_output = node
                break
        
        if not group_output:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Нет выходного узла в клонере")
            return False
        
        # Найдем последний узел трансформации или первый с геометрией перед выходом
        source_node = None
        source_socket = None
        
        # Сначала ищем узлы трансформации с выходом Geometry
        for node in cloner_group.nodes:
            if node != group_output and not node.name.startswith('Effector_'):
                for output in node.outputs:
                    if output.name == 'Geometry' and any(link.to_node == group_output for link in output.links):
                        source_node = node
                        source_socket = output
                        break
                if source_node:
                    break
                    
        # Если не нашли, ищем любой узел перед выходом с геометрией
        if not source_node:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Не найден источник геометрии в клонере")
            return False
            
        # Создаем новый узел эффектора
        effector_node = cloner_group.nodes.new('GeometryNodeGroup')
        effector_node.name = f"Effector_{effector_mod.name}"
        effector_node.node_tree = effector_group
        
        # Устанавливаем положение узла между источником и выходом
        source_pos = source_node.location
        output_pos = group_output.location
        effector_node.location = (source_pos.x + (output_pos.x - source_pos.x) * 0.5, source_pos.y)
        
        # Копируем значения параметров из модификатора эффектора
        for input_socket in [s for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']:
            if input_socket.name in ['Geometry']:
                continue  # Пропускаем вход геометрии
            
            # Если параметр не имеет установленного значения в модификаторе, пропускаем
            if input_socket.identifier not in effector_mod:
                continue
                
            # Копируем значение параметра из модификатора в узел
            try:
                effector_node.inputs[input_socket.name].default_value = effector_mod[input_socket.identifier]
            except (KeyError, TypeError) as e:
                print(f"[DEBUG] apply_effector_to_stacked_cloner: Не удалось установить значение для {input_socket.name}: {e}")
                pass
                
        # Удаляем существующую связь от источника к выходу
        links_to_remove = []
        for link in cloner_group.links:
            if link.from_node == source_node and link.to_node == group_output:
                links_to_remove.append(link)
        
        for link in links_to_remove:
            try:
                cloner_group.links.remove(link)
            except RuntimeError:
                pass
                
        # Создаем новые связи: источник -> эффектор -> выход
        cloner_group.links.new(source_socket, effector_node.inputs['Geometry'])
        cloner_group.links.new(effector_node.outputs['Geometry'], group_output.inputs['Geometry'])
        
        # Отключаем рендер эффектора, т.к. его эффект уже применен через клонер
        effector_mod.show_render = False
        
        # Убедимся, что сохранены флаги стекового клонера
        cloner_mod["is_stacked_cloner"] = True
        cloner_group["is_stacked_cloner"] = True
        
        # Получаем тип клонера
        cloner_type = cloner_mod.get("cloner_type", "")
        if not cloner_type and cloner_group.get("cloner_type"):
            cloner_type = cloner_group["cloner_type"]
            
        # Если определен тип клонера, фиксируем его
        if cloner_type:
            cloner_mod["cloner_type"] = cloner_type
            cloner_group["cloner_type"] = cloner_type
        
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Эффектор успешно применен")
        return True
        
    except Exception as e:
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Ошибка при применении старого метода: {e}")
        import traceback
        traceback.print_exc()
        
        # Восстанавливаем прямую связь в случае ошибки
        try:
            source_node = None
            source_socket = None
            group_output = None
            
            # Находим выходной узел
            for node in cloner_mod.node_group.nodes:
                if node.type == 'GROUP_OUTPUT':
                    group_output = node
                    break
                    
            # Находим последний узел трансформации с геометрией
            for node in cloner_mod.node_group.nodes:
                if node != group_output and not node.name.startswith('Effector_'):
                    for output in node.outputs:
                        if output.name == 'Geometry':
                            source_node = node
                            source_socket = output
                            break
                    if source_node:
                        break
                        
            # Восстанавливаем прямую связь
            if source_node and group_output:
                cloner_mod.node_group.links.new(source_socket, group_output.inputs['Geometry'])
                print(f"[DEBUG] apply_effector_to_stacked_cloner: Восстановлена прямая связь после ошибки")
        except Exception as restore_e:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Ошибка при восстановлении связи: {restore_e}")
        
        return False
        
    # Если обработчик заблокирован, проверяем, не вызваны ли мы из обработчика
    global _effector_handler_blocked
    
    # Сохраняем предыдущее состояние блокировки
    previous_blocked_state = _effector_handler_blocked
    # Устанавливаем блокировку на время работы функции
    _effector_handler_blocked = True
    
    # Восстанавливаем предыдущее состояние блокировки
    _effector_handler_blocked = previous_blocked_state
    
    return True 