"""
Common controls for cloner interfaces.

This module provides UI drawing functions for common cloner controls,
including transform, position, random, and effector settings.
"""

# Removed unused imports: bpy, display_socket_prop, get_stacked_cloner_info
# These were used in deleted functions


# ===== УСТАРЕВШИЕ ФУНКЦИИ УДАЛЕНЫ =====
#
# Следующие функции были удалены после миграции на новую систему параметров:
# - draw_common_cloner_settings() - заменена автоматической генерацией UI
# - draw_transform_settings() - заменена автоматической генерацией UI
# - draw_position_settings() - заменена автоматической генерацией UI
# - draw_random_settings() - заменена автоматической генерацией UI
# - draw_effector_settings() - заменена на _draw_effector_controls() в settings_panels.py
#
# Все эти функции дублировали функциональность новой системы generate_ui_from_parameters()
# и больше не используются в коде.


def _draw_linked_effectors(effector_box, modifier):
    """Отображает список подключенных эффекторов"""
    # Получаем информацию о подключенных эффекторах
    if modifier.node_group and "linked_effectors" in modifier.node_group:
        linked_effectors = list(modifier.node_group["linked_effectors"])

        if linked_effectors:
            effector_box.label(text="Linked Effectors:")

            # Создаем список эффекторов
            for eff_name in linked_effectors:
                row = effector_box.row()
                # Используем split для выравнивания и добавления кнопки удаления справа
                split = row.split(factor=0.85)
                split.label(text=eff_name, icon='FORCE_FORCE')

                # Кнопка для отвязки эффектора
                unlink_op = split.operator("object.unlink_effector", text="", icon='X')
                unlink_op.effector_name = eff_name
                unlink_op.cloner_name = modifier.name
    else:
        effector_box.label(text="No effector support")


def _draw_effector_linking(effector_box, modifier, context, is_chain_menu):
    """Отображает интерфейс для связывания эффекторов"""
    if not modifier.node_group or "linked_effectors" not in modifier.node_group:
        return

    linked_effectors = list(modifier.node_group["linked_effectors"])

    # В меню цепочки клонеров не отображаем создание новых эффекторов
    # Это предотвратит проблемы с созданием эффекторов для неактивных объектов
    if is_chain_menu:
        # Показываем сообщение о том, что нужно создавать эффекторы через обычное меню
        effector_box.label(text="Use main panel to add/link effectors", icon='INFO')
        return

    # Collect all effector modifiers
    obj = context.active_object
    if not obj:
        return

    available_effectors = []

    # Collect all NODE modifiers with "Effector" in their name
    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group and "Effector" in mod.node_group.name:
            # Skip effectors that are already linked
            if mod.name in linked_effectors:
                continue
            available_effectors.append(mod.name)

    # Show linking option only if there are available effectors
    if available_effectors:
        effector_box.separator()
        effector_box.label(text="Link Effector:")

        # Create a row for the dropdown and button
        row = effector_box.row(align=True)

        # Use prop_search for selecting an effector
        row.prop_search(
            context.scene,
            "effector_to_link",
            obj,
            "modifiers",
            text=""
        )

        # Add button to link the selected effector (if one is selected)
        if context.scene.effector_to_link and context.scene.effector_to_link in available_effectors:
            op = row.operator("object.cloner_link_effector", text="Link", icon="LINKED")
            op.cloner_name = modifier.name
            op.effector_name = context.scene.effector_to_link


# ===== УСТАРЕВШИЕ HELPER ФУНКЦИИ УДАЛЕНЫ =====
#
# Следующие helper функции были удалены, так как больше не используются:
# - _is_circle_cloner() - использовалась в устаревших функциях отображения
# - _is_linear_or_circle_cloner() - использовалась в устаревших функциях отображения


def register():
    """Register common controls components"""
    pass


def unregister():
    """Unregister common controls components"""
    pass
