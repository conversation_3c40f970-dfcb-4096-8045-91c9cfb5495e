"""
Parameter display utilities for cloner interfaces.

This module provides functions for displaying and managing cloner parameters,
including socket properties and parameter groups.
"""

import bpy
from ...common.ui_utils import display_socket_prop, find_socket_by_name


def display_cloner_parameter(layout, modifier, parameter_name, display_name=None, **kwargs):
    """
    Отображает параметр клонера с автоматическим поиском сокета.

    Args:
        layout: UI layout для отрисовки
        modifier: Модификатор клонера
        parameter_name (str): Имя параметра для поиска
        display_name (str, optional): Отображаемое имя параметра
        **kwargs: Дополнительные аргументы для display_socket_prop

    Returns:
        bool: True если параметр найден и отображен
    """
    if not display_name:
        display_name = parameter_name

    return display_socket_prop(layout, modifier, parameter_name, text=display_name, **kwargs)


def display_parameter_group(layout, modifier, group_name, parameters, icon=None):
    """
    Отображает группу параметров в отдельном блоке.

    Args:
        layout: UI layout для отрисовки
        modifier: Модификатор клонера
        group_name (str): Название группы
        parameters (list): Список параметров [(name, display_name), ...]
        icon (str, optional): Иконка для группы
    """
    box = layout.box()
    if icon:
        box.label(text=group_name, icon=icon)
    else:
        box.label(text=group_name)

    col = box.column(align=True)

    for param_info in parameters:
        if isinstance(param_info, tuple):
            param_name, display_name = param_info
        else:
            param_name = param_info
            display_name = param_info

        display_cloner_parameter(col, modifier, param_name, display_name)


def display_socket_parameter(layout, modifier, socket_name, **kwargs):
    """
    Отображает параметр сокета с дополнительными опциями.

    Args:
        layout: UI layout для отрисовки
        modifier: Модификатор клонера
        socket_name (str): Имя сокета
        **kwargs: Дополнительные аргументы

    Returns:
        bool: True если сокет найден и отображен
    """
    socket_id = find_socket_by_name(modifier, socket_name)
    if not socket_id:
        return False

    # Получаем дополнительные параметры
    text = kwargs.get('text', socket_name)
    enabled = kwargs.get('enabled', True)

    # Создаем элемент UI
    if enabled:
        layout.prop(modifier, f'["{socket_id}"]', text=text)
    else:
        row = layout.row()
        row.enabled = False
        row.prop(modifier, f'["{socket_id}"]', text=text)

    return True


def get_parameter_value(modifier, parameter_name):
    """
    Получает значение параметра клонера.

    Args:
        modifier: Модификатор клонера
        parameter_name (str): Имя параметра

    Returns:
        Значение параметра или None если не найден
    """
    socket_id = find_socket_by_name(modifier, parameter_name)
    if not socket_id:
        return None

    try:
        return modifier[socket_id]
    except KeyError:
        return None


def set_parameter_value(modifier, parameter_name, value):
    """
    Устанавливает значение параметра клонера.

    UPDATED: Теперь использует новую систему параметров для клонеров,
    с fallback на старый метод для эффекторов и полей.

    Args:
        modifier: Модификатор клонера
        parameter_name (str): Имя параметра
        value: Новое значение параметра

    Returns:
        bool: True если значение установлено успешно
    """
    # Check if this is a cloner using new parameter system
    if modifier.node_group and any(cloner_type in modifier.node_group.name for cloner_type in ['Circle', 'Grid', 'Linear', 'Spiral']):
        try:
            from ....core.parameters.value_setter import ValueSetter
            setter = ValueSetter(modifier)
            return setter.override_parameter_value(parameter_name, value)
        except Exception as e:
            print(f"Error using new parameter system for {parameter_name}: {e}")
            # Fall back to old method

    # Legacy method for effectors, fields, and old cloners
    socket_id = find_socket_by_name(modifier, parameter_name)
    if not socket_id:
        return False

    try:
        modifier[socket_id] = value
        return True
    except (KeyError, TypeError):
        return False


def display_vector_parameter(layout, modifier, parameter_name, labels=None, **kwargs):
    """
    Отображает векторный параметр с отдельными компонентами.

    Args:
        layout: UI layout для отрисовки
        modifier: Модификатор клонера
        parameter_name (str): Имя векторного параметра
        labels (list, optional): Метки для компонентов [X, Y, Z]
        **kwargs: Дополнительные аргументы
    """
    if not labels:
        labels = ["X", "Y", "Z"]

    socket_id = find_socket_by_name(modifier, parameter_name)
    if not socket_id:
        return False

    # Создаем заголовок
    layout.label(text=kwargs.get('text', parameter_name))

    # Отображаем компоненты
    for i, label in enumerate(labels):
        if i < 3:  # Максимум 3 компонента
            row = layout.row(align=True)
            row.prop(modifier, f'["{socket_id}"]', index=i, text=label)

    return True


def display_parameter_with_toggle(layout, modifier, parameter_name, toggle_name, **kwargs):
    """
    Отображает параметр с переключателем включения/выключения.

    Args:
        layout: UI layout для отрисовки
        modifier: Модификатор клонера
        parameter_name (str): Имя основного параметра
        toggle_name (str): Имя параметра-переключателя
        **kwargs: Дополнительные аргументы
    """
    # Отображаем переключатель
    toggle_displayed = display_cloner_parameter(layout, modifier, toggle_name)

    if toggle_displayed:
        # Проверяем состояние переключателя
        toggle_value = get_parameter_value(modifier, toggle_name)

        # Отображаем основной параметр только если переключатель включен
        if toggle_value:
            display_cloner_parameter(layout, modifier, parameter_name, **kwargs)
    else:
        # Если переключатель не найден, просто отображаем параметр
        display_cloner_parameter(layout, modifier, parameter_name, **kwargs)


def display_conditional_parameter(layout, modifier, parameter_name, condition_func, **kwargs):
    """
    Отображает параметр только при выполнении условия.

    Args:
        layout: UI layout для отрисовки
        modifier: Модификатор клонера
        parameter_name (str): Имя параметра
        condition_func: Функция условия, принимающая modifier
        **kwargs: Дополнительные аргументы
    """
    if condition_func(modifier):
        return display_cloner_parameter(layout, modifier, parameter_name, **kwargs)
    return False


def get_all_parameter_names(modifier):
    """
    Получает список всех доступных параметров модификатора.

    Args:
        modifier: Модификатор клонера

    Returns:
        list: Список имен параметров
    """
    if not modifier.node_group:
        return []

    parameter_names = []

    # Получаем все сокеты из node_group
    if hasattr(modifier.node_group, "interface") and hasattr(modifier.node_group.interface, "items_tree"):
        for socket in modifier.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                parameter_names.append(socket.name)

    return parameter_names


def register():
    """Register parameter display components"""
    pass


def unregister():
    """Unregister parameter display components"""
    pass
