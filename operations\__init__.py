"""
Operations for Advanced Cloners addon.
"""

import bpy
from .cloner_ops import *  # Импортируем все операторы клонеров
from .effector_ops import * # Импортируем все операторы эффекторов
# Не импортируем .field_ops, так как его операторы дублируют операторы в ui/operators/field_ui_ops.py

def register():
    # Регистрируем нужные операторы
    from .cloner_ops import CLONER_OT_create_cloner, CLONER_OT_delete_cloner, CLONER_OT_move_modifier
    from .effector_ops import EFFECTOR_OT_create_effector, EFFECTOR_OT_delete_effector, EFFECTOR_OT_move_modifier

    # Регистрируем классы операторов
    bpy.utils.register_class(CLONER_OT_create_cloner)
    bpy.utils.register_class(CLONER_OT_delete_cloner)
    bpy.utils.register_class(CLONER_OT_move_modifier)
    bpy.utils.register_class(EFFECTOR_OT_create_effector)
    bpy.utils.register_class(EFFECTOR_OT_delete_effector)
    bpy.utils.register_class(EFFECTOR_OT_move_modifier)

def unregister():
    # Отмена регистрации в обратном порядке
    from .cloner_ops import CLONER_OT_create_cloner, CLONER_OT_delete_cloner, CLONER_OT_move_modifier
    from .effector_ops import EFFECTOR_OT_create_effector, EFFECTOR_OT_delete_effector, EFFECTOR_OT_move_modifier

    # Отменяем регистрацию классов операторов
    bpy.utils.unregister_class(EFFECTOR_OT_move_modifier)
    bpy.utils.unregister_class(EFFECTOR_OT_delete_effector)
    bpy.utils.unregister_class(EFFECTOR_OT_create_effector)
    bpy.utils.unregister_class(CLONER_OT_move_modifier)
    bpy.utils.unregister_class(CLONER_OT_delete_cloner)
    bpy.utils.unregister_class(CLONER_OT_create_cloner)