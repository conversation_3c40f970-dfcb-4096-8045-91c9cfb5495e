"""
Grid Cloner Module

This module provides the GridCloner class and related functionality for creating
3D grid cloners with advanced features like centering, randomization, and 2D/3D modes.
"""

# Импорты bpy и mathutils удалены - не используются в этом модуле
from ..base import ClonerBase
from .logic_builder import create_logic_group
from .interface_builder import create_main_group
from ....core.registry import register_cloner


@register_cloner("GRID", "Grid Cloner", "Create a grid of instances", "MESH_GRID")
class GridCloner(ClonerBase):
    """Grid Cloner implementation with modular architecture"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core 3D grid cloner logic"""
        return create_logic_group(name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create an advanced 3D grid cloner node group with centering and 2D/3D switch"""
        return create_main_group(logic_group, name_suffix)

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create the complete grid cloner node group with logic and interface"""
        logic_group = cls.create_logic_group(name_suffix)
        main_group = cls.create_main_group(logic_group, name_suffix)
        return main_group


# Maintain backwards compatibility with the procedural interface
def create_grid_cloner_logic_group():
    """Legacy function for backwards compatibility"""
    return GridCloner.create_logic_group()


def gridcloner3d_node_group():
    """Legacy function for backwards compatibility"""
    return GridCloner.create_node_group()


# Функции register()/unregister() удалены - регистрация происходит через декораторы @register_cloner
