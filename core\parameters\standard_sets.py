"""
Standard parameter sets that are commonly used across components.

This module defines reusable parameter groups that are shared between
different component types, such as global transforms, random settings,
and instance properties.
"""

from typing import Dict, List
from .parameter_definition import (
    ParameterDefinition,
    ParameterGroup,
    ParameterType,
    ParameterSubtype
)


# Standard Global Transform parameters
STANDARD_GLOBAL_TRANSFORM = ParameterGroup(
    name="global_transform",
    description="Global transformation parameters applied to the entire component",
    ui_order=100,  # Usually displayed after main parameters
    parameters=[
        ParameterDefinition(
            name="Global Position",
            param_type=ParameterType.VECTOR,
            default_value=(0.0, 0.0, 0.0),
            subtype=ParameterSubtype.TRANSLATION,
            description="Global position offset for the entire component",
            ui_group="Global Transform",
            ui_order=1
        ),
        ParameterDefinition(
            name="Global Rotation",
            param_type=ParameterType.VECTOR,
            default_value=(0.0, 0.0, 0.0),
            subtype=ParameterSubtype.EULER,
            description="Global rotation for the entire component",
            ui_group="Global Transform",
            ui_order=2
        )
    ]
)


# Standard Random parameters
STANDARD_RANDOM = ParameterGroup(
    name="random",
    description="Random variation parameters",
    ui_order=200,
    parameters=[
        ParameterDefinition(
            name="Random Position",
            param_type=ParameterType.VECTOR,
            default_value=(0.0, 0.0, 0.0),
            min_value=0.0,
            description="Random position variation range",
            ui_group="Random",
            ui_order=1
        ),
        ParameterDefinition(
            name="Random Rotation",
            param_type=ParameterType.VECTOR,
            default_value=(0.0, 0.0, 0.0),
            subtype=ParameterSubtype.EULER,
            min_value=0.0,
            description="Random rotation variation range",
            ui_group="Random",
            ui_order=2
        ),
        ParameterDefinition(
            name="Random Scale",
            param_type=ParameterType.FLOAT,
            default_value=0.0,
            min_value=0.0,
            max_value=1.0,
            description="Random scale variation amount",
            ui_group="Random",
            ui_order=3
        ),
        ParameterDefinition(
            name="Random Seed",
            param_type=ParameterType.INT,
            default_value=0,
            min_value=0,
            max_value=10000,
            description="Seed for random number generation",
            ui_group="Random",
            ui_order=4
        ),
        ParameterDefinition(
            name="Pick Random Instance",
            param_type=ParameterType.BOOL,
            default_value=False,
            description="Randomly pick instances from collections",
            ui_group="Random",
            ui_order=5,
            is_hidden=True  # Hidden from UI - mainly for collection cloners, not commonly used
        )
    ]
)


# Standard Instance parameters
STANDARD_INSTANCE = ParameterGroup(
    name="instance",
    description="Instance transformation parameters",
    ui_order=150,
    parameters=[
        ParameterDefinition(
            name="Instance Scale",
            param_type=ParameterType.VECTOR,
            default_value=(1.0, 1.0, 1.0),
            min_value=0.0,
            description="Scale applied to each instance",
            ui_group="Instance Transform",
            ui_order=1
        ),
        ParameterDefinition(
            name="Instance Rotation",
            param_type=ParameterType.VECTOR,
            default_value=(0.0, 0.0, 0.0),
            subtype=ParameterSubtype.EULER,
            description="Rotation applied to each instance",
            ui_group="Instance Transform",
            ui_order=2
        )
    ]
)


# Standard Effector Control parameters
STANDARD_EFFECTOR_CONTROL = ParameterGroup(
    name="effector_control",
    description="Effector integration parameters",
    ui_order=300,
    parameters=[
        ParameterDefinition(
            name="Use Effector",
            param_type=ParameterType.BOOL,
            default_value=True,
            description="Enable effector influence on this component",
            ui_group="Effector Control",
            ui_order=1,
            is_hidden=True  # Hidden from UI - effector control is handled through linking system
        )
    ]
)


# Standard Input/Output parameters for different component types
STANDARD_CLONER_IO = ParameterGroup(
    name="io",
    description="Input/Output sockets for cloners",
    ui_order=0,  # Usually first
    parameters=[
        ParameterDefinition(
            name="Geometry",
            param_type=ParameterType.GEOMETRY,
            default_value=None,
            is_input=False,  # Output
            description="Generated cloned geometry"
            # Output socket must be visible for cloner logic to work
        ),
        ParameterDefinition(
            name="Object",
            param_type=ParameterType.OBJECT,
            default_value=None,
            description="Object to clone",
            ui_group="Input",
            ui_order=1,
            is_hidden=True  # Hidden from UI - object is selected automatically in addon
        ),
        ParameterDefinition(
            name="Realize Instances",
            param_type=ParameterType.BOOL,
            default_value=False,
            description="Convert instances to real geometry to prevent recursion issues",
            ui_group="Advanced",
            ui_order=100,
            is_hidden=True  # Hidden from UI - technical parameter not needed by users
        )
    ]
)


STANDARD_EFFECTOR_IO = ParameterGroup(
    name="io",
    description="Input/Output sockets for effectors",
    ui_order=0,
    parameters=[
        ParameterDefinition(
            name="Geometry",
            param_type=ParameterType.GEOMETRY,
            default_value=None,
            is_input=True,
            description="Input geometry to affect",
            ui_group="Input",
            is_hidden=False  # Изменено с True на False, чтобы сокет всегда создавался
        ),
        ParameterDefinition(
            name="Geometry",
            param_type=ParameterType.GEOMETRY,
            default_value=None,
            is_input=False,
            description="Output affected geometry",
            ui_group="Output",
            is_hidden=False  # Изменено с True на False, чтобы сокет всегда создавался
        ),
        ParameterDefinition(
            name="Enable",
            param_type=ParameterType.BOOL,
            default_value=True,
            description="Enable/disable this effector",
            ui_group="Control",
            ui_order=1
        ),
        ParameterDefinition(
            name="Strength",
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            min_value=0.0,
            max_value=1.0,
            description="Overall strength of the effector",
            ui_group="Control",
            ui_order=2
        )
    ]
)


STANDARD_FIELD_IO = ParameterGroup(
    name="io",
    description="Input/Output sockets for fields",
    ui_order=0,
    parameters=[
        ParameterDefinition(
            name="Geometry",
            param_type=ParameterType.GEOMETRY,
            default_value=None,
            is_input=True,
            description="Input geometry",
            ui_group="Input",
            is_hidden=True
        ),
        ParameterDefinition(
            name="Geometry",
            param_type=ParameterType.GEOMETRY,
            default_value=None,
            is_input=False,
            description="Output geometry",
            ui_group="Output",
            is_hidden=True
        ),
        ParameterDefinition(
            name="Value",
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            is_input=False,
            description="Field influence value",
            ui_group="Output",
            is_hidden=True
        )
    ]
)


# Standard Base Effector parameters
STANDARD_EFFECTOR_BASE = ParameterGroup(
    name="effector_base",
    description="Base parameters for all effectors",
    ui_order=1,
    parameters=[
        ParameterDefinition(
            name="Enable",
            param_type=ParameterType.BOOL,
            default_value=True,
            description="Enable or disable the effector",
            ui_group="Base",
            ui_order=1
        ),
        ParameterDefinition(
            name="Strength",
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            min_value=0.0,
            max_value=1.0,
            description="Overall strength of the effector",
            ui_group="Base",
            ui_order=2
        )
    ]
)

# Standard Transform parameters
STANDARD_TRANSFORM = ParameterGroup(
    name="transform",
    description="Standard transformation parameters",
    ui_order=5,
    parameters=[
        ParameterDefinition(
            name="Position",
            param_type=ParameterType.VECTOR,
            default_value=(0.5, 0.5, 0.5),
            description="Amount of random position offset",
            ui_group="Transform",
            ui_order=1
        ),
        ParameterDefinition(
            name="Rotation",
            param_type=ParameterType.VECTOR,
            default_value=(15.0, 15.0, 15.0),
            subtype=ParameterSubtype.EULER,
            description="Amount of random rotation",
            ui_group="Transform",
            ui_order=2
        ),
        ParameterDefinition(
            name="Scale",
            param_type=ParameterType.VECTOR,
            default_value=(0.2, 0.2, 0.2),
            description="Amount of random scaling",
            ui_group="Transform",
            ui_order=3
        )
    ]
)

# Field Control parameters
STANDARD_FIELD_CONTROL = ParameterGroup(
    name="field_control",
    description="Field influence parameters",
    ui_order=300,
    parameters=[
        ParameterDefinition(
            name="Use Field",
            param_type=ParameterType.BOOL,
            default_value=False,
            description="Use field to modulate the effector's influence",
            ui_group="Field Control",
            ui_order=1
        ),
        ParameterDefinition(
            name="Field",
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            min_value=0.0,
            max_value=1.0,
            description="Field influence factor",
            ui_group="Field Control",
            ui_order=2
        )
    ]
)

# Standard Effector Input/Output parameters
STANDARD_EFFECTOR_IO = ParameterGroup(
    name="io",
    description="Input/Output parameters for effectors",
    ui_order=0,  # Always first
    parameters=[
        ParameterDefinition(
            name="Geometry",
            param_type=ParameterType.GEOMETRY,
            default_value=None,
            description="Input geometry",
            ui_group="Input/Output",
            ui_order=1,
            is_hidden=True  # Usually not shown in UI
        ),
        ParameterDefinition(
            name="Geometry",
            param_type=ParameterType.GEOMETRY,
            default_value=None,
            description="Output geometry",
            ui_group="Input/Output",
            ui_order=2,
            is_input=False,  # This is an output
            is_hidden=True  # Usually not shown in UI
        )
    ]
)

# Registry of standard parameter sets
STANDARD_PARAMETER_SETS: Dict[str, ParameterGroup] = {
    "global_transform": STANDARD_GLOBAL_TRANSFORM,
    "random": STANDARD_RANDOM,
    "instance": STANDARD_INSTANCE,
    "effector_control": STANDARD_EFFECTOR_CONTROL,
    "cloner_io": STANDARD_CLONER_IO,
    "effector_io": STANDARD_EFFECTOR_IO,
    "field_io": STANDARD_FIELD_IO,
    "effector_base": STANDARD_EFFECTOR_BASE,
    "transform": STANDARD_TRANSFORM,
    "field_control": STANDARD_FIELD_CONTROL
}


def get_standard_parameter_set(name: str) -> ParameterGroup:
    """
    Get a standard parameter set by name.

    Args:
        name: Name of the standard parameter set

    Returns:
        ParameterGroup: The requested parameter group

    Raises:
        KeyError: If the standard parameter set doesn't exist
    """
    if name not in STANDARD_PARAMETER_SETS:
        available = ", ".join(STANDARD_PARAMETER_SETS.keys())
        raise KeyError(f"Standard parameter set '{name}' not found. Available: {available}")

    return STANDARD_PARAMETER_SETS[name]


def get_available_standard_sets() -> List[str]:
    """Get list of all available standard parameter set names."""
    return list(STANDARD_PARAMETER_SETS.keys())
