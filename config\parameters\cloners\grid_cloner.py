"""
Grid Cloner Parameter Definitions

This module defines all parameters for the Grid Cloner component using the new
unified parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

Grid Cloner creates instances in a 3D grid pattern with configurable spacing.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    get_standard_parameter_set
)


# Basic Grid Cloner parameters
GRID_BASIC_GROUP = ParameterGroup(
    name="basic",
    description="Basic grid cloner settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Count X",
            param_type=ParameterType.INT,
            default_value=3,
            min_value=1,
            max_value=100,
            description="Number of instances along X axis",
            ui_group="Basic Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Count Y",
            param_type=ParameterType.INT,
            default_value=3,
            min_value=1,
            max_value=100,
            description="Number of instances along Y axis",
            ui_group="Basic Settings",
            ui_order=2
        ),
        ParameterDefinition(
            name="Count Z",
            param_type=ParameterType.INT,
            default_value=1,
            min_value=1,
            max_value=100,
            description="Number of instances along Z axis",
            ui_group="Basic Settings",
            ui_order=3
        ),
        ParameterDefinition(
            name="Spacing",
            param_type=ParameterType.VECTOR,
            default_value=(3.0, 3.0, 3.0),
            description="Spacing between instances in each axis",
            ui_group="Basic Settings",
            ui_order=4
        ),
        ParameterDefinition(
            name="Center Grid",
            param_type=ParameterType.BOOL,
            default_value=True,
            description="Center the grid around the origin",
            ui_group="Basic Settings",
            ui_order=5,
            is_hidden=True  # Hidden from UI - enabled by default, rarely needs to be changed
        )
    ]
)

# Input/Output sockets - using standard cloner IO parameters
# GRID_IO_GROUP removed - now using get_standard_parameter_set("cloner_io")

# Complete parameter set for Grid Cloner
GRID_CLONER_PARAMETERS = ComponentParameterSet(
    component_type="CLONER",
    component_id="GRID",
    description="Grid Cloner parameter set - creates instances in a 3D grid pattern",
    version="1.0",
    groups=[
        get_standard_parameter_set("cloner_io"),  # Using standard cloner IO parameters
        GRID_BASIC_GROUP,
        get_standard_parameter_set("global_transform"),
        get_standard_parameter_set("instance"),
        get_standard_parameter_set("random"),
        get_standard_parameter_set("effector_control")
    ]
)
