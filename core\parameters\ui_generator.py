"""
Automatic UI generator for component parameters.

This module provides functionality to automatically generate Blender UI elements
based on parameter definitions, eliminating the need for manual UI creation
in settings_panels.py files.
"""

import bpy
from typing import Dict, List, Optional, Callable, Any
from .parameter_definition import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType
)
from ..utils.node_operations.node_utils import find_socket_by_name


class UIGenerator:
    """
    Generates Blender UI elements automatically from parameter definitions.
    
    This class takes parameter definitions and creates the corresponding
    UI elements (props, boxes, columns, etc.) in a Blender layout.
    """
    
    def __init__(self, layout: bpy.types.UILayout, modifier: bpy.types.NodesModifier):
        """
        Initialize UI generator for a specific layout and modifier.
        
        Args:
            layout: The Blender UI layout to generate elements in
            modifier: The modifier containing the parameters
        """
        self.layout = layout
        self.modifier = modifier
        self.generated_elements = {}  # Track generated UI elements
        self.current_group_box = None
    
    def generate_from_parameter_set(self, parameter_set: ComponentParameterSet) -> bool:
        """
        Generate complete UI from a component parameter set.
        
        Args:
            parameter_set: Complete parameter set for the component
            
        Returns:
            bool: True if UI was generated successfully
        """
        try:
            print(f"Generating UI for {parameter_set.component_type}.{parameter_set.component_id}")
            
            # Group parameters by UI group
            ui_groups = self._organize_parameters_by_ui_group(parameter_set)
            
            # Generate UI for each group
            for group_name, params in ui_groups.items():
                if not self._generate_ui_group(group_name, params):
                    print(f"Failed to generate UI for group: {group_name}")
                    return False
            
            print(f"Successfully generated UI with {len(self.generated_elements)} elements")
            return True
            
        except Exception as e:
            print(f"Error generating UI from parameter set: {e}")
            return False
    
    def generate_from_parameter_group(self, group: ParameterGroup) -> bool:
        """
        Generate UI from a single parameter group.
        
        Args:
            group: Parameter group to generate UI from
            
        Returns:
            bool: True if group UI was generated successfully
        """
        try:
            print(f"Generating UI for group: {group.name}")
            
            # Create group box if needed
            if group.name != "basic" and len(group.parameters) > 1:
                box = self.layout.box()
                box.label(text=group.description or group.name.replace("_", " ").title())
                self.current_group_box = box.column(align=True)
            else:
                self.current_group_box = self.layout.column(align=True)
            
            # Sort parameters by UI order
            sorted_params = sorted(group.parameters, key=lambda p: p.ui_order)
            
            # Generate UI for each parameter
            for param in sorted_params:
                if not self.generate_parameter_ui(param):
                    print(f"Failed to generate UI for parameter: {param.name}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"Error generating UI from parameter group {group.name}: {e}")
            return False
    
    def generate_parameter_ui(self, param: ParameterDefinition) -> bool:
        """
        Generate UI for a single parameter.
        
        Args:
            param: Parameter definition to generate UI from
            
        Returns:
            bool: True if UI was generated successfully
        """
        try:
            # Skip hidden parameters
            if param.is_hidden:
                return True
            
            # Skip output parameters (they don't need UI controls)
            if not param.is_input:
                return True
            
            # Find the socket ID
            socket_id = find_socket_by_name(self.modifier, param.name)
            if not socket_id:
                print(f"Warning: Socket not found for parameter: {param.name}")
                return False
            
            # Use current group box or main layout
            layout = self.current_group_box or self.layout
            
            # Generate UI based on parameter type
            ui_element = self._create_ui_element_for_parameter(layout, param, socket_id)
            
            if ui_element:
                self.generated_elements[param.name] = ui_element
                print(f"Generated UI for parameter: {param.name}")
                return True
            else:
                print(f"Failed to create UI element for parameter: {param.name}")
                return False
            
        except Exception as e:
            print(f"Error generating UI for parameter {param.name}: {e}")
            return False
    
    def _create_ui_element_for_parameter(
        self, 
        layout: bpy.types.UILayout, 
        param: ParameterDefinition, 
        socket_id: str
    ) -> Optional[Any]:
        """
        Create appropriate UI element for a parameter.
        
        Args:
            layout: Layout to create element in
            param: Parameter definition
            socket_id: Socket ID for the parameter
            
        Returns:
            The created UI element or None if creation failed
        """
        try:
            display_name = param.get_ui_display_name()
            
            # Create basic property element
            if param.param_type == ParameterType.VECTOR:
                # For vectors, we might want to show components separately or as a single vector
                return layout.prop(self.modifier, f'["{socket_id}"]', text=display_name)
            
            elif param.param_type == ParameterType.BOOL:
                return layout.prop(self.modifier, f'["{socket_id}"]', text=display_name)
            
            elif param.param_type in [ParameterType.INT, ParameterType.FLOAT]:
                return layout.prop(self.modifier, f'["{socket_id}"]', text=display_name)
            
            elif param.param_type == ParameterType.STRING:
                return layout.prop(self.modifier, f'["{socket_id}"]', text=display_name)
            
            elif param.param_type in [ParameterType.OBJECT, ParameterType.COLLECTION]:
                return layout.prop(self.modifier, f'["{socket_id}"]', text=display_name)
            
            else:
                # Fallback for unknown types
                return layout.prop(self.modifier, f'["{socket_id}"]', text=display_name)
            
        except Exception as e:
            print(f"Error creating UI element for {param.name}: {e}")
            return None
    
    def _organize_parameters_by_ui_group(self, parameter_set: ComponentParameterSet) -> Dict[str, List[ParameterDefinition]]:
        """
        Organize parameters by their UI groups.
        
        Args:
            parameter_set: Parameter set to organize
            
        Returns:
            Dictionary mapping UI group names to parameter lists
        """
        ui_groups = {}
        
        for group in parameter_set.groups:
            for param in group.parameters:
                # Skip hidden and output parameters
                if param.is_hidden or not param.is_input:
                    continue
                
                ui_group_name = param.ui_group or group.name
                
                if ui_group_name not in ui_groups:
                    ui_groups[ui_group_name] = []
                
                ui_groups[ui_group_name].append(param)
        
        # Sort parameters within each group by ui_order
        for group_name in ui_groups:
            ui_groups[group_name].sort(key=lambda p: p.ui_order)
        
        return ui_groups
    
    def _generate_ui_group(self, group_name: str, parameters: List[ParameterDefinition]) -> bool:
        """
        Generate UI for a group of parameters.
        
        Args:
            group_name: Name of the UI group
            parameters: List of parameters in the group
            
        Returns:
            bool: True if group UI was generated successfully
        """
        try:
            # Create group box for non-basic groups
            if group_name != "basic" and len(parameters) > 1:
                box = self.layout.box()
                box.label(text=group_name.replace("_", " ").title())
                group_layout = box.column(align=True)
            else:
                group_layout = self.layout.column(align=True)
            
            # Generate UI for each parameter
            for param in parameters:
                socket_id = find_socket_by_name(self.modifier, param.name)
                if socket_id:
                    ui_element = self._create_ui_element_for_parameter(group_layout, param, socket_id)
                    if ui_element:
                        self.generated_elements[param.name] = ui_element
            
            return True
            
        except Exception as e:
            print(f"Error generating UI group {group_name}: {e}")
            return False


def generate_ui_from_parameters(
    layout: bpy.types.UILayout,
    modifier: bpy.types.NodesModifier,
    parameter_set: ComponentParameterSet
) -> bool:
    """
    Convenience function to generate UI from parameter set.
    
    Args:
        layout: The layout to generate UI in
        modifier: The modifier containing the parameters
        parameter_set: Parameter set defining the UI
        
    Returns:
        bool: True if UI was generated successfully
    """
    generator = UIGenerator(layout, modifier)
    return generator.generate_from_parameter_set(parameter_set)


def generate_ui_from_groups(
    layout: bpy.types.UILayout,
    modifier: bpy.types.NodesModifier,
    groups: List[ParameterGroup]
) -> bool:
    """
    Convenience function to generate UI from parameter groups.
    
    Args:
        layout: The layout to generate UI in
        modifier: The modifier containing the parameters
        groups: List of parameter groups to generate UI from
        
    Returns:
        bool: True if UI was generated successfully
    """
    generator = UIGenerator(layout, modifier)
    
    for group in groups:
        if not generator.generate_from_parameter_group(group):
            return False
    
    return True
