"""
Stacked cloner integration utilities for the effector system.

This module handles the specific logic for working with stacked cloners,
which have different architecture and requirements compared to standard cloners.
"""

import bpy
from ....models.effectors import EFFECTOR_NODE_GROUP_PREFIXES


def update_stacked_cloner_with_effectors(obj, cloner_mod):
    """
    Обновляет стековый клонер с эффекторами (существующая логика).

    Args:
        obj: Объект с модификатором
        cloner_mod: Модификатор стекового клонера
    """
    # Здесь должна быть существующая логика для стековых клонеров
    # Пока используем заглушку, которая вызывает существующую функцию

    node_group = cloner_mod.node_group
    linked_effectors = node_group.get("linked_effectors", [])

    print(f"[DEBUG] Обработка стекового клонера с {len(linked_effectors)} эффекторами")

    # Применяем каждый эффектор к стековому клонеру
    for effector_name in linked_effectors:
        effector_mod = obj.modifiers.get(effector_name)
        if effector_mod:
            print(f"[DEBUG] Применение эффектора {effector_name} к стековому клонеру")
            # Импортируем функцию здесь для избежания циклической зависимости
            from .effector_application import apply_effector_to_stacked_cloner
            apply_effector_to_stacked_cloner(obj, cloner_mod, effector_mod)


def validate_stacked_cloner_structure(cloner_mod):
    """
    Проверяет правильность структуры стекового клонера.
    
    Args:
        cloner_mod: Модификатор стекового клонера
        
    Returns:
        bool: True если структура корректна
    """
    if not cloner_mod or not cloner_mod.node_group:
        return False
        
    # Проверяем флаги стекового клонера
    mod_is_stacked = cloner_mod.get("is_stacked_cloner", False)
    node_is_stacked = cloner_mod.node_group.get("is_stacked_cloner", False)
    
    if not (mod_is_stacked or node_is_stacked):
        return False
        
    # Проверяем наличие типа клонера
    cloner_type = cloner_mod.get("cloner_type") or cloner_mod.node_group.get("cloner_type")
    if not cloner_type:
        return False
        
    print(f"[DEBUG] Валидация стекового клонера: тип={cloner_type}, стековый={mod_is_stacked or node_is_stacked}")
    return True


def ensure_stacked_cloner_consistency(cloner_mod):
    """
    Обеспечивает консистентность настроек стекового клонера.
    
    Args:
        cloner_mod: Модификатор стекового клонера
    """
    if not cloner_mod or not cloner_mod.node_group:
        return
        
    # Синхронизируем флаги между модификатором и node_group
    cloner_mod["is_stacked_cloner"] = True
    cloner_mod.node_group["is_stacked_cloner"] = True
    
    # Синхронизируем тип клонера
    mod_type = cloner_mod.get("cloner_type")
    node_type = cloner_mod.node_group.get("cloner_type")
    
    if mod_type and not node_type:
        cloner_mod.node_group["cloner_type"] = mod_type
    elif node_type and not mod_type:
        cloner_mod["cloner_type"] = node_type
    elif not mod_type and not node_type:
        # Пытаемся определить тип по имени группы
        node_group_name = cloner_mod.node_group.name
        if "_Grid_" in node_group_name or "Grid_Stack_" in node_group_name:
            cloner_type = "GRID"
        elif "_Linear_" in node_group_name or "Linear_Stack_" in node_group_name:
            cloner_type = "LINEAR"
        elif "_Circle_" in node_group_name or "Circle_Stack_" in node_group_name:
            cloner_type = "CIRCLE"
        else:
            cloner_type = "GRID"  # По умолчанию
            
        cloner_mod["cloner_type"] = cloner_type
        cloner_mod.node_group["cloner_type"] = cloner_type
        
    print(f"[DEBUG] Синхронизированы настройки стекового клонера: {cloner_mod.get('cloner_type')}")


def find_stacked_cloner_sockets(cloner_mod):
    """
    Находит важные сокеты в стековом клонере.
    
    Args:
        cloner_mod: Модификатор стекового клонера
        
    Returns:
        dict: Словарь с найденными сокетами
    """
    if not cloner_mod or not cloner_mod.node_group:
        return {}
        
    sockets = {
        'use_effector': None,
        'geometry_input': None,
        'geometry_output': None
    }
    
    # Ищем сокеты в интерфейсе
    for socket in cloner_mod.node_group.interface.items_tree:
        if socket.item_type != 'SOCKET':
            continue
            
        if socket.in_out == 'INPUT':
            if socket.name == "Use Effector":
                sockets['use_effector'] = socket.identifier
            elif socket.name == "Geometry":
                sockets['geometry_input'] = socket.identifier
        elif socket.in_out == 'OUTPUT':
            if socket.name == "Geometry":
                sockets['geometry_output'] = socket.identifier
                
    return sockets


def activate_stacked_cloner_effector_mode(cloner_mod):
    """
    Активирует режим эффектора для стекового клонера.
    
    Args:
        cloner_mod: Модификатор стекового клонера
        
    Returns:
        bool: True если режим активирован успешно
    """
    sockets = find_stacked_cloner_sockets(cloner_mod)
    use_effector_socket = sockets.get('use_effector')
    
    if use_effector_socket:
        try:
            cloner_mod[use_effector_socket] = True
            print(f"[DEBUG] Активирован режим эффектора для стекового клонера: {use_effector_socket}")
            return True
        except Exception as e:
            print(f"[DEBUG] Ошибка при активации режима эффектора: {e}")
            
    # Пытаемся активировать по альтернативным путям
    return _activate_effector_mode_fallback(cloner_mod)


def _activate_effector_mode_fallback(cloner_mod):
    """Запасной метод активации режима эффектора."""
    # Попробуем найти сокет по имени напрямую
    try:
        cloner_mod["Use Effector"] = True
        print(f"[DEBUG] Активирован режим эффектора (прямой доступ)")
        return True
    except:
        pass
        
    # Попробуем типичные индексы для Use Effector
    common_indices = ["Socket_12", "Socket_13", "Socket_14", "Socket_15"]
    for idx in common_indices:
        try:
            if idx in cloner_mod:
                current_val = cloner_mod[idx]
                # Если это булево значение, вероятно это Use Effector
                if isinstance(current_val, bool) or current_val in [0, 1]:
                    cloner_mod[idx] = True
                    print(f"[DEBUG] Активирован режим эффектора через индекс: {idx}")
                    return True
        except:
            continue
            
    print(f"[DEBUG] Не удалось активировать режим эффектора для стекового клонера")
    return False


def get_stacked_cloner_info(cloner_mod):
    """
    Получает информацию о стековом клонере.
    
    Args:
        cloner_mod: Модификатор клонера
        
    Returns:
        tuple: (is_stacked, cloner_type, node_group_name)
    """
    if not cloner_mod:
        return False, None, None
        
    is_stacked = cloner_mod.get("is_stacked_cloner", False)
    if cloner_mod.node_group:
        is_stacked = is_stacked or cloner_mod.node_group.get("is_stacked_cloner", False)
        
    cloner_type = None
    if is_stacked:
        cloner_type = cloner_mod.get("cloner_type")
        if not cloner_type and cloner_mod.node_group:
            cloner_type = cloner_mod.node_group.get("cloner_type")
            
    node_group_name = cloner_mod.node_group.name if cloner_mod.node_group else None
    
    return is_stacked, cloner_type, node_group_name


def cleanup_stacked_cloner_effectors(cloner_mod, effector_names_to_remove):
    """
    Очищает эффекторы из стекового клонера.
    
    Args:
        cloner_mod: Модификатор стекового клонера
        effector_names_to_remove: Список имен эффекторов для удаления
    """
    if not cloner_mod or not cloner_mod.node_group:
        return
        
    nodes = cloner_mod.node_group.nodes
    
    # Удаляем узлы эффекторов
    nodes_to_remove = []
    for node in nodes:
        if node.name.startswith('Effector_'):
            effector_name = node.name.replace('Effector_', '')
            if effector_name in effector_names_to_remove:
                nodes_to_remove.append(node)
                
    for node in nodes_to_remove:
        try:
            nodes.remove(node)
            print(f"[DEBUG] Удален узел эффектора из стекового клонера: {node.name}")
        except:
            pass
            
    # Восстанавливаем прямые связи если все эффекторы удалены
    remaining_effectors = [n for n in nodes if n.name.startswith('Effector_')]
    if not remaining_effectors:
        print(f"[DEBUG] Восстанавливаем прямые связи в стековом клонере")
        _restore_stacked_cloner_direct_connections(cloner_mod.node_group)


def _restore_stacked_cloner_direct_connections(node_group):
    """Восстанавливает прямые связи в стековом клонере."""
    from .connection_management import restore_direct_connection_improved
    restore_direct_connection_improved(node_group)


def get_stacked_cloner_effector_chain(cloner_mod):
    """
    Получает цепочку эффекторов в стековом клонере.
    
    Args:
        cloner_mod: Модификатор стекового клонера
        
    Returns:
        list: Список имен эффекторов в порядке их применения
    """
    if not cloner_mod or not cloner_mod.node_group:
        return []
        
    effector_chain = []
    
    # Получаем все узлы эффекторов
    effector_nodes = []
    for node in cloner_mod.node_group.nodes:
        if node.name.startswith('Effector_'):
            effector_name = node.name.replace('Effector_', '')
            effector_nodes.append((node.location.x, effector_name))
            
    # Сортируем по X-координатам (слева направо)
    effector_nodes.sort(key=lambda x: x[0])
    effector_chain = [name for _, name in effector_nodes]
    
    print(f"[DEBUG] Цепочка эффекторов в стековом клонере: {effector_chain}")
    return effector_chain


def update_stacked_cloner_effector_parameters(cloner_mod, effector_name, parameters):
    """
    Обновляет параметры эффектора в стековом клонере.
    
    Args:
        cloner_mod: Модификатор стекового клонера
        effector_name: Имя эффектора
        parameters: Словарь параметров для обновления
    """
    if not cloner_mod or not cloner_mod.node_group:
        return
        
    # Ищем узел эффектора
    effector_node = None
    for node in cloner_mod.node_group.nodes:
        if node.name == f"Effector_{effector_name}":
            effector_node = node
            break
            
    if not effector_node:
        print(f"[DEBUG] Узел эффектора {effector_name} не найден в стековом клонере")
        return
        
    # Обновляем параметры
    for param_name, param_value in parameters.items():
        if param_name in effector_node.inputs:
            try:
                effector_node.inputs[param_name].default_value = param_value
                print(f"[DEBUG] Обновлен параметр {param_name} = {param_value} для эффектора {effector_name}")
            except Exception as e:
                print(f"[DEBUG] Ошибка при обновлении параметра {param_name}: {e}")
