"""
Модуль оптимизации производительности для Advanced Cloners.

Содержит высокопроизводительные системы для ускорения работы аддона:
- Пакетная обработка объектов (BatchObjectProcessor)
- Кэширование модулей (ModuleCache)
- Система кэширования данных (PerformanceCache)
"""

# Импорт системы кэширования производительности
from .performance_cache import (
    cached_function,
    clear_all_caches,
    get_cache_stats,
    PerformanceTimer,
    performance_monitor,
    fast_object_exists,
    fast_get_collections,
    fast_get_cloner_types,
    register_cache_handlers,
    unregister_cache_handlers,
    invalidate_cache_on_scene_update,
    # Глобальные кэши
    _component_cache,
    _ui_cache,
    _node_group_cache,
    _collection_cache,
    _object_cache
)

# Импорт пакетного процессора объектов
from .batch_processor import (
    BatchObjectProcessor,
    process_all_objects_batch,
    get_cached_batch_results,
    get_objects_needing_anti_recursion,
    get_objects_needing_cleanup,
    get_effector_links_info,
    get_chain_status_info
)

# Импорт кэша модулей
from .module_cache import (
    ModuleCache,
    cached_import,
    cached_function as cached_module_function,
    cached_class,
    auto_cache_imports,
    AdvancedClonersModuleCache,
    get_update_cloner_with_effectors,
    get_create_cloner_collection,
    get_restore_original_object,
    get_component_registry
)


def register():
    """Регистрирует все компоненты оптимизации"""
    register_cache_handlers()
    print("✅ Optimization components registered")


def unregister():
    """Отменяет регистрацию компонентов оптимизации"""
    unregister_cache_handlers()
    clear_all_caches()
    ModuleCache.clear_cache()
    AdvancedClonersModuleCache.clear_cache()
    print("🧹 Optimization components unregistered")


def get_optimization_stats():
    """Возвращает статистику всех систем оптимизации"""
    cache_stats = get_cache_stats()
    module_stats = ModuleCache.get_cache_stats()
    
    return {
        "performance_cache": cache_stats,
        "module_cache": module_stats,
        "total_cached_items": cache_stats["total"] + module_stats["total"]
    }


# Публичный API
__all__ = [
    # Performance Cache
    'cached_function',
    'clear_all_caches', 
    'get_cache_stats',
    'PerformanceTimer',
    'performance_monitor',
    'fast_object_exists',
    'fast_get_collections',
    'fast_get_cloner_types',
    'register_cache_handlers',
    'unregister_cache_handlers',
    
    # Batch Processor
    'BatchObjectProcessor',
    'process_all_objects_batch',
    'get_cached_batch_results',
    'get_objects_needing_anti_recursion',
    'get_objects_needing_cleanup',
    'get_effector_links_info',
    'get_chain_status_info',
    
    # Module Cache
    'ModuleCache',
    'cached_import',
    'cached_module_function',
    'cached_class',
    'auto_cache_imports',
    'AdvancedClonersModuleCache',
    'get_update_cloner_with_effectors',
    'get_create_cloner_collection',
    'get_restore_original_object',
    'get_component_registry',
    
    # Utility functions
    'register',
    'unregister',
    'get_optimization_stats'
]
