"""
Collection Cloners Module

Этот модуль предоставляет классы коллекционных клонеров для создания
Grid, Linear и Circle клонеров коллекций.

Коллекционные клонеры создают новый объект и клонируют всю коллекцию,
а не отдельные объекты.
"""

from ..base import ClonerBase
from ....core.registry import register_cloner
from .collection_logic import create_collection_cloner_logic


@register_cloner("COLLECTION_CIRCLE", "Collection Circle Cloner", "Create circle from collections", "OUTLINER_COLLECTION")
class CollectionCircleCloner(ClonerBase):
    """Collection Circle Cloner implementation for cloning entire collections"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core collection circle cloner logic"""
        # Теперь используем собственную логику вместо фабрики
        return create_collection_cloner_logic("CIRCLE", name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a collection circle cloner node group with Object input"""
        # Логика уже включена в create_collection_cloner
        return logic_group

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create complete collection circle cloner node group"""
        return cls.create_logic_group(name_suffix)


@register_cloner("COLLECTION_GRID", "Collection Grid Cloner", "Create grid from collections", "OUTLINER_COLLECTION")
class CollectionGridCloner(ClonerBase):
    """Collection Grid Cloner implementation for cloning entire collections"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core collection grid cloner logic"""
        # Теперь используем собственную логику вместо фабрики
        return create_collection_cloner_logic("GRID", name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a collection grid cloner node group with Object input"""
        # Логика уже включена в create_collection_cloner
        return logic_group

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create complete collection grid cloner node group"""
        return cls.create_logic_group(name_suffix)


@register_cloner("COLLECTION_LINEAR", "Collection Linear Cloner", "Create line from collections", "OUTLINER_COLLECTION")
class CollectionLinearCloner(ClonerBase):
    """Collection Linear Cloner implementation for cloning entire collections"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core collection linear cloner logic"""
        # Теперь используем собственную логику вместо фабрики
        return create_collection_cloner_logic("LINEAR", name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a collection linear cloner node group with Object input"""
        # Логика уже включена в create_collection_cloner
        return logic_group

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create complete collection linear cloner node group"""
        return cls.create_logic_group(name_suffix)


def register():
    """Register collection cloner components"""
    pass


def unregister():
    """Unregister collection cloner components"""
    pass
