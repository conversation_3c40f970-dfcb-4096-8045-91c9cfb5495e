"""
Модуль общих утилит.

Содержит общие вспомогательные функции, используемые в различных частях системы:
- utils: Общие утилиты (поиск коллекций, регистрация обновлений)
"""

# Реэкспорт всех функций для обратной совместимости
from .utils import (
    find_layer_collection,
    register_chain_update
)

# Публичный API
__all__ = [
    'find_layer_collection',
    'register_chain_update'
]

def register():
    """Register common utilities components"""
    pass

def unregister():
    """Unregister common utilities components"""
    pass
