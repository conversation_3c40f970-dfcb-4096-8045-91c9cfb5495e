"""
Базовые утилиты для работы с клонерами.
"""

import bpy
from ...registry import component_registry



def get_active_cloner(obj):
    """
    Получает активный модификатор клонера для объекта.

    Args:
        obj (bpy.types.Object): Объе<PERSON>т, для которого нужно получить активный клонер

    Returns:
        bpy.types.Modifier: Активный модификатор клонера или None, если не найден
    """
    if not obj:
        return None

    # Проверяем свойство active_cloner_name в объекте
    active_cloner_name = obj.get("active_cloner_name", "")
    if active_cloner_name and active_cloner_name in obj.modifiers:
        mod = obj.modifiers[active_cloner_name]
        if mod.type == 'NODES' and mod.node_group:
            # Проверяем, что это действительно клонер
            cloner_group_names = component_registry.get_cloner_group_names()
            for group_name in cloner_group_names.values():
                if group_name in mod.node_group.name:
                    return mod

    # Если active_cloner_name не установлен или недействителен,
    # ищем первый модификатор клонера
    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group:
            cloner_group_names = component_registry.get_cloner_group_names()
            for group_name in cloner_group_names.values():
                if group_name in mod.node_group.name:
                    # Запомним этот клонер как активный
                    obj["active_cloner_name"] = mod.name
                    return mod

    return None