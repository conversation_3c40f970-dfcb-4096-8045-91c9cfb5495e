"""
Operators for the Advanced Cloners addon UI.

This module has been reorganized into specialized sub-modules:
- cloner_operators: All cloner-related UI operators
- effector_operators: All effector-related UI operators
- field_operators: All field-related UI operators
"""

# Import all operators from sub-modules
from . import cloner_operators
from . import effector_operators
from . import field_operators
# Диагностические операторы удалены



# Public API
__all__ = [
    # Sub-modules
    'cloner_operators',
    'effector_operators',
    'field_operators'
]


def register():
    """Register all UI operators"""
    cloner_operators.register()
    effector_operators.register()
    field_operators.register()


def unregister():
    """Unregister all UI operators"""
    field_operators.unregister()
    effector_operators.unregister()
    cloner_operators.unregister()