"""
Noise Effector module.

This module contains the noise effector implementation,
which provides random noise-based transformations for instances.
"""

# Import the noise effector class
from .noise_effector import NoiseEffector

# Public API
__all__ = [
    'NoiseEffector'
]


def register():
    """Register noise effector components"""
    pass


def unregister():
    """Unregister noise effector components"""
    pass
