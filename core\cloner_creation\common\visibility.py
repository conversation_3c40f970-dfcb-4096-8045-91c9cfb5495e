"""
Унифицированные функции видимости для всех типов клонеров.

Объединяет логику из:
- object_cloner_modules/utils/visibility.py
- collection_cloner_modules/utils/visibility.py

Сохраняет оригинальную логику 1 в 1 для обеспечения совместимости.
"""

import bpy
import bmesh
from typing import Union

# Импортируем функцию поиска layer collection
from ....operations.helpers.common.utils import find_layer_collection

def setup_cloner_visibility(cloner_obj: bpy.types.Object, mode: str = "object") -> bool:
    """
    Настраивает видимость объекта клонера.

    Унифицирует логику из object и collection модулей.

    Args:
        cloner_obj: Объект клонера
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        bool: True если настройка прошла успешно
    """
    try:
        # Логика идентична в обоих модулях (object строки 31-42, collection строки 65-76)
        if cloner_obj:
            cloner_obj.hide_viewport = False
            cloner_obj.hide_render = False

            # Гарантируем, что объект имеет вершины
            if cloner_obj.type == 'MESH' and len(cloner_obj.data.vertices) == 0:
                # Создаем простую геометрию, чтобы объект был видим
                bm = bmesh.new()
                bm.verts.new((0, 0, 0))
                bm.to_mesh(cloner_obj.data)
                bm.free()

        return True
    except Exception as e:
        print(f"Ошибка при настройке видимости клонера: {e}")
        return False

def setup_collection_visibility(context: bpy.types.Context, cloner_collection: bpy.types.Collection, mode: str = "object") -> bool:
    """
    Настраивает видимость коллекции клонера.

    Унифицирует логику из object и collection модулей.

    Args:
        context: Контекст Blender
        cloner_collection: Коллекция клонера
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        bool: True если настройка прошла успешно
    """
    try:
        if mode == "object":
            # Логика из object_cloner_modules/utils/visibility.py (строки 62-67)
            layer_collection = context.view_layer.layer_collection
            layer_coll = find_layer_collection(layer_collection, cloner_collection.name)
            if layer_coll:
                # Всегда делаем коллекцию клонера видимой
                layer_coll.exclude = False
            return True

        elif mode in ["collection", "stacked"]:
            # Логика из collection_cloner_modules/utils/visibility.py (строки 29-43)
            # Убедимся, что коллекция клонера добавлена в сцену
            if cloner_collection.name not in context.scene.collection.children:
                try:
                    context.scene.collection.children.link(cloner_collection)
                except Exception as e:
                    print(f"Ошибка при добавлении коллекции в сцену: {e}")

            # Убеждаемся, что коллекция клонера видима
            layer_collection = context.view_layer.layer_collection
            layer_coll = find_layer_collection(layer_collection, cloner_collection.name)
            if layer_coll:
                # Всегда делаем коллекцию клонера видимой
                layer_coll.exclude = False

            return True

        else:
            raise ValueError(f"Unknown mode: {mode}")

    except Exception as e:
        print(f"Ошибка при настройке видимости коллекции: {e}")
        return False

def ensure_chain_visibility(context: bpy.types.Context, source_obj: bpy.types.Object, mode: str = "object") -> bool:
    """
    Обеспечивает видимость всех коллекций в цепочке клонеров.

    Унифицирует логику из object и collection модулей.

    Args:
        context: Контекст Blender
        source_obj: Исходный объект или предыдущий клонер в цепочке
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        bool: True если настройка прошла успешно
    """
    try:
        if mode == "object":
            # Логика из object_cloner_modules/utils/visibility.py (строки 108-117)
            if source_obj.name.startswith("Cloner_"):
                layer_collection = context.view_layer.layer_collection
                for mod in source_obj.modifiers:
                    if mod.type == 'NODES' and mod.get("cloner_collection"):
                        prev_collection_name = mod.get("cloner_collection")
                        prev_layer_coll = find_layer_collection(layer_collection, prev_collection_name)
                        if prev_layer_coll:
                            prev_layer_coll.exclude = False
            return True

        elif mode in ["collection", "stacked"]:
            # Логика из collection_cloner_modules/utils/visibility.py (строки 97-108)
            if source_obj:  # previous_cloner_object
                layer_collection = context.view_layer.layer_collection

                for mod in source_obj.modifiers:
                    if mod.type == 'NODES' and mod.get("cloner_collection"):
                        prev_collection_name = mod.get("cloner_collection")
                        prev_layer_coll = find_layer_collection(layer_collection, prev_collection_name)
                        if prev_layer_coll:
                            prev_layer_coll.exclude = False
            return True

        else:
            raise ValueError(f"Unknown mode: {mode}")

    except Exception as e:
        print(f"Ошибка при обеспечении видимости цепочки: {e}")
        return False

def hide_original_source(context: bpy.types.Context, source: Union[bpy.types.Object, bpy.types.Collection],
                        modifier: bpy.types.Modifier = None, mode: str = "object") -> bool:
    """
    Скрывает исходный объект или коллекцию.

    Унифицирует логику из object и collection модулей.

    Args:
        context: Контекст Blender
        source: Исходный объект или коллекция
        modifier: Модификатор клонера (для collection режима)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        bool: True если скрытие прошло успешно
    """
    try:
        if mode == "object":
            # Логика из object_cloner_modules/utils/visibility.py (строки 85-89)
            if isinstance(source, bpy.types.Object):
                # Скрываем оригинальный объект только если это не клонер
                if not source.name.startswith("Cloner_"):
                    source.hide_viewport = True
                    source.hide_render = True
            return True

        elif mode in ["collection", "stacked"]:
            # Логика из collection_cloner_modules/utils/visibility.py (строки 129-142)
            if isinstance(source, bpy.types.Collection):
                layer_collection = context.view_layer.layer_collection

                # Скрываем исходную коллекцию в layer view
                target_layer_coll = find_layer_collection(layer_collection, source.name)
                if target_layer_coll:
                    # Сохраняем текущее состояние видимости
                    was_excluded = target_layer_coll.exclude
                    if modifier:
                        modifier["was_collection_excluded"] = was_excluded

                    # Скрываем исходную коллекцию только если это не коллекция клонера
                    if not source.name.startswith("cloner_"):
                        target_layer_coll.exclude = True
            return True

        else:
            raise ValueError(f"Unknown mode: {mode}")

    except Exception as e:
        print(f"Ошибка при скрытии исходного источника: {e}")
        return False

def add_collection_to_scene(context: bpy.types.Context, collection: bpy.types.Collection) -> bool:
    """
    Добавляет коллекцию в сцену.

    Перенесено из collection_cloner_modules/utils/visibility.py (строки 162-165).

    Args:
        context: Контекст Blender
        collection: Коллекция для добавления

    Returns:
        bool: True если добавление прошло успешно
    """
    try:
        # Убедимся, что коллекция добавлена в сцену
        if collection.name not in context.scene.collection.children:
            context.scene.collection.children.link(collection)
        return True
    except Exception as e:
        print(f"Ошибка при добавлении коллекции в сцену: {e}")
        return False
