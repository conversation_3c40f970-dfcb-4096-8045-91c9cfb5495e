"""
Collection Grid Cloner Parameter Definitions

This module defines all parameters for the Collection Grid Cloner component using the new
unified parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

Collection Grid Cloner creates instances of collections in a 3D grid pattern.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    get_standard_parameter_set
)


# Basic Collection Grid Cloner parameters (same as Grid but for collections)
COLLECTION_GRID_BASIC_GROUP = ParameterGroup(
    name="basic",
    description="Basic collection grid cloner settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Count X",
            param_type=ParameterType.INT,
            default_value=3,
            min_value=1,
            max_value=100,
            description="Number of instances along X axis",
            ui_group="Basic Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Count Y",
            param_type=ParameterType.INT,
            default_value=3,
            min_value=1,
            max_value=100,
            description="Number of instances along Y axis",
            ui_group="Basic Settings",
            ui_order=2
        ),
        ParameterDefinition(
            name="Count Z",
            param_type=ParameterType.INT,
            default_value=1,
            min_value=1,
            max_value=100,
            description="Number of instances along Z axis",
            ui_group="Basic Settings",
            ui_order=3
        ),
        ParameterDefinition(
            name="Spacing",
            param_type=ParameterType.VECTOR,
            default_value=(3.0, 3.0, 3.0),
            description="Spacing between instances in each axis",
            ui_group="Basic Settings",
            ui_order=4
        ),
        ParameterDefinition(
            name="Center Grid",
            param_type=ParameterType.BOOL,
            default_value=True,
            description="Center the grid around the origin",
            ui_group="Basic Settings",
            ui_order=5,
            is_hidden=True  # Hidden from UI - enabled by default, rarely needs to be changed
        )
    ]
)

# Complete parameter set for Collection Grid Cloner
COLLECTION_GRID_CLONER_PARAMETERS = ComponentParameterSet(
    component_type="CLONER",
    component_id="COLLECTION_GRID",
    description="Collection Grid Cloner parameter set - creates instances of collections in a 3D grid pattern",
    version="1.0",
    groups=[
        get_standard_parameter_set("collection_cloner_io"),  # Using collection cloner IO parameters
        COLLECTION_GRID_BASIC_GROUP,
        get_standard_parameter_set("global_transform"),
        get_standard_parameter_set("instance"),
        get_standard_parameter_set("random"),
        get_standard_parameter_set("effector_control")
    ]
)
