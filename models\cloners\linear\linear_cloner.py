"""
Linear Cloner Module

This module provides the LinearCloner class and related functionality for creating
linear cloners with interpolation, randomization, and instance picking features.
"""

# Импорты bpy и mathutils удалены - не используются в этом модуле
from ..base import ClonerBase
from .logic_builder import create_logic_group
from .interface_builder import create_main_group
from ....core.registry import register_cloner


@register_cloner("LINEAR", "Linear Cloner", "Create a line of instances", "SORTSIZE")
class LinearCloner(ClonerBase):
    """Linear Cloner implementation with modular architecture"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core linear cloner logic"""
        return create_logic_group(name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a linear cloner node group with scale and rotation interpolation"""
        return create_main_group(logic_group, name_suffix)

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create the complete linear cloner node group with logic and interface"""
        logic_group = cls.create_logic_group(name_suffix)
        main_group = cls.create_main_group(logic_group, name_suffix)
        return main_group


# Maintain backwards compatibility with the procedural interface
def create_linear_cloner_logic_group():
    """Legacy function for backwards compatibility"""
    return LinearCloner.create_logic_group()


def advancedlinearcloner_node_group():
    """Legacy function for backwards compatibility"""
    return LinearCloner.create_node_group()


# Функции register()/unregister() удалены - регистрация происходит через декораторы @register_cloner
