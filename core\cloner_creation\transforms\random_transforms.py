"""
Унифицированные случайные трансформации для всех типов клонеров.

Объединяет логику из:
- object_cloner_modules/transforms/random_transforms.py
- stacked_cloner_modules/transforms/stacked_random.py

Сохраняет оригинальную логику 1 в 1 для обеспечения совместимости.
95% дублирования между модулями - критический приоритет рефакторинга.
"""

import bpy
from typing import Dict, Optional, Tuple

def setup_random_position(nodes, links, group_in, index_node, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Настраивает случайную позицию.

    Унифицирует логику из object и stacked модулей.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        index_node: Узел индекса
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел случайной позиции или None
    """
    try:
        # Логика идентична в обоих модулях (object строки 32-51, stacked строки 98-111)
        random_pos_node = nodes.new('FunctionNodeRandomValue')
        random_pos_node.data_type = 'FLOAT_VECTOR'
        random_pos_node.location = (0, -150)

        # Подключаем seed и ID
        links.new(group_in.outputs['Random Seed'], random_pos_node.inputs['Seed'])
        links.new(index_node.outputs['Index'], random_pos_node.inputs['ID'])

        # Создаем отрицательный диапазон для позиции
        vector_math_neg_pos = nodes.new('ShaderNodeVectorMath')
        vector_math_neg_pos.operation = 'MULTIPLY'
        vector_math_neg_pos.inputs[1].default_value = (-1.0, -1.0, -1.0)
        vector_math_neg_pos.location = (-100, -150)
        links.new(group_in.outputs['Random Position'], vector_math_neg_pos.inputs[0])

        # Устанавливаем диапазон случайной позиции
        links.new(vector_math_neg_pos.outputs['Vector'], random_pos_node.inputs['Min'])
        links.new(group_in.outputs['Random Position'], random_pos_node.inputs['Max'])

        return random_pos_node
    except Exception as e:
        print(f"Ошибка при настройке случайной позиции: {e}")
        return None

def setup_random_rotation(nodes, links, group_in, index_node, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Настраивает случайное вращение.

    Унифицирует логику из object и stacked модулей.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        index_node: Узел индекса
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел случайного вращения или None
    """
    try:
        # Логика идентична в обоих модулях (object строки 73-92, stacked строки 136-149)
        random_rot_node = nodes.new('FunctionNodeRandomValue')
        random_rot_node.data_type = 'FLOAT_VECTOR'
        random_rot_node.location = (0, -250)

        # Подключаем seed и ID
        links.new(group_in.outputs['Random Seed'], random_rot_node.inputs['Seed'])
        links.new(index_node.outputs['Index'], random_rot_node.inputs['ID'])

        # Создаем отрицательный диапазон для вращения
        vector_math_neg_rot = nodes.new('ShaderNodeVectorMath')
        vector_math_neg_rot.operation = 'MULTIPLY'
        vector_math_neg_rot.inputs[1].default_value = (-1.0, -1.0, -1.0)
        vector_math_neg_rot.location = (-100, -250)
        links.new(group_in.outputs['Random Rotation'], vector_math_neg_rot.inputs[0])

        # Устанавливаем диапазон случайного вращения
        links.new(vector_math_neg_rot.outputs['Vector'], random_rot_node.inputs['Min'])
        links.new(group_in.outputs['Random Rotation'], random_rot_node.inputs['Max'])

        return random_rot_node
    except Exception as e:
        print(f"Ошибка при настройке случайного вращения: {e}")
        return None

def setup_random_scale(nodes, links, group_in, index_node, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Настраивает случайный масштаб.

    Унифицирует логику из object и stacked модулей.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        index_node: Узел индекса
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел случайного масштаба или None
    """
    try:
        # Логика идентична в обоих модулях (object строки 114-147, stacked строки 174-201)
        random_scale_node = nodes.new('FunctionNodeRandomValue')
        random_scale_node.data_type = 'FLOAT_VECTOR'
        random_scale_node.location = (0, -350)

        # Подключаем seed и ID
        links.new(group_in.outputs['Random Seed'], random_scale_node.inputs['Seed'])
        links.new(index_node.outputs['Index'], random_scale_node.inputs['ID'])

        # Создаем узел для объединения Random Scale в вектор
        combine_random_scale = nodes.new('ShaderNodeCombineXYZ')
        combine_random_scale.location = (-200, -350)
        links.new(group_in.outputs['Random Scale'], combine_random_scale.inputs['X'])
        links.new(group_in.outputs['Random Scale'], combine_random_scale.inputs['Y'])
        links.new(group_in.outputs['Random Scale'], combine_random_scale.inputs['Z'])

        # 1 - Random Scale для минимального значения
        vector_one_minus = nodes.new('ShaderNodeVectorMath')
        vector_one_minus.operation = 'SUBTRACT'
        vector_one_minus.inputs[0].default_value = (1.0, 1.0, 1.0)
        vector_one_minus.location = (-100, -300)
        links.new(combine_random_scale.outputs['Vector'], vector_one_minus.inputs[1])

        # 1 + Random Scale для максимального значения
        vector_one_plus = nodes.new('ShaderNodeVectorMath')
        vector_one_plus.operation = 'ADD'
        vector_one_plus.inputs[0].default_value = (1.0, 1.0, 1.0)
        vector_one_plus.location = (-100, -400)
        links.new(combine_random_scale.outputs['Vector'], vector_one_plus.inputs[1])

        # Устанавливаем диапазон для случайного масштаба
        links.new(vector_one_minus.outputs['Vector'], random_scale_node.inputs['Min'])
        links.new(vector_one_plus.outputs['Vector'], random_scale_node.inputs['Max'])

        return random_scale_node
    except Exception as e:
        print(f"Ошибка при настройке случайного масштаба: {e}")
        return None

def create_random_transforms(nodes, links, group_in, index_node, mode: str = "object") -> Dict[str, Optional[bpy.types.Node]]:
    """
    Создает все узлы случайных трансформаций.

    Унифицированная функция для создания всех случайных трансформаций.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        index_node: Узел индекса
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Dict: Словарь с узлами случайных трансформаций
    """
    try:
        random_transforms = {
            'position': setup_random_position(nodes, links, group_in, index_node, mode),
            'rotation': setup_random_rotation(nodes, links, group_in, index_node, mode),
            'scale': setup_random_scale(nodes, links, group_in, index_node, mode)
        }

        return random_transforms
    except Exception as e:
        print(f"Ошибка при создании случайных трансформаций: {e}")
        return {'position': None, 'rotation': None, 'scale': None}

def combine_base_and_random_rotation(nodes, links, group_in, random_rot_node, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Комбинирует базовое и случайное вращение.

    Перенесено из object_cloner_modules/transforms/random_transforms.py (строки 216-222).

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        random_rot_node: Узел случайного вращения
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел с комбинированным вращением или None
    """
    try:
        # Смешиваем базовое вращение со случайным
        add_random_rotation = nodes.new('ShaderNodeVectorMath')
        add_random_rotation.operation = 'ADD'
        add_random_rotation.location = (100, -250)
        links.new(group_in.outputs['Instance Rotation'], add_random_rotation.inputs[0])
        links.new(random_rot_node.outputs['Value'], add_random_rotation.inputs[1])

        return add_random_rotation
    except Exception as e:
        print(f"Ошибка при комбинировании вращения: {e}")
        return None

def combine_base_and_random_scale(nodes, links, group_in, random_scale_node, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Комбинирует базовый и случайный масштаб.

    Перенесено из object_cloner_modules/transforms/random_transforms.py (строки 242-248).

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        random_scale_node: Узел случайного масштаба
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел с комбинированным масштабом или None
    """
    try:
        # Смешиваем базовый масштаб со случайным (умножение)
        add_random_scale = nodes.new('ShaderNodeVectorMath')
        add_random_scale.operation = 'MULTIPLY'
        add_random_scale.location = (100, -350)
        links.new(group_in.outputs['Instance Scale'], add_random_scale.inputs[0])
        links.new(random_scale_node.outputs['Value'], add_random_scale.inputs[1])

        return add_random_scale
    except Exception as e:
        print(f"Ошибка при комбинировании масштаба: {e}")
        return None

def apply_random_transforms(nodes, links, target_node, random_transforms: Dict[str, Optional[bpy.types.Node]],
                           mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет случайные трансформации к узлу.

    Унифицирует логику из object и stacked модулей.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        target_node: Узел для применения трансформаций
        random_transforms: Словарь с узлами случайных трансформаций
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Финальный узел с примененными трансформациями или None
    """
    try:
        current_node = target_node
        random_pos = random_transforms.get('position')
        random_rot = random_transforms.get('rotation')
        random_scale = random_transforms.get('scale')

        if mode == "object":
            # Логика из object_cloner_modules/transforms/random_transforms.py (строки 170-196)
            # 1. Применяем случайную позицию
            if random_pos:
                translate_instances = nodes.new('GeometryNodeTranslateInstances')
                translate_instances.location = (100, 200)
                links.new(current_node.outputs['Instances'], translate_instances.inputs['Instances'])
                links.new(random_pos.outputs['Value'], translate_instances.inputs['Translation'])
                current_node = translate_instances

            # 2. Применяем случайное вращение
            if random_rot:
                rotate_instances = nodes.new('GeometryNodeRotateInstances')
                rotate_instances.location = (200, 200)
                links.new(current_node.outputs['Instances'], rotate_instances.inputs['Instances'])
                links.new(random_rot.outputs['Value'], rotate_instances.inputs['Rotation'])
                current_node = rotate_instances

            # 3. Применяем случайный масштаб
            if random_scale:
                scale_instances = nodes.new('GeometryNodeScaleInstances')
                scale_instances.location = (300, 200)
                links.new(current_node.outputs['Instances'], scale_instances.inputs['Instances'])
                links.new(random_scale.outputs['Value'], scale_instances.inputs['Scale'])
                current_node = scale_instances

        elif mode in ["stacked", "collection"]:
            # Логика из stacked_cloner_modules/transforms/stacked_random.py (строки 58-72)
            # 1. Случайное смещение
            if random_pos:
                set_position = nodes.new('GeometryNodeSetPosition')
                set_position.location = (100, 200)
                links.new(current_node.outputs['Instances'], set_position.inputs['Geometry'])
                links.new(random_pos.outputs['Value'], set_position.inputs['Offset'])
                current_node = set_position

            # 2. Вращение
            if random_rot:
                rotate_random = nodes.new('GeometryNodeRotateInstances')
                rotate_random.location = (200, 200)
                links.new(current_node.outputs['Geometry'], rotate_random.inputs['Instances'])
                links.new(random_rot.outputs['Value'], rotate_random.inputs['Rotation'])
                current_node = rotate_random

            # 3. Масштаб
            if random_scale:
                scale_random = nodes.new('GeometryNodeScaleInstances')
                scale_random.location = (300, 200)
                links.new(current_node.outputs['Instances'], scale_random.inputs['Instances'])
                links.new(random_scale.outputs['Value'], scale_random.inputs['Scale'])
                current_node = scale_random

        return current_node
    except Exception as e:
        print(f"Ошибка при применении случайных трансформаций: {e}")
        return target_node

def apply_stacked_random_transforms_complete(node_group, instance_node, group_in, mode: str = "stacked") -> Optional[bpy.types.Node]:
    """
    Полная реализация случайных трансформаций для stacked клонеров.

    Перенесено из stacked_cloner_modules/transforms/stacked_random.py (строки 15-77).

    Args:
        node_group: Node group для создания узлов
        instance_node: Узел Instance on Points для подключения
        group_in: Входной узел группы
        mode: Режим клонера (по умолчанию "stacked")

    Returns:
        Node: Финальный узел с примененными трансформациями или None
    """
    try:
        nodes = node_group.nodes
        links = node_group.links

        # Создаем узел индекса для генерации случайных значений
        index = nodes.new('GeometryNodeInputIndex')

        # Создаем узлы случайных значений
        random_transforms = create_random_transforms(nodes, links, group_in, index, mode)

        # Смешиваем базовые трансформации со случайными
        add_random_rotation = combine_base_and_random_rotation(nodes, links, group_in, random_transforms['rotation'], mode)
        add_random_scale = combine_base_and_random_scale(nodes, links, group_in, random_transforms['scale'], mode)

        # Применяем трансформации последовательно
        current_node = instance_node

        # 1. Случайное смещение
        if random_transforms['position']:
            set_position = nodes.new('GeometryNodeSetPosition')
            links.new(current_node.outputs['Instances'], set_position.inputs['Geometry'])
            links.new(random_transforms['position'].outputs['Value'], set_position.inputs['Offset'])
            current_node = set_position

        # 2. Вращение (базовое + случайное)
        if add_random_rotation:
            rotate_random = nodes.new('GeometryNodeRotateInstances')
            links.new(current_node.outputs['Geometry'], rotate_random.inputs['Instances'])
            links.new(add_random_rotation.outputs['Vector'], rotate_random.inputs['Rotation'])
            current_node = rotate_random

        # 3. Масштаб (базовый * случайный)
        if add_random_scale:
            scale_random = nodes.new('GeometryNodeScaleInstances')
            links.new(current_node.outputs['Instances'], scale_random.inputs['Instances'])
            links.new(add_random_scale.outputs['Vector'], scale_random.inputs['Scale'])
            current_node = scale_random

        return current_node

    except Exception as e:
        print(f"Ошибка при применении полных случайных трансформаций: {e}")
        return None

def combine_base_and_random_rotation(nodes, links, group_in, random_rot_node, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Комбинирует базовое и случайное вращение для object клонеров.

    Специфичная функция для object клонеров, которая складывает Instance Rotation и Random Rotation.
    Используется в Linear клонере для комбинирования трансформаций.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        random_rot_node: Узел случайного вращения
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел VectorMath с комбинированным вращением или None
    """
    try:
        add_random_rotation = nodes.new('ShaderNodeVectorMath')
        add_random_rotation.operation = 'ADD'
        add_random_rotation.name = "Combine Instance+Random Rotation"

        # Устанавливаем позицию в зависимости от режима
        if mode == "object":
            add_random_rotation.location = (100, -250)
        elif mode == "stacked":
            add_random_rotation.location = (200, -250)
        else:
            add_random_rotation.location = (100, -250)

        links.new(group_in.outputs['Instance Rotation'], add_random_rotation.inputs[0])
        links.new(random_rot_node.outputs['Value'], add_random_rotation.inputs[1])
        return add_random_rotation
    except Exception as e:
        print(f"Ошибка при комбинировании вращения ({mode}): {e}")
        return None

def combine_base_and_random_scale(nodes, links, group_in, random_scale_node, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Комбинирует базовый и случайный масштаб для object клонеров.

    Специфичная функция для object клонеров, которая умножает Instance Scale на Random Scale.
    Используется в Linear клонере для комбинирования трансформаций.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        group_in: Входной узел группы
        random_scale_node: Узел случайного масштаба
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел VectorMath с комбинированным масштабом или None
    """
    try:
        add_random_scale = nodes.new('ShaderNodeVectorMath')
        add_random_scale.operation = 'MULTIPLY'
        add_random_scale.name = "Combine Instance*Random Scale"

        # Устанавливаем позицию в зависимости от режима
        if mode == "object":
            add_random_scale.location = (100, -350)
        elif mode == "stacked":
            add_random_scale.location = (200, -350)
        else:
            add_random_scale.location = (100, -350)

        links.new(group_in.outputs['Instance Scale'], add_random_scale.inputs[0])
        links.new(random_scale_node.outputs['Value'], add_random_scale.inputs[1])
        return add_random_scale
    except Exception as e:
        print(f"Ошибка при комбинировании масштаба ({mode}): {e}")
        return None
