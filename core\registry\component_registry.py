"""
Централизованная система регистрации компонентов.

Управляет регистрацией клонеров, эффекторов и полей через систему декораторов,
обеспечивая автоматическую регистрацию компонентов при их определении.
"""

from typing import Dict, List, Tuple, Type

class ComponentMeta:
    """Метаданные компонента"""
    def __init__(self,
                 component_id: str,
                 display_name: str,
                 description: str,
                 icon: str,
                 category: str = "CLONER"):
        self.component_id = component_id
        self.display_name = display_name
        self.description = description
        self.icon = icon
        self.category = category

class ComponentRegistry:
    """
    Централизованный реестр всех компонентов.

    Управляет регистрацией и доступом к клонерам, эффекторам и полям.
    Компоненты регистрируются автоматически через систему декораторов.
    """

    def __init__(self):
        self._cloners: Dict[str, Type] = {}
        self._effectors: Dict[str, Type] = {}
        self._fields: Dict[str, Type] = {}
        self._cloner_meta: Dict[str, ComponentMeta] = {}
        self._effector_meta: Dict[str, ComponentMeta] = {}
        self._field_meta: Dict[str, ComponentMeta] = {}

    def register_cloner(self, cloner_class: Type, meta: ComponentMeta):
        """Регистрирует клонер (с проверкой дублирования)"""
        if meta.component_id not in self._cloners:
            self._cloners[meta.component_id] = cloner_class
            self._cloner_meta[meta.component_id] = meta
        else:
            # Компонент уже зарегистрирован, обновляем только если класс отличается
            if self._cloners[meta.component_id] != cloner_class:
                self._cloners[meta.component_id] = cloner_class
                self._cloner_meta[meta.component_id] = meta

    def register_effector(self, effector_class: Type, meta: ComponentMeta):
        """Регистрирует эффектор (с проверкой дублирования)"""
        if meta.component_id not in self._effectors:
            self._effectors[meta.component_id] = effector_class
            self._effector_meta[meta.component_id] = meta
        else:
            # Компонент уже зарегистрирован, обновляем только если класс отличается
            if self._effectors[meta.component_id] != effector_class:
                self._effectors[meta.component_id] = effector_class
                self._effector_meta[meta.component_id] = meta

    def register_field(self, field_class: Type, meta: ComponentMeta):
        """Регистрирует поле (с проверкой дублирования)"""
        if meta.component_id not in self._fields:
            self._fields[meta.component_id] = field_class
            self._field_meta[meta.component_id] = meta
        else:
            # Компонент уже зарегистрирован, обновляем только если класс отличается
            if self._fields[meta.component_id] != field_class:
                self._fields[meta.component_id] = field_class
                self._field_meta[meta.component_id] = meta

    def get_cloner_types(self) -> List[Tuple[str, str, str, str]]:
        """Возвращает список типов клонеров для UI"""
        return [
            (meta.component_id, meta.display_name, meta.description, meta.icon)
            for meta in self._cloner_meta.values()
        ]

    def get_effector_types(self) -> List[Tuple[str, str, str, str]]:
        """Возвращает список типов эффекторов для UI"""
        return [
            (meta.component_id, meta.display_name, meta.description, meta.icon)
            for meta in self._effector_meta.values()
        ]

    def get_field_types(self) -> List[Tuple[str, str, str, str]]:
        """Возвращает список типов полей для UI"""
        return [
            (meta.component_id, meta.display_name, meta.description, meta.icon)
            for meta in self._field_meta.values()
        ]

    def get_cloner_class(self, component_id: str) -> Type:
        """Получает класс клонера по ID"""
        return self._cloners.get(component_id)

    def get_effector_class(self, component_id: str) -> Type:
        """Получает класс эффектора по ID"""
        return self._effectors.get(component_id)

    def get_field_class(self, component_id: str) -> Type:
        """Получает класс поля по ID"""
        return self._fields.get(component_id)

    def get_cloner_group_names(self) -> Dict[str, str]:
        """Возвращает имена групп узлов для клонеров"""
        return {
            component_id: f"Advanced{meta.display_name.replace(' ', '')}"
            for component_id, meta in self._cloner_meta.items()
        }

    def get_cloner_mod_names(self) -> Dict[str, str]:
        """Возвращает имена модификаторов для клонеров"""
        return {
            component_id: meta.display_name
            for component_id, meta in self._cloner_meta.items()
        }

    def get_effector_group_names(self) -> Dict[str, str]:
        """Возвращает имена групп узлов для эффекторов"""
        return {
            component_id: f"{meta.display_name.replace(' ', '')}"
            for component_id, meta in self._effector_meta.items()
        }

    def get_effector_mod_names(self) -> Dict[str, str]:
        """Возвращает имена модификаторов для эффекторов"""
        return {
            component_id: meta.display_name
            for component_id, meta in self._effector_meta.items()
        }

    def get_field_group_names(self) -> Dict[str, str]:
        """Возвращает имена групп узлов для полей"""
        return {
            component_id: f"{meta.display_name.replace(' ', '')}"
            for component_id, meta in self._field_meta.items()
        }

    def get_field_mod_names(self) -> Dict[str, str]:
        """Возвращает имена модификаторов для полей"""
        return {
            component_id: meta.display_name
            for component_id, meta in self._field_meta.items()
        }



# Глобальный реестр компонентов
component_registry = ComponentRegistry()
