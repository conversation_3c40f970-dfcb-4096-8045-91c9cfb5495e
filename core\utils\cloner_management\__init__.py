"""
Модуль-заглушка для управления клонерами.

Этот модуль предоставляет функции для поиска и управления клонерами.
"""

import bpy
from ...registry import component_registry

def get_cloners_on_object(obj):
    """
    Получает список модификаторов клонеров на указанном объекте.
    
    Args:
        obj (bpy.types.Object): Объект, на котором нужно найти клонеры
        
    Returns:
        list: Список модификаторов клонеров
    """
    if not obj:
        return []
        
    cloners = []
    
    # Получаем префиксы имен групп клонеров из реестра
    cloner_group_names = component_registry.get_cloner_group_names()
    
    # Проверяем каждый модификатор
    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group:
            # Проверяем, содержит ли имя группы узлов один из префиксов клонеров
            for group_name in cloner_group_names.values():
                if group_name in mod.node_group.name:
                    cloners.append(mod)
                    break
                    
    return cloners
    
def get_all_cloners_in_scene(scene):
    """
    Получает список всех модификаторов клонеров в сцене.
    
    Args:
        scene (bpy.types.Scene): Сцена, в которой нужно найти клонеры
        
    Returns:
        list: Список кортежей (объект, модификатор) с клонерами
    """
    all_cloners = []
    
    # Перебираем все объекты в сцене
    for obj in scene.objects:
        # Получаем клонеры на текущем объекте
        cloners = get_cloners_on_object(obj)
        
        # Добавляем их в общий список с указанием объекта
        for mod in cloners:
            all_cloners.append((obj, mod))
            
    return all_cloners