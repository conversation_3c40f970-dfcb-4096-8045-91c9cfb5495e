"""
Central registry for all component parameter definitions.

This module manages the registration and retrieval of parameter sets
for all component types, providing a centralized location for parameter
definitions.
"""

from typing import Dict, Optional, List, Tuple
from .parameter_definition import ComponentParameterSet


class ParameterRegistry:
    """
    Central registry for component parameter definitions.
    
    Manages parameter sets for all component types and provides
    methods for registration and retrieval.
    """
    
    def __init__(self):
        self._parameter_sets: Dict[Tuple[str, str], ComponentParameterSet] = {}
        self._component_types: Dict[str, List[str]] = {
            'CLONER': [],
            'EFFECTOR': [],
            'FIELD': []
        }
    
    def register_parameter_set(self, parameter_set: ComponentParameterSet) -> bool:
        """
        Register a parameter set for a component.
        
        Args:
            parameter_set: The parameter set to register
            
        Returns:
            bool: True if registration was successful
        """
        try:
            key = (parameter_set.component_type, parameter_set.component_id)
            
            # Check if already registered
            if key in self._parameter_sets:
                print(f"Warning: Parameter set for {parameter_set.component_type}.{parameter_set.component_id} already registered, overwriting")
            
            # Register the parameter set
            self._parameter_sets[key] = parameter_set
            
            # Add to component type list if not already there
            if parameter_set.component_id not in self._component_types[parameter_set.component_type]:
                self._component_types[parameter_set.component_type].append(parameter_set.component_id)
            
            print(f"Registered parameter set: {parameter_set.component_type}.{parameter_set.component_id}")
            return True
            
        except Exception as e:
            print(f"Error registering parameter set {parameter_set.component_type}.{parameter_set.component_id}: {e}")
            return False
    
    def get_parameter_set(self, component_type: str, component_id: str) -> Optional[ComponentParameterSet]:
        """
        Get parameter set for a specific component.
        
        Args:
            component_type: Type of component ('CLONER', 'EFFECTOR', 'FIELD')
            component_id: ID of component ('GRID', 'RANDOM', 'SPHERE', etc.)
            
        Returns:
            ComponentParameterSet or None if not found
        """
        key = (component_type, component_id)
        return self._parameter_sets.get(key)
    
    def has_parameter_set(self, component_type: str, component_id: str) -> bool:
        """
        Check if parameter set exists for a component.
        
        Args:
            component_type: Type of component
            component_id: ID of component
            
        Returns:
            bool: True if parameter set exists
        """
        key = (component_type, component_id)
        return key in self._parameter_sets
    
    def get_component_ids(self, component_type: str) -> List[str]:
        """
        Get all registered component IDs for a given type.
        
        Args:
            component_type: Type of component
            
        Returns:
            List of component IDs
        """
        return self._component_types.get(component_type, []).copy()
    
    def get_all_component_types(self) -> List[str]:
        """Get all registered component types."""
        return list(self._component_types.keys())
    
    def get_registered_components(self) -> Dict[str, List[str]]:
        """Get all registered components organized by type."""
        return {
            comp_type: comp_ids.copy() 
            for comp_type, comp_ids in self._component_types.items()
        }
    
    def unregister_parameter_set(self, component_type: str, component_id: str) -> bool:
        """
        Unregister a parameter set.
        
        Args:
            component_type: Type of component
            component_id: ID of component
            
        Returns:
            bool: True if unregistration was successful
        """
        try:
            key = (component_type, component_id)
            
            if key in self._parameter_sets:
                del self._parameter_sets[key]
                
                # Remove from component type list
                if component_id in self._component_types[component_type]:
                    self._component_types[component_type].remove(component_id)
                
                print(f"Unregistered parameter set: {component_type}.{component_id}")
                return True
            else:
                print(f"Warning: Parameter set {component_type}.{component_id} not found for unregistration")
                return False
                
        except Exception as e:
            print(f"Error unregistering parameter set {component_type}.{component_id}: {e}")
            return False
    
    def clear_all(self):
        """Clear all registered parameter sets."""
        self._parameter_sets.clear()
        for comp_type in self._component_types:
            self._component_types[comp_type].clear()
        print("Cleared all parameter sets from registry")
    
    def get_statistics(self) -> Dict[str, int]:
        """Get statistics about registered parameter sets."""
        stats = {
            'total_parameter_sets': len(self._parameter_sets),
            'total_cloners': len(self._component_types['CLONER']),
            'total_effectors': len(self._component_types['EFFECTOR']),
            'total_fields': len(self._component_types['FIELD'])
        }
        return stats


# Global parameter registry instance
parameter_registry = ParameterRegistry()


def register_component_parameters(parameter_set: ComponentParameterSet) -> bool:
    """
    Convenience function to register component parameters.
    
    Args:
        parameter_set: The parameter set to register
        
    Returns:
        bool: True if registration was successful
    """
    return parameter_registry.register_parameter_set(parameter_set)


def get_component_parameters(component_type: str, component_id: str) -> Optional[ComponentParameterSet]:
    """
    Convenience function to get component parameters.
    
    Args:
        component_type: Type of component
        component_id: ID of component
        
    Returns:
        ComponentParameterSet or None if not found
    """
    return parameter_registry.get_parameter_set(component_type, component_id)
