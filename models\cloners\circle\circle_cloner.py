"""
Circle Cloner Module

This module provides the CircleCloner class and related functionality for creating
circular/radial cloners with height adjustment, face-to-center alignment, and randomization.
"""

import bpy  # Используется в create_node_group_with_new_parameters
# import math удален - не используется в этом модуле
from ..base import ClonerBase
from .logic_builder import create_logic_group
from .interface_builder import create_main_group
from ....core.registry import register_cloner


@register_cloner("CIRCLE", "Circle Cloner", "Create a circle of instances", "MESH_CIRCLE")
class CircleCloner(ClonerBase):
    """Circle Cloner implementation with modular architecture"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core circle cloner logic"""
        return create_logic_group(name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a radial cloner node group similar to Cinema 4D's Radial Cloner"""
        return create_main_group(logic_group, name_suffix)

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """
        Create the complete circle cloner node group with logic and interface.

        UPDATED: Now uses new parameter system by default.
        """
        logic_group = cls.create_logic_group(name_suffix)
        main_group = cls.create_main_group(logic_group, name_suffix)
        return main_group

    @classmethod
    def create_node_group_with_new_parameters(cls, name_suffix=""):
        """
        EXPERIMENTAL: Create node group using the new parameter system.

        This method demonstrates how the new parameter system works.
        It creates a node group and automatically builds the interface
        from parameter definitions.

        Args:
            name_suffix: Optional suffix for the node group name

        Returns:
            The created node group with auto-generated interface
        """
        # Create new node group
        node_group = bpy.data.node_groups.new(
            type='GeometryNodeTree',
            name=f"CircleCloner_NewParams{name_suffix}"
        )

        # Use new parameter system to create interface
        success = cls.create_interface_from_parameters(node_group)

        if success:
            print(f"✅ Successfully created Circle Cloner interface using new parameter system")

            # Add basic nodes for demonstration
            nodes = node_group.nodes
            group_input = nodes.new('NodeGroupInput')
            group_output = nodes.new('NodeGroupOutput')
            group_input.location = (-200, 0)
            group_output.location = (200, 0)

            return node_group
        else:
            print(f"❌ Failed to create interface using new parameter system")
            # Fallback to old method
            return cls.create_node_group(name_suffix)


# Функции register()/unregister() удалены - регистрация происходит через декораторы @register_cloner
