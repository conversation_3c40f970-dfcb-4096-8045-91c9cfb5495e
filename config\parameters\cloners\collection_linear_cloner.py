"""
Collection Linear Cloner Parameter Definitions

This module defines all parameters for the Collection Linear Cloner component using the new
unified parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

Collection Linear Cloner creates instances of collections in a linear pattern.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    get_standard_parameter_set
)


# Basic Collection Linear Cloner parameters
COLLECTION_LINEAR_BASIC_GROUP = ParameterGroup(
    name="basic",
    description="Basic collection linear cloner settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Count",
            param_type=ParameterType.INT,
            default_value=5,
            min_value=1,
            max_value=1000,
            description="Number of instances along the line",
            ui_group="Basic Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Offset",
            param_type=ParameterType.VECTOR,
            default_value=(3.0, 0.0, 0.0),
            description="Offset between instances",
            ui_group="Basic Settings",
            ui_order=2
        )
    ]
)

# Complete parameter set for Collection Linear Cloner
COLLECTION_LINEAR_CLONER_PARAMETERS = ComponentParameterSet(
    component_type="CLONER",
    component_id="COLLECTION_LINEAR",
    description="Collection Linear Cloner parameter set - creates instances of collections in a linear pattern",
    version="1.0",
    groups=[
        get_standard_parameter_set("collection_cloner_io"),  # Using collection cloner IO parameters
        COLLECTION_LINEAR_BASIC_GROUP,
        get_standard_parameter_set("global_transform"),
        get_standard_parameter_set("instance"),  # Collection cloners use standard instance parameters
        get_standard_parameter_set("random"),
        get_standard_parameter_set("effector_control")
    ]
)
