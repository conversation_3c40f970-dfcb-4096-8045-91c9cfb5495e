"""
Построитель узлов для Grid клонера.

Отвечает за создание специфичной логики Grid клонера:
- Создание узлов сетки
- Логика 3D сетки
- Центрирование сетки
- Специфичные параметры Grid клонера
"""

from .base_builder import BaseNodeBuilder
from ....models.cloners.grid import GridCloner
from ....core.cloner_creation.transforms import (
    apply_global_transforms
)

class GridNodeBuilder(BaseNodeBuilder):
    """
    Построитель узлов для Grid клонера.

    Наследует от BaseNodeBuilder и добавляет специфичную логику для Grid клонера.
    """

    def __init__(self, node_group, orig_obj):
        """
        Инициализация построителя Grid узлов.

        Args:
            node_group: Группа узлов для настройки
            orig_obj: Исходный объект для клонирования
        """
        super().__init__(node_group, orig_obj, "GRID")

    def setup_grid_interface(self):
        """
        Настраивает интерфейс специфичный для Grid клонера.

        UPDATED: Теперь использует новую систему параметров с fallback на ручное создание.

        Returns:
            bool: True если настройка прошла успешно
        """
        try:
            # Очищаем текущую группу узлов и добавляем только необходимые интерфейсные сокеты
            for socket in list(self.node_group.interface.items_tree):
                self.node_group.interface.remove(socket)

            # NEW: Try to use new parameter system first
            from ....core.parameters import get_component_parameters, build_interface_from_parameters

            grid_params = get_component_parameters('CLONER', 'GRID')
            if grid_params:
                # Automatically create interface from parameter definitions
                success = build_interface_from_parameters(self.node_group, grid_params)
                if success:
                    print(f"✅ Grid Cloner interface created automatically from parameter definitions (GridNodeBuilder)")
                    return True
                else:
                    print(f"❌ Failed to create interface automatically, falling back to manual creation (GridNodeBuilder)")
            else:
                print(f"❌ Grid Cloner parameters not found, using manual interface creation (GridNodeBuilder)")

            # LEGACY: Fallback to manual interface creation
            # Добавляем базовый выходной сокет
            self.node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

            # Основные параметры для Grid
            count_x_socket = self.node_group.interface.new_socket("Count X", in_out='INPUT', socket_type='NodeSocketInt')
            count_x_socket.default_value = 3

            count_y_socket = self.node_group.interface.new_socket("Count Y", in_out='INPUT', socket_type='NodeSocketInt')
            count_y_socket.default_value = 3

            count_z_socket = self.node_group.interface.new_socket("Count Z", in_out='INPUT', socket_type='NodeSocketInt')
            count_z_socket.default_value = 1

            spacing_socket = self.node_group.interface.new_socket("Spacing", in_out='INPUT', socket_type='NodeSocketVector')
            spacing_socket.default_value = (3.0, 3.0, 3.0)

            # Add Use Effector parameter for effector integration
            use_effector_socket = self.node_group.interface.new_socket("Use Effector", in_out='INPUT', socket_type='NodeSocketBool')
            use_effector_socket.default_value = True

            return True
        except Exception as e:
            print(f"Ошибка при настройке Grid интерфейса: {e}")
            return False

    def build_grid_nodes(self):
        """
        Строит узлы специфичные для Grid клонера.
        Returns:
            bool: True если построение прошло успешно
        """
        try:
            print(f"Создание Grid клонера с использованием логики из GridCloner для объекта {self.orig_obj.name}")

            # Создаем logic_group с основной логикой клонера
            logic_group = GridCloner.create_logic_group(f"_{self.orig_obj.name}")

            # Очищаем существующие узлы
            for node in list(self.nodes):
                self.nodes.remove(node)

            # Создаем базовые узлы заново
            if not self.create_basic_nodes():
                return False

            # Добавляем узел с логикой клонера
            cloner_logic_node = self.nodes.new('GeometryNodeGroup')
            cloner_logic_node.node_tree = logic_group
            cloner_logic_node.name = "Grid Cloner Logic"
            cloner_logic_node.location = (0, 0)

            # Соединяем инстансы объекта с инстансами клонера
            self.links.new(self.object_info.outputs[self.output_socket], cloner_logic_node.inputs['Instance Source'])

            # Соединяем основные параметры
            self.links.new(self.group_in.outputs['Count X'], cloner_logic_node.inputs['Count X'])
            self.links.new(self.group_in.outputs['Count Y'], cloner_logic_node.inputs['Count Y'])
            self.links.new(self.group_in.outputs['Count Z'], cloner_logic_node.inputs['Count Z'])
            self.links.new(self.group_in.outputs['Spacing'], cloner_logic_node.inputs['Spacing'])
            self.links.new(self.group_in.outputs['Instance Scale'], cloner_logic_node.inputs['Instance Scale'])
            self.links.new(self.group_in.outputs['Instance Rotation'], cloner_logic_node.inputs['Instance Rotation'])
            self.links.new(self.group_in.outputs['Random Position'], cloner_logic_node.inputs['Random Position'])
            self.links.new(self.group_in.outputs['Random Rotation'], cloner_logic_node.inputs['Random Rotation'])
            self.links.new(self.group_in.outputs['Random Scale'], cloner_logic_node.inputs['Random Scale'])
            self.links.new(self.group_in.outputs['Random Seed'], cloner_logic_node.inputs['Random Seed'])
            self.links.new(self.group_in.outputs['Pick Random Instance'], cloner_logic_node.inputs['Pick Random Instance'])
            self.links.new(self.group_in.outputs['Center Grid'], cloner_logic_node.inputs['Center Grid'])

            # Добавляем соединение для параметра Realize Instances
            if 'Realize Instances' in self.group_in.outputs and 'Realize Instances' in cloner_logic_node.inputs:
                self.links.new(self.group_in.outputs['Realize Instances'], cloner_logic_node.inputs['Realize Instances'])
                print("Connected Realize Instances parameter to Grid Cloner Logic")

            # Сохраняем ссылку на финальный узел для дальнейшей обработки
            self.cloner_logic_node = cloner_logic_node

            return True
        except Exception as e:
            print(f"Ошибка при создании Grid узлов: {e}")
            return False

    def add_global_transform(self):
        """
        Добавляет глобальные трансформации.

        Использует новую модульную функцию глобальных трансформаций.

        Returns:
            bool: True если добавление прошло успешно
        """
        try:
            # Применяем глобальные трансформации используя новую модульную функцию
            transform = apply_global_transforms(
                self.nodes,
                self.links,
                self.cloner_logic_node,
                'Geometry',
                self.group_in
            )

            if transform:
                # Обновляем финальный узел
                self.final_node = transform
                self.final_socket = 'Geometry'
                print(f"Grid клонер создан успешно")
                return True
            else:
                return False
        except Exception as e:
            print(f"Ошибка при добавлении глобальных трансформаций: {e}")
            return False

    def build(self):
        """
        Основной метод построения Grid узлов.

        Переопределяет базовый метод для добавления Grid-специфичной логики.

        Returns:
            bool: True если построение прошло успешно
        """
        try:
            # Настраиваем Grid-специфичный интерфейс (очищает и пересоздает)
            if not self.setup_grid_interface():
                return False

            # Настраиваем общие сокеты
            if not self.setup_common_sockets():
                return False

            # Создаем Grid-специфичные узлы
            if not self.build_grid_nodes():
                return False

            # Добавляем глобальные трансформации
            if not self.add_global_transform():
                return False

            # Настраиваем анти-рекурсию
            if not self.setup_anti_recursion(self.final_node, self.final_socket):
                return False

            return True
        except Exception as e:
            print(f"Ошибка при построении Grid узлов: {e}")
            return False
