"""
Node operations utilities for Geometry Nodes.

This module provides utilities for working with Blender's Geometry Nodes,
including node group management, node utilities, and modifier operations.
"""

# Import all functionality from node operation modules for backward compatibility
from .node_utils import *

# Public API
__all__ = [
    # From node_utils
    'find_socket_by_name',
    'connect_sockets',
    'disconnect_socket',
    'get_node_by_name',
    'add_node_to_group',
    'remove_node_from_group',
    'create_independent_node_group',

    # Note: node_groups.py was removed as it duplicated functionality from node_utils.py
    # Note: modifiers.py was removed as it was mostly empty
]

def register():
    """Register node operations components"""
    pass

def unregister():
    """Unregister node operations components"""
    pass
