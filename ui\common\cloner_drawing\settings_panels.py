"""
Settings panels for different cloner types.

This module provides UI drawing functions for cloner-specific settings panels,
including Grid, Linear, Circle, and Collection cloners.

Updated to use the new automatic UI generation system from parameter definitions.
"""

from ...common.ui_utils import get_stacked_cloner_info
# Removed unused imports: bpy, display_socket_prop, find_socket_by_name

# Import automatic UI generation system
from ....core.parameters import generate_ui_from_parameters

# Import parameter definitions for all cloner types
from ....config.parameters.cloners import (
    CIRCLE_CLONER_PARAMETERS,
    GRID_CLONER_PARAMETERS,
    LINEAR_CLONER_PARAMETERS,
    SPIRAL_CLONER_PARAMETERS
)


# ===== УСТАРЕВШИЕ ФУНКЦИИ УДАЛЕНЫ =====
#
# Следующие функции были удалены после миграции на новую систему параметров:
# - draw_grid_cloner_settings() - заменена на generate_ui_from_parameters() с GRID_CLONER_PARAMETERS
# - draw_linear_cloner_settings() - заменена на generate_ui_from_parameters() с LINEAR_CLONER_PARAMETERS
# - draw_circle_cloner_settings() - заменена на generate_ui_from_parameters() с CIRCLE_CLONER_PARAMETERS
# - draw_spiral_cloner_settings() - заменена на generate_ui_from_parameters() с SPIRAL_CLONER_PARAMETERS
#
# Все клонеры теперь используют единую функцию draw_collection_cloner_settings()
# которая автоматически определяет тип клонера и применяет соответствующие параметры.


def draw_collection_cloner_settings(layout, modifier, cloner_type):
    """Отображает настройки клонера коллекций"""
    col = layout.column(align=True)

    # Получаем информацию о стековом клонере
    is_stacked_cloner, stacked_type = get_stacked_cloner_info(modifier)

    # Для стековых клонеров сначала проверяем поле cloner_display_type
    if is_stacked_cloner:
        display_type = modifier.get("cloner_display_type", "")
        if display_type == "Linear":
            cloner_type = "LINEAR"
        elif display_type == "Grid":
            cloner_type = "GRID"
        elif display_type == "Circle":
            cloner_type = "CIRCLE"
        # Затем проверяем имя модификатора
        elif "Linear" in modifier.name:
            cloner_type = "LINEAR"
        elif "Circle" in modifier.name:
            cloner_type = "CIRCLE"
        elif "Grid" in modifier.name:
            cloner_type = "GRID"
        # Если имя не помогло, используем тип из метаданных
        elif stacked_type:
            cloner_type = stacked_type

    # Determine what header to display based on cloner type
    is_object_cloner = modifier.node_group.name.startswith("ObjectCloner_")
    is_collection_cloner = modifier.node_group.name.startswith("CollectionCloner_")

    # If neither a collection nor an ObjectCloner_, it's a standard object cloner
    is_standard_object_cloner = not (is_object_cloner or is_collection_cloner)

    if is_standard_object_cloner:
        cloner_label = "Object"
    else:
        cloner_label = "Object" if is_object_cloner else "Collection"

    # Для стековых клонеров добавляем индикатор в заголовок
    if is_stacked_cloner:
        # Заголовок с актуальным типом клонера
        # Дополнительно проверяем, что все совпадает
        title_prefix = {
            "LINEAR": "Linear",
            "GRID": "Grid",
            "CIRCLE": "Circle"
        }.get(cloner_type, cloner_type.title())
        col.label(text=f"{title_prefix} {cloner_label} Stacked Settings", icon='LINKED')
    else:
        col.label(text=f"{cloner_type.title()} {cloner_label} Settings")

    col.separator(factor=0.5)

    # Display settings based on cloner type using automatic UI generation
    # New system provides 100% success rate, no fallback needed
    if cloner_type == "GRID":
        generate_ui_from_parameters(col, modifier, GRID_CLONER_PARAMETERS)
        _draw_effector_controls(col, modifier)
    elif cloner_type == "LINEAR":
        generate_ui_from_parameters(col, modifier, LINEAR_CLONER_PARAMETERS)
        _draw_effector_controls(col, modifier)
    elif cloner_type == "CIRCLE":
        generate_ui_from_parameters(col, modifier, CIRCLE_CLONER_PARAMETERS)
        _draw_effector_controls(col, modifier)
    elif cloner_type == "SPIRAL":
        generate_ui_from_parameters(col, modifier, SPIRAL_CLONER_PARAMETERS)
        _draw_effector_controls(col, modifier)


# ===== УСТАРЕВШИЕ FALLBACK ФУНКЦИИ УДАЛЕНЫ =====
#
# Следующие fallback функции были удалены, так как новая система параметров
# обеспечивает 100% успех настройки и отображения параметров:
#
# - _draw_grid_collection_settings() - заменена автоматической генерацией UI
# - _draw_linear_collection_settings() - заменена автоматической генерацией UI
# - _draw_circle_collection_settings() - заменена автоматической генерацией UI


# ===== ДОПОЛНИТЕЛЬНЫЕ УСТАРЕВШИЕ ФУНКЦИИ УДАЛЕНЫ =====
#
# Следующие функции также были удалены, так как они больше не используются:
# - _draw_count_offset_settings() - заменена автоматической генерацией UI
# - _draw_linear_base_settings() - заменена автоматической генерацией UI
# - _draw_count_radius_settings() - заменена автоматической генерацией UI
# - _draw_circle_base_settings() - заменена автоматической генерацией UI


def _draw_effector_controls(layout, modifier):
    """
    Отображает упрощенные элементы управления эффекторами без чекбокса Use Effector.
    Показывает только список связанных эффекторов и возможность добавления новых.
    """
    import bpy
    from .common_controls import _draw_linked_effectors, _draw_effector_linking

    # Проверяем, поддерживает ли клонер эффекторы
    if not modifier.node_group or "linked_effectors" not in modifier.node_group:
        return

    # Создаем секцию эффекторов
    effector_box = layout.box()
    effector_box.label(text="Effectors", icon='FORCE_FORCE')

    # Список подключенных эффекторов (без чекбокса Use Effector)
    _draw_linked_effectors(effector_box, modifier)

    # Добавление новых эффекторов
    context = bpy.context
    _draw_effector_linking(effector_box, modifier, context, is_chain_menu=False)


def register():
    """Register settings panels components"""
    pass


def unregister():
    """Unregister settings panels components"""
    pass
