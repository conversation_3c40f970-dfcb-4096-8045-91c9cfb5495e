"""
Random Effector Interface Builder

This module contains the main interface group creation for the Random Effector.
It handles the public interface and integration with the logic group.

Uses the unified parameter system for automatic interface creation.
"""

import bpy
from ..base import EffectorBase
from .logic_builder import create_logic_group

def create_main_group(logic_group, name_suffix=""):
    """
    Create a main group for the Random Effector that uses the logic group.
    
    This function creates the node group that provides the public interface
    and connects it to the logic group.
    
    Args:
        logic_group: The logic group to use
        name_suffix: Optional suffix for the node group name
        
    Returns:
        The created node group
    """
    # Создаем новую группу узлов
    main_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=f"RandomEffector{name_suffix}")

    # --- Сначала создаем основные сокеты вручную ---
    # Выходы
    main_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Входы
    main_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    main_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
    main_group.interface.new_socket(name="Strength", in_out='INPUT', socket_type='NodeSocketFloat')

    # Параметры трансформации
    main_group.interface.new_socket(name="Position", in_out='INPUT', socket_type='NodeSocketVector')
    main_group.interface.new_socket(name="Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    main_group.interface.new_socket(name="Scale", in_out='INPUT', socket_type='NodeSocketVector')

    # Специфичные для RandomEffector
    uniform_scale_input = main_group.interface.new_socket(name="Uniform Scale", in_out='INPUT', socket_type='NodeSocketBool')
    uniform_scale_input.default_value = True

    seed_input = main_group.interface.new_socket(name="Seed", in_out='INPUT', socket_type='NodeSocketInt')
    seed_input.default_value = 0
    seed_input.min_value = 0

    # --- Поддержка полей (опциональная) ---
    # Создаем сокеты для поддержки полей
    main_group.interface.new_socket(name="Use Field", in_out='INPUT', socket_type='NodeSocketBool')
    field_input = main_group.interface.new_socket(name="Field", in_out='INPUT', socket_type='NodeSocketFloat')

    # Используем автоматическую систему параметров только для получения значений по умолчанию
    from ....core.parameters import get_component_parameters
    from ....operations.helpers.parameter_setup.universal_params import setup_random_effector_params

    random_params = get_component_parameters('EFFECTOR', 'RANDOM')
    if random_params:
        print(f"✅ Random Effector parameters found - will be used for UI generation")
    else:
        print(f"⚠️ Random Effector parameters not found - using hardcoded UI generation")

    # --- Создание узлов ---
    nodes = main_group.nodes
    links = main_group.links

    group_input = nodes.new('NodeGroupInput')
    group_output = nodes.new('NodeGroupOutput')

    # Добавляем узел логической группы
    logic_node = nodes.new('GeometryNodeGroup')
    logic_node.node_tree = logic_group

    # Соединяем общие входы с логической группой
    links.new(group_input.outputs['Geometry'], logic_node.inputs['Geometry'])
    links.new(group_input.outputs['Enable'], logic_node.inputs['Enable'])
    links.new(group_input.outputs['Strength'], logic_node.inputs['Strength'])
    links.new(group_input.outputs['Position'], logic_node.inputs['Position'])
    links.new(group_input.outputs['Rotation'], logic_node.inputs['Rotation'])
    links.new(group_input.outputs['Scale'], logic_node.inputs['Scale'])

    # Специфичные соединения
    links.new(group_input.outputs['Uniform Scale'], logic_node.inputs['Uniform Scale'])
    links.new(group_input.outputs['Seed'], logic_node.inputs['Seed'])

    # --- Настройка модификации через поле (если поддерживается) ---
    # Получаем фактор влияния поля
    field_factor = EffectorBase.setup_nodes_for_field_control(
        nodes, links, group_input,
        'Enable', 'Field'
    )

    # Соединяем выход с группой логики
    links.new(logic_node.outputs['Geometry'], group_output.inputs['Geometry'])

    return main_group