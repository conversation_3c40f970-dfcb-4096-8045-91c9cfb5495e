"""
Фабрика для создания стековых клонеров.
Создает node groups для стековых клонеров различных типов.
"""

import bpy
from ....core.factories.universal_factory import universal_factory

def create_stacked_cloner(stacked_type, object_name):
    """
    Создает node group для стекового клонера.

    Args:
        stacked_type: Тип стекового клонера (STACKED_GRID, STACKED_LINEAR, STACKED_CIRCLE)
        object_name: Имя объекта для которого создается клонер

    Returns:
        bpy.types.NodeGroup: Созданная node group или None при ошибке
    """
    try:
        # Получаем базовый тип клонера
        base_type = stacked_type.replace("STACKED_", "")

        # Получаем класс клонера из реестра
        cloner_class = universal_factory._get_component_class('CLONER', stacked_type)
        if not cloner_class:
            print(f"Unknown stacked cloner type: {stacked_type}")
            return None

        # Создаем имя для node group через унифицированную функцию
        from ..common.naming import generate_node_group_name
        node_group_name = generate_node_group_name(object_name, base_type, "stacked")

        # Создаем node group
        node_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=node_group_name)
        print(f"Создана стековая нод-группа: {node_group.name}")

        # NEW: Try to use new parameter system first
        from ....core.parameters import get_component_parameters, build_interface_from_parameters

        # Map stacked types to base types for parameter lookup
        param_type_map = {
            "STACKED_GRID": "GRID",
            "STACKED_LINEAR": "LINEAR",
            "STACKED_CIRCLE": "CIRCLE"
        }

        param_type = param_type_map.get(stacked_type, base_type)
        cloner_params = get_component_parameters('CLONER', param_type)

        # TEST: Try automatic interface creation for testing, but always use existing logic
        if cloner_params:
            # Test automatic interface creation (for validation only)
            test_node_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=f"TEST_{node_group_name}")
            success = build_interface_from_parameters(test_node_group, cloner_params)
            if success:
                print(f"✅ {stacked_type} Cloner interface created automatically from parameter definitions (StackedFactory)")
            else:
                print(f"❌ Failed to create interface automatically ({stacked_type})")
            # Clean up test node group
            bpy.data.node_groups.remove(test_node_group)
        else:
            print(f"❌ {stacked_type} Cloner parameters not found")

        # ALWAYS use existing logic for creating the actual interface and nodes
        _create_base_sockets(node_group, base_type)

        # Создаем узлы для конкретного типа клонера
        if base_type == "GRID":
            _create_grid_nodes(node_group)
        elif base_type == "LINEAR":
            _create_linear_nodes(node_group)
        elif base_type == "CIRCLE":
            _create_circle_nodes(node_group)
        else:
            print(f"Unsupported base type: {base_type}")
            return None

        # Добавляем метаданные
        node_group["is_stacked_cloner"] = True
        node_group["cloner_type"] = base_type
        node_group["linked_effectors"] = []

        return node_group

    except Exception as e:
        print(f"Ошибка при создании стекового клонера: {e}")
        import traceback
        traceback.print_exc()
        return None

def _create_base_sockets(node_group, base_type):
    """Создает базовые сокеты для стекового клонера."""
    # Выходной сокет геометрии
    node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Входной сокет геометрии - ключевое отличие от обычного клонера
    node_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')

    # Параметр для анти-рекурсии - ВСЕГДА создаем для стековых клонеров
    # Получаем текущую настройку анти-рекурсии
    import bpy
    use_anti_recursion = getattr(bpy.context.scene, 'use_anti_recursion', True)

    realize_socket = node_group.interface.new_socket(
        name="Realize Instances",
        in_out='INPUT',
        socket_type='NodeSocketBool'
    )
    realize_socket.default_value = False  # Как в старой реализации
    realize_socket.description = "Enable to prevent recursion depth issues when creating chains of cloners"

    # Add Use Effector parameter for effector integration
    use_effector_socket = node_group.interface.new_socket("Use Effector", in_out='INPUT', socket_type='NodeSocketBool')
    use_effector_socket.default_value = True

def _create_grid_nodes(node_group):
    """Создает узлы для Grid стекового клонера."""
    # Создаем сокеты с правильными значениями по умолчанию (как у объектных клонеров)
    count_x = node_group.interface.new_socket(name="Count X", in_out='INPUT', socket_type='NodeSocketInt')
    count_x.default_value = 3

    count_y = node_group.interface.new_socket(name="Count Y", in_out='INPUT', socket_type='NodeSocketInt')
    count_y.default_value = 3

    count_z = node_group.interface.new_socket(name="Count Z", in_out='INPUT', socket_type='NodeSocketInt')
    count_z.default_value = 1

    spacing = node_group.interface.new_socket(name="Spacing", in_out='INPUT', socket_type='NodeSocketVector')
    spacing.default_value = (1.0, 1.0, 1.0)  # Как в backup файле

    # Добавляем общие сокеты
    _add_common_sockets(node_group)

    # Создаем реальные узлы (используем оригинальную логику из backup)
    return _build_grid_nodes_structure(node_group)

def _create_linear_nodes(node_group):
    """Создает узлы для Linear стекового клонера."""
    # Создаем сокеты с правильными значениями по умолчанию (как у объектных клонеров)
    count = node_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
    count.default_value = 5

    offset = node_group.interface.new_socket(name="Offset", in_out='INPUT', socket_type='NodeSocketVector')
    offset.default_value = (1.0, 0.0, 0.0)  # Как в backup файле

    # Добавляем общие сокеты
    _add_common_sockets(node_group)

    # Создаем реальные узлы (используем оригинальную логику из backup)
    return _build_linear_nodes_structure(node_group)

def _create_circle_nodes(node_group):
    """Создает узлы для Circle стекового клонера."""
    # Создаем сокеты с правильными значениями по умолчанию (как у объектных клонеров)
    count = node_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
    count.default_value = 8

    radius = node_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
    radius.default_value = 5.0  # Как в backup файле

    # Добавляем сокет Height для совместимости с UI (как у объектных клонеров)
    height = node_group.interface.new_socket(name="Height", in_out='INPUT', socket_type='NodeSocketFloat')
    height.default_value = 0.0

    # Добавляем общие сокеты
    _add_common_sockets(node_group)

    # Создаем реальные узлы (используем оригинальную логику из backup)
    return _build_circle_nodes_structure(node_group)

def _add_common_sockets(node_group):
    """Добавляет общие сокеты для всех типов клонеров."""
    # Глобальные трансформации
    global_pos = node_group.interface.new_socket(name="Global Position", in_out='INPUT', socket_type='NodeSocketVector')
    global_pos.default_value = (0.0, 0.0, 0.0)

    global_rot = node_group.interface.new_socket(name="Global Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    global_rot.default_value = (0.0, 0.0, 0.0)

    # Трансформации инстансов
    instance_rot = node_group.interface.new_socket(name="Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    instance_rot.default_value = (0.0, 0.0, 0.0)
    instance_rot.subtype = 'EULER'

    instance_scale = node_group.interface.new_socket(name="Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')
    instance_scale.default_value = (1.0, 1.0, 1.0)

    # Случайность
    random_seed = node_group.interface.new_socket(name="Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
    random_seed.default_value = 0

    random_pos = node_group.interface.new_socket(name="Random Position", in_out='INPUT', socket_type='NodeSocketVector')
    random_pos.default_value = (0.0, 0.0, 0.0)

    random_rot = node_group.interface.new_socket(name="Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    random_rot.default_value = (0.0, 0.0, 0.0)

    # ИСПРАВЛЕНО: Random Scale как Float (как в backup файле)
    random_scale = node_group.interface.new_socket(name="Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    random_scale.default_value = 0.0

    # Pick Random Instance parameter (for compatibility with new parameter system)
    pick_random = node_group.interface.new_socket(name="Pick Random Instance", in_out='INPUT', socket_type='NodeSocketBool')
    pick_random.default_value = False

    # Дополнительные параметры (как в backup файле)
    center_grid = node_group.interface.new_socket(name="Center Grid", in_out='INPUT', socket_type='NodeSocketBool')
    center_grid.default_value = False  # Как в backup файле

    use_effector = node_group.interface.new_socket(name="Use Effector", in_out='INPUT', socket_type='NodeSocketBool')
    use_effector.default_value = True  # Как в backup файле

    # Анти-рекурсия уже создана в _create_base_sockets, не дублируем

def create_standard_stacked_cloner(context, cloner_type, orig_obj):
    """
    Создает стандартный стековый клонер.

    Полная реализация без зависимости от старого файла.

    Args:
        context: Контекст Blender
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для добавления модификатора

    Returns:
        tuple: (modifier, success) - созданный модификатор и статус успеха
    """
    try:
        from ....core.factories.universal_factory import universal_factory
        from ....operations.helpers.parameter_setup.universal_params import setup_cloner_params
        from ....core.utils.effector_management import update_cloner_with_effectors

        print(f"=== СОЗДАНИЕ СТЕКОВОГО КЛОНЕРА (МОДУЛЬНАЯ СИСТЕМА) ===")
        print(f"Тип: {cloner_type}, Объект: {orig_obj.name}")

        # Преобразуем тип клонера в стековый тип
        stacked_type = f"STACKED_{cloner_type}"
        print(f"Стековый тип: {stacked_type}")

        # Создаем уникальное имя для модификатора через унифицированную функцию
        from ..common.naming import generate_modifier_name
        modifier_name = generate_modifier_name(orig_obj, cloner_type, "stacked")

        print(f"Имя модификатора: {modifier_name}")

        # Создаем node group через модульную систему
        node_group = create_stacked_cloner(stacked_type, f"{orig_obj.name}")
        if not node_group:
            print(f"Ошибка при создании node group для {stacked_type}")
            return None, False

        print(f"Создана нод-группа через модульную систему: {node_group.name}")

        # Создаем модификатор
        modifier = orig_obj.modifiers.new(name=modifier_name, type='NODES')
        modifier.node_group = node_group

        # Сохраняем информацию о типе клонера
        modifier["source_type"] = "STACKED"
        modifier["cloner_type"] = stacked_type
        modifier["is_stacked_cloner"] = True
        modifier["original_object"] = orig_obj.name
        modifier["chain_source_object"] = orig_obj.name

        print(f"Создан модификатор: {modifier_name}")

        # Настраиваем параметры клонера через универсальную систему
        setup_cloner_params(modifier, stacked_type)

        # Обновляем эффекторы если нужно
        try:
            update_cloner_with_effectors(orig_obj, modifier)
        except Exception as e:
            print(f"Ошибка при обновлении эффекторов: {e}")

        print(f"Created {modifier_name} using stacked modifiers")
        return modifier, True

    except Exception as e:
        print(f"Ошибка при создании стекового клонера: {e}")
        import traceback
        traceback.print_exc()
        return None, False


# ===== ФУНКЦИИ ПОСТРОЕНИЯ УЗЛОВ (из backup файла) =====

def _build_grid_nodes_structure(node_group):
    """Создает структуру узлов для Grid стекового клонера (ПОЛНАЯ ЛОГИКА ИЗ BACKUP ФАЙЛА)."""
    try:
        # Получаем контекст для анти-рекурсии
        import bpy
        context = bpy.context

        # Построение базовой структуры узлов (ПРЯМО ИЗ BACKUP ФАЙЛА)
        nodes = node_group.nodes
        links = node_group.links

        # Группы ввода/вывода
        group_in = nodes.new('NodeGroupInput')
        group_out = nodes.new('NodeGroupOutput')

        # --- ПОЛНАЯ ЛОГИКА GRID КЛОНЕРА ИЗ BACKUP ФАЙЛА (строки 152-486) ---
        # Spacing Multiplier
        spacing_multiplier = nodes.new('ShaderNodeVectorMath')
        spacing_multiplier.operation = 'MULTIPLY'
        spacing_multiplier.inputs[1].default_value = (1.0, 1.0, 1.0)
        links.new(group_in.outputs['Spacing'], spacing_multiplier.inputs[0])

        # Разделяем умноженные значения отступов по компонентам
        separate_xyz_spacing = nodes.new('ShaderNodeSeparateXYZ')
        links.new(spacing_multiplier.outputs['Vector'], separate_xyz_spacing.inputs['Vector'])

        # --- Point Generation Logic ---
        # Шаг 1: Создаем линию точек по оси X с правильным отступом
        line_x = nodes.new('GeometryNodeMeshLine')
        line_x.name = "Line X Points"
        line_x.mode = 'OFFSET'  # Режим OFFSET для постоянного отступа
        line_x.count_mode = 'TOTAL'
        links.new(group_in.outputs['Count X'], line_x.inputs['Count'])

        # Создаем вектор отступа для оси X (Spacing X, 0, 0)
        combine_x_offset = nodes.new('ShaderNodeCombineXYZ')
        links.new(separate_xyz_spacing.outputs['X'], combine_x_offset.inputs['X'])
        combine_x_offset.inputs['Y'].default_value = 0.0
        combine_x_offset.inputs['Z'].default_value = 0.0
        links.new(combine_x_offset.outputs['Vector'], line_x.inputs['Offset'])

        # Шаг 2: Создаем линию точек по оси Y с правильным отступом
        line_y = nodes.new('GeometryNodeMeshLine')
        line_y.name = "Line Y Points"
        line_y.mode = 'OFFSET'
        line_y.count_mode = 'TOTAL'
        links.new(group_in.outputs['Count Y'], line_y.inputs['Count'])

        # Создаем вектор отступа для оси Y (0, Spacing Y, 0)
        combine_y_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_y_offset.inputs['X'].default_value = 0.0
        links.new(separate_xyz_spacing.outputs['Y'], combine_y_offset.inputs['Y'])
        combine_y_offset.inputs['Z'].default_value = 0.0
        links.new(combine_y_offset.outputs['Vector'], line_y.inputs['Offset'])

        # Шаг 3: Инстансируем line_x вдоль line_y для создания 2D сетки
        instance_x_on_y = nodes.new('GeometryNodeInstanceOnPoints')
        instance_x_on_y.name = "Instance X on Y"

        # Используем mesh_to_points для конвертации линий в точки
        mesh_to_points_y = nodes.new('GeometryNodeMeshToPoints')
        links.new(line_y.outputs['Mesh'], mesh_to_points_y.inputs['Mesh'])
        links.new(mesh_to_points_y.outputs['Points'], instance_x_on_y.inputs['Points'])
        links.new(line_x.outputs['Mesh'], instance_x_on_y.inputs['Instance'])

        # Реализуем 2D сетку
        realize_2d_grid = nodes.new('GeometryNodeRealizeInstances')
        realize_2d_grid.name = "Realize 2D Grid"
        links.new(instance_x_on_y.outputs['Instances'], realize_2d_grid.inputs['Geometry'])

        # Шаг 4: Создаем линию по оси Z с правильным отступом
        line_z = nodes.new('GeometryNodeMeshLine')
        line_z.name = "Line Z Points"
        line_z.mode = 'OFFSET'
        line_z.count_mode = 'TOTAL'
        links.new(group_in.outputs['Count Z'], line_z.inputs['Count'])

        # Создаем вектор отступа для оси Z (0, 0, Spacing Z)
        combine_z_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_z_offset.inputs['X'].default_value = 0.0
        combine_z_offset.inputs['Y'].default_value = 0.0
        links.new(separate_xyz_spacing.outputs['Z'], combine_z_offset.inputs['Z'])
        links.new(combine_z_offset.outputs['Vector'], line_z.inputs['Offset'])

        # Шаг 5: Инстансируем 2D сетку вдоль линии Z для создания 3D сетки
        instance_2d_on_z = nodes.new('GeometryNodeInstanceOnPoints')
        instance_2d_on_z.name = "Instance 2D on Z"
        mesh_to_points_z = nodes.new('GeometryNodeMeshToPoints')
        links.new(line_z.outputs['Mesh'], mesh_to_points_z.inputs['Mesh'])
        links.new(mesh_to_points_z.outputs['Points'], instance_2d_on_z.inputs['Points'])
        links.new(realize_2d_grid.outputs['Geometry'], instance_2d_on_z.inputs['Instance'])

        # Реализуем 3D сетку
        realize_3d_grid = nodes.new('GeometryNodeRealizeInstances')
        realize_3d_grid.name = "Realize 3D Grid"
        links.new(instance_2d_on_z.outputs['Instances'], realize_3d_grid.inputs['Geometry'])

        # Переключатель между 2D сеткой (если Count Z = 1) и 3D сеткой (если Count Z > 1)
        compare_z_count = nodes.new('FunctionNodeCompare')
        compare_z_count.data_type = 'INT'
        compare_z_count.operation = 'GREATER_THAN'
        compare_z_count.inputs[3].default_value = 1  # Сравниваем с 1
        links.new(group_in.outputs['Count Z'], compare_z_count.inputs[2])  # Input A

        switch_points = nodes.new('GeometryNodeSwitch')
        switch_points.name = "Switch 2D/3D Points"
        switch_points.input_type = 'GEOMETRY'
        links.new(compare_z_count.outputs['Result'], switch_points.inputs['Switch'])
        links.new(realize_2d_grid.outputs['Geometry'], switch_points.inputs[False])  # 2D если Count Z = 1
        links.new(realize_3d_grid.outputs['Geometry'], switch_points.inputs[True])  # 3D если Count Z > 1

        # Продолжаем с центрированием и инстансированием...
        return _complete_grid_logic(nodes, links, group_in, group_out, switch_points, separate_xyz_spacing, context)

    except Exception as e:
        print(f"Ошибка при создании Grid узлов: {e}")
        import traceback
        traceback.print_exc()
        return False


def _build_linear_nodes_structure(node_group):
    """Создает структуру узлов для Linear стекового клонера (ПОЛНАЯ ЛОГИКА ИЗ BACKUP ФАЙЛА)."""
    try:
        # Получаем контекст для анти-рекурсии
        import bpy
        context = bpy.context

        # Построение базовой структуры узлов (ПРЯМО ИЗ BACKUP ФАЙЛА)
        nodes = node_group.nodes
        links = node_group.links

        # Группы ввода/вывода
        group_in = nodes.new('NodeGroupInput')
        group_out = nodes.new('NodeGroupOutput')

        # --- ПОЛНАЯ ЛОГИКА LINEAR КЛОНЕРА ИЗ BACKUP ФАЙЛА (строки 488-704) ---
        # Offset Multiplier
        offset_multiplier = nodes.new('ShaderNodeVectorMath')
        offset_multiplier.operation = 'MULTIPLY'
        offset_multiplier.inputs[1].default_value = (1.0, 1.0, 1.0)
        links.new(group_in.outputs['Offset'], offset_multiplier.inputs[0])

        line_node = nodes.new('GeometryNodeMeshLine')
        if hasattr(line_node, "mode"):
            line_node.mode = 'OFFSET'
        if hasattr(line_node, "count_mode"):
            line_node.count_mode = 'TOTAL'

        # Соединяем параметры Count и Offset
        links.new(group_in.outputs['Count'], line_node.inputs['Count'])

        # Используем умноженный offset
        if 'Offset' in line_node.inputs:
            links.new(offset_multiplier.outputs['Vector'], line_node.inputs['Offset'])
            node_group["has_count_offset"] = True

        # Инстансирование на точках
        instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
        mesh_to_points = nodes.new('GeometryNodeMeshToPoints')

        # Соединяем линию с точками
        links.new(line_node.outputs['Mesh'], mesh_to_points.inputs['Mesh'])

        # Безопасное соединение, проверяя наличие нужных сокетов
        if 'Points' in mesh_to_points.outputs and 'Points' in instance_on_points.inputs:
            links.new(mesh_to_points.outputs['Points'], instance_on_points.inputs['Points'])
        elif 'Geometry' in mesh_to_points.outputs and 'Points' in instance_on_points.inputs:
            links.new(mesh_to_points.outputs['Geometry'], instance_on_points.inputs['Points'])

        # Соединяем входную геометрию с инстансами - ИСПОЛЬЗУЕМ ИНДЕКСЫ
        if 'Instance' in instance_on_points.inputs:
            try:
                links.new(group_in.outputs[0], instance_on_points.inputs['Instance'])
            except Exception as e:
                print(f"Ошибка при соединении геометрии: {e}")

        # Применяем полные трансформации из унифицированного модуля
        from ..transforms import (
            apply_stacked_random_transforms_complete,
            apply_global_transforms,
            create_stacked_anti_recursion
        )

        # Случайные трансформации
        transformed_instances = apply_stacked_random_transforms_complete(nodes.id_data, instance_on_points, group_in, "stacked")
        if not transformed_instances:
            transformed_instances = instance_on_points

        # Instance трансформации (глобальные трансформации)
        final_transform = apply_global_transforms(nodes, links, transformed_instances, 'Instances', group_in, "stacked")
        if not final_transform:
            final_transform = transformed_instances

        # Анти-рекурсия
        use_anti_recursion = getattr(context.scene, 'use_anti_recursion', True)
        print(f"[DEBUG] Создание анти-рекурсии для стекового клонера: use_anti_recursion = {use_anti_recursion}")
        create_stacked_anti_recursion(nodes.id_data, final_transform, group_in, group_out, use_anti_recursion)

        return True

    except Exception as e:
        print(f"Ошибка при создании Linear узлов: {e}")
        import traceback
        traceback.print_exc()
        return False


def _build_circle_nodes_structure(node_group):
    """Создает структуру узлов для Circle стекового клонера (ПОЛНАЯ ЛОГИКА ИЗ BACKUP ФАЙЛА)."""
    try:
        # Получаем контекст для анти-рекурсии
        import bpy
        context = bpy.context

        # Построение базовой структуры узлов (ПРЯМО ИЗ BACKUP ФАЙЛА)
        nodes = node_group.nodes
        links = node_group.links

        # Группы ввода/вывода
        group_in = nodes.new('NodeGroupInput')
        group_out = nodes.new('NodeGroupOutput')

        # --- ПОЛНАЯ ЛОГИКА CIRCLE КЛОНЕРА ИЗ BACKUP ФАЙЛА (строки 711-1000+) ---
        # Radius Multiplier
        radius_multiplier = nodes.new('ShaderNodeMath')
        radius_multiplier.operation = 'MULTIPLY'
        radius_multiplier.inputs[1].default_value = 1.0
        links.new(group_in.outputs['Radius'], radius_multiplier.inputs[0])

        # Height Multiplier
        height_multiplier = nodes.new('ShaderNodeMath')
        height_multiplier.operation = 'MULTIPLY'
        height_multiplier.inputs[1].default_value = 1.0
        links.new(group_in.outputs['Height'], height_multiplier.inputs[0])

        circle_node = nodes.new('GeometryNodeMeshCircle')
        circle_node.fill_type = 'NONE'

        # Соединяем сокеты группы с узлами
        links.new(group_in.outputs['Count'], circle_node.inputs['Vertices'])
        links.new(radius_multiplier.outputs['Value'], circle_node.inputs['Radius'])

        # Добавляем маркер для отображения CountRadius в UI
        node_group["has_count_radius"] = True

        # Создаем узел для установки высоты
        set_height = nodes.new('GeometryNodeSetPosition')
        combine_height = nodes.new('ShaderNodeCombineXYZ')
        combine_height.inputs[0].default_value = 0.0  # X остается без изменений
        combine_height.inputs[1].default_value = 0.0  # Y остается без изменений
        links.new(height_multiplier.outputs['Value'], combine_height.inputs[2])  # Z = Height * Multiplier

        # Инстансирование на точках
        instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
        mesh_to_points = nodes.new('GeometryNodeMeshToPoints')

        # Соединяем окружность с точками
        links.new(circle_node.outputs['Mesh'], mesh_to_points.inputs['Mesh'])

        # Безопасное соединение, проверяя наличие нужных сокетов
        if 'Points' in mesh_to_points.outputs and 'Geometry' in set_height.inputs:
            links.new(mesh_to_points.outputs['Points'], set_height.inputs['Geometry'])
        elif 'Geometry' in mesh_to_points.outputs and 'Geometry' in set_height.inputs:
            links.new(mesh_to_points.outputs['Geometry'], set_height.inputs['Geometry'])

        links.new(combine_height.outputs['Vector'], set_height.inputs['Offset'])

        # Соединяем с инстансированием
        if 'Geometry' in set_height.outputs and 'Points' in instance_on_points.inputs:
            links.new(set_height.outputs['Geometry'], instance_on_points.inputs['Points'])

        # Соединяем входную геометрию с инстансами
        if 'Instance' in instance_on_points.inputs:
            try:
                links.new(group_in.outputs[0], instance_on_points.inputs['Instance'])
            except Exception as e:
                print(f"Ошибка при соединении геометрии CIRCLE: {e}")

        # Добавляем поворот к центру (лицом внутрь)
        face_center = nodes.new('GeometryNodeRotateInstances')
        combine_face_center = nodes.new('ShaderNodeCombineXYZ')
        combine_face_center.inputs[0].default_value = 0.0   # X
        combine_face_center.inputs[1].default_value = 0.0   # Y
        combine_face_center.inputs[2].default_value = 90.0  # Z - поворот на 90 градусов

        links.new(instance_on_points.outputs['Instances'], face_center.inputs['Instances'])
        links.new(combine_face_center.outputs['Vector'], face_center.inputs['Rotation'])

        # Применяем пользовательское вращение после поворота к центру
        rotate_user = nodes.new('GeometryNodeRotateInstances')
        links.new(face_center.outputs['Instances'], rotate_user.inputs['Instances'])
        links.new(group_in.outputs['Instance Rotation'], rotate_user.inputs['Rotation'])

        # Применяем полные трансформации из унифицированного модуля
        from ..transforms import (
            apply_stacked_random_transforms_complete,
            apply_global_transforms,
            create_stacked_anti_recursion
        )

        # Случайные трансформации
        transformed_instances = apply_stacked_random_transforms_complete(nodes.id_data, rotate_user, group_in, "stacked")
        if not transformed_instances:
            transformed_instances = rotate_user

        # Instance трансформации (глобальные трансформации)
        final_transform = apply_global_transforms(nodes, links, transformed_instances, 'Instances', group_in, "stacked")
        if not final_transform:
            final_transform = transformed_instances

        # Анти-рекурсия
        use_anti_recursion = getattr(context.scene, 'use_anti_recursion', True)
        create_stacked_anti_recursion(nodes.id_data, final_transform, group_in, group_out, use_anti_recursion)

        return True

    except Exception as e:
        print(f"Ошибка при создании Circle узлов: {e}")
        import traceback
        traceback.print_exc()
        return False


def _complete_grid_logic(nodes, links, group_in, group_out, switch_points, separate_xyz_spacing, context):
    """Завершает логику Grid клонера - центрирование, инстансирование и трансформации."""
    try:
        # --- Centering Logic ---
        # Рассчитываем смещение для центрирования сетки на основе общего размера

        # Рассчитываем размер X: (Count X - 1) * Spacing X
        count_x_minus_one = nodes.new('ShaderNodeMath')
        count_x_minus_one.operation = 'SUBTRACT'
        count_x_minus_one.inputs[1].default_value = 1.0
        links.new(group_in.outputs['Count X'], count_x_minus_one.inputs[0])

        total_size_x = nodes.new('ShaderNodeMath')
        total_size_x.operation = 'MULTIPLY'
        links.new(count_x_minus_one.outputs['Value'], total_size_x.inputs[0])
        links.new(separate_xyz_spacing.outputs['X'], total_size_x.inputs[1])

        # Рассчитываем размер Y: (Count Y - 1) * Spacing Y
        count_y_minus_one = nodes.new('ShaderNodeMath')
        count_y_minus_one.operation = 'SUBTRACT'
        count_y_minus_one.inputs[1].default_value = 1.0
        links.new(group_in.outputs['Count Y'], count_y_minus_one.inputs[0])

        total_size_y = nodes.new('ShaderNodeMath')
        total_size_y.operation = 'MULTIPLY'
        links.new(count_y_minus_one.outputs['Value'], total_size_y.inputs[0])
        links.new(separate_xyz_spacing.outputs['Y'], total_size_y.inputs[1])

        # Рассчитываем размер Z: (Count Z - 1) * Spacing Z
        count_z_minus_one = nodes.new('ShaderNodeMath')
        count_z_minus_one.operation = 'SUBTRACT'
        count_z_minus_one.inputs[1].default_value = 1.0
        links.new(group_in.outputs['Count Z'], count_z_minus_one.inputs[0])

        total_size_z = nodes.new('ShaderNodeMath')
        total_size_z.operation = 'MULTIPLY'
        links.new(count_z_minus_one.outputs['Value'], total_size_z.inputs[0])
        links.new(separate_xyz_spacing.outputs['Z'], total_size_z.inputs[1])

        # Рассчитываем смещение центра (половина общего размера)
        center_offset_x = nodes.new('ShaderNodeMath')
        center_offset_x.operation = 'DIVIDE'
        center_offset_x.inputs[1].default_value = 2.0
        links.new(total_size_x.outputs['Value'], center_offset_x.inputs[0])

        center_offset_y = nodes.new('ShaderNodeMath')
        center_offset_y.operation = 'DIVIDE'
        center_offset_y.inputs[1].default_value = 2.0
        links.new(total_size_y.outputs['Value'], center_offset_y.inputs[0])

        center_offset_z = nodes.new('ShaderNodeMath')
        center_offset_z.operation = 'DIVIDE'
        center_offset_z.inputs[1].default_value = 2.0
        links.new(total_size_z.outputs['Value'], center_offset_z.inputs[0])

        # Соединяем смещение центра
        center_offset = nodes.new('ShaderNodeCombineXYZ')
        links.new(center_offset_x.outputs['Value'], center_offset.inputs['X'])
        links.new(center_offset_y.outputs['Value'], center_offset.inputs['Y'])
        links.new(center_offset_z.outputs['Value'], center_offset.inputs['Z'])

        # Негатируем для правильного направления смещения
        negate_center = nodes.new('ShaderNodeVectorMath')
        negate_center.operation = 'MULTIPLY'
        negate_center.inputs[1].default_value = (-1.0, -1.0, -1.0)
        links.new(center_offset.outputs['Vector'], negate_center.inputs[0])

        # Создаем нулевой вектор для опции без центрирования
        zero_vector = nodes.new('ShaderNodeCombineXYZ')
        zero_vector.inputs[0].default_value = 0.0
        zero_vector.inputs[1].default_value = 0.0
        zero_vector.inputs[2].default_value = 0.0

        # Переключаем между центрированием и без центрирования на основе опции Center Grid
        center_switch = nodes.new('GeometryNodeSwitch')
        center_switch.input_type = 'VECTOR'
        links.new(group_in.outputs['Center Grid'], center_switch.inputs[0])  # Switch
        links.new(zero_vector.outputs['Vector'], center_switch.inputs[False])  # Без центрирования
        links.new(negate_center.outputs['Vector'], center_switch.inputs[True])  # С центрированием

        # Применяем смещение центрирования к точкам сетки
        center_geometry = nodes.new('GeometryNodeSetPosition')
        center_geometry.name = "Center Grid Points"
        links.new(switch_points.outputs['Output'], center_geometry.inputs['Geometry'])
        links.new(center_switch.outputs['Output'], center_geometry.inputs['Offset'])

        # Инстансирование на точках
        instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
        instance_on_points.name = "Instance Final Geometry"
        links.new(center_geometry.outputs['Geometry'], instance_on_points.inputs['Points'])

        # Соединяем входную геометрию с инстансами
        links.new(group_in.outputs[0], instance_on_points.inputs['Instance'])

        # Применяем полные трансформации из унифицированного модуля
        from ..transforms import (
            apply_stacked_random_transforms_complete,
            apply_global_transforms,
            create_stacked_anti_recursion
        )

        # Случайные трансформации
        transformed_instances = apply_stacked_random_transforms_complete(nodes.id_data, instance_on_points, group_in, "stacked")
        if not transformed_instances:
            transformed_instances = instance_on_points

        # Instance трансформации (глобальные трансформации)
        final_transform = apply_global_transforms(nodes, links, transformed_instances, 'Instances', group_in, "stacked")
        if not final_transform:
            final_transform = transformed_instances

        # Анти-рекурсия
        use_anti_recursion = getattr(context.scene, 'use_anti_recursion', True)
        final_node = create_stacked_anti_recursion(nodes.id_data, final_transform, group_in, group_out, use_anti_recursion)

        return True

    except Exception as e:
        print(f"Ошибка при завершении Grid логики: {e}")
        import traceback
        traceback.print_exc()
        return False
