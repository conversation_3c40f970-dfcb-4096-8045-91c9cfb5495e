"""
Random Effector Module

This module provides the RandomEffector class and related functionality for creating
random effectors that apply random transformations to instances.
"""

import bpy
from ..base import EffectorBase
from ....core.registry import register_effector
from .logic_builder import create_logic_group
from .interface_builder import create_main_group

@register_effector("RANDOM", "Random Effector", "Apply random transformations", "FORCE_TURBULENCE")
class RandomEffector(EffectorBase):
    """Random Effector implementation with modular architecture"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a logic group with the core random effector functionality"""
        return create_logic_group(name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a random effector node group with field control"""
        return create_main_group(logic_group, name_suffix)

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create the complete random effector node group with logic and interface"""
        logic_group = cls.create_logic_group(name_suffix)
        main_group = cls.create_main_group(logic_group, name_suffix)
        return main_group


# Maintain backwards compatibility with the procedural interface
def randomeffector_node_group():
    """Legacy function for backwards compatibility"""
    return RandomEffector.create_node_group()


def create_random_effector_logic_group():
    """Legacy function for backwards compatibility"""
    return RandomEffector.create_logic_group()


def register():
    pass


def unregister():
    pass