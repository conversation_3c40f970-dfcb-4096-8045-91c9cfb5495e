"""
Random Effector Interface Builder

This module contains the main interface group creation for the Random Effector.
It handles the public interface and integration with the logic group.

Uses the unified parameter system for automatic interface creation.
"""

import bpy
from ...base import EffectorBase
from .logic_builder import create_logic_group

def create_main_group(logic_group, name_suffix=""):
    """
    Create a main group for the Random Effector that uses the logic group.
    
    This function creates the node group that provides the public interface
    and connects it to the logic group.
    
    Args:
        logic_group: The logic group to use
        name_suffix: Optional suffix for the node group name
        
    Returns:
        The created node group
    """
    # Создаем новую группу узлов
    main_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=f"RandomEffector{name_suffix}")

    # Используем автоматическое создание интерфейса из параметров
    from .....core.parameters import get_component_parameters, build_interface_from_parameters

    random_params = get_component_parameters('EFFECTOR', 'RANDOM')
    if random_params:
        # Автоматически создаем интерфейс на основе определений параметров
        success = build_interface_from_parameters(main_group, random_params)
        if success:
            print(f"✅ Random Effector interface created automatically from parameter definitions")
        else:
            print(f"❌ Failed to create interface automatically - check parameter definitions")
            print(f"⚠️ Check RANDOM_EFFECTOR_PARAMETERS in config/parameters/effectors/random_effector_params.py")
            return None
    else:
        print(f"❌ Random Effector parameters not found")
        print(f"⚠️ Ensure RANDOM_EFFECTOR_PARAMETERS is properly imported and registered")
        return None

    # --- Создание узлов ---
    nodes = main_group.nodes
    links = main_group.links

    group_input = nodes.new('NodeGroupInput')
    group_output = nodes.new('NodeGroupOutput')

    # Добавляем узел логической группы
    logic_node = nodes.new('GeometryNodeGroup')
    logic_node.node_tree = logic_group

    # Соединяем общие входы с логической группой
    links.new(group_input.outputs['Geometry'], logic_node.inputs['Geometry'])
    links.new(group_input.outputs['Enable'], logic_node.inputs['Enable'])
    links.new(group_input.outputs['Strength'], logic_node.inputs['Strength'])
    links.new(group_input.outputs['Position'], logic_node.inputs['Position'])
    links.new(group_input.outputs['Rotation'], logic_node.inputs['Rotation'])
    links.new(group_input.outputs['Scale'], logic_node.inputs['Scale'])

    # Специфичные соединения
    links.new(group_input.outputs['Uniform Scale'], logic_node.inputs['Uniform Scale'])
    links.new(group_input.outputs['Seed'], logic_node.inputs['Seed'])

    # --- Настройка модификации через поле (если поддерживается) ---
    # Получаем фактор влияния поля
    field_factor = _setup_nodes_for_field_control(
        nodes, links, group_input,
        'Enable', 'Field'
    )

    # Соединяем выход с группой логики
    links.new(logic_node.outputs['Geometry'], group_output.inputs['Geometry'])

    return main_group


def _setup_nodes_for_field_control(nodes, links, group_input, target_socket_name, field_socket_name):
    """
    Setup nodes for field control.
    
    This helper function adds nodes for controlling the effect strength
    with a field, if field support is enabled.
    
    Args:
        nodes: The nodes collection to add nodes to
        links: The links collection to add links to
        group_input: The group input node
        target_socket_name: Name of the socket to affect with the field
        field_socket_name: Name of the field socket
        
    Returns:
        The field factor output
    """
    # Проверяем, есть ли у нас сокеты для поля
    field_socket = None
    use_field_socket = None
    
    for socket in group_input.outputs:
        if socket.name == field_socket_name:
            field_socket = socket
        elif socket.name == 'Use Field':
            use_field_socket = socket
    
    # Если нет сокетов для поля, просто возвращаемся
    if not field_socket or not use_field_socket:
        return None
    
    # Создаем узел для управления полем
    field_switch = nodes.new('GeometryNodeSwitch')
    field_switch.input_type = 'FLOAT'
    field_switch.name = "Field Switch"
    
    # Соединяем сокеты поля
    links.new(use_field_socket, field_switch.inputs[0])  # Switch
    links.new(field_socket, field_switch.inputs['True'])  # With field
    
    # Возвращаем 1.0 (полная сила) если поле не используется
    field_switch.inputs['False'].default_value = 1.0
    
    return field_switch.outputs[0]