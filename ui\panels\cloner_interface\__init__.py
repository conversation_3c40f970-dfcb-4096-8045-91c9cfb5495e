"""
Основной модуль интерфейса клонеров.

Содержит главную панель UI и регистрацию свойств,
объединяя функциональность из подмодулей.
"""

import bpy
from bpy.types import Panel
from bpy.props import StringProperty, BoolProperty, EnumProperty

from ...common.ui_constants import (
    UI_CLONER_PANEL_CATEGORY,
)

from ....core.utils.duplication.chain_tracker import get_cloner_chain_for_object

# Импорты функций интерфейса из подмодулей
from .creation_interface import draw_creation_interface
from .chain_management import draw_cloner_chain
from .cloner_list_display import draw_cloners_list
# Диагностические инструменты удалены

# Обработчик изменения типа источника клонирования
def update_source_type(self, context):
    """Обработчик изменения типа источника клонирования"""
    try:
        obj = context.active_object
        if not obj:
            return

        # Находим активный модификатор клонера
        from ....core.utils.services.base_utils import get_active_cloner
        active_cloner = get_active_cloner(obj)
        if not active_cloner:
            # Если нет активного клонера, просто игнорируем изменение
            # Это предотвратит проблемы при создании эффекторов
            print(f"[DEBUG] update_source_type: нет активного клонера, игнорируем изменение")
            return

        # Получаем текущий тип
        new_type = context.scene.source_type_for_cloner
        old_type = active_cloner.get("source_type", "OBJECT")

        # Проверяем, действительно ли тип изменился
        if new_type == old_type:
            return

        # Обрабатываем переключение типа
        # Используем заглушку - в дальнейшем функция будет перенесена в core.constants
        success = True

        # Обрабатываем результат
        if not success:
            # Восстанавливаем предыдущий тип
            context.scene.source_type_for_cloner = old_type
            return

        # Сохраняем новый тип в модификаторе
        active_cloner["source_type"] = new_type
        print(f"[DEBUG] update_source_type: изменен тип с {old_type} на {new_type}")

    except Exception as e:
        print(f"[ERROR] update_source_type: {e}")
        # В случае ошибки не делаем ничего, чтобы не нарушить работу UI


class CLONERS_PT_Main(Panel):
    """Cloners main panel"""
    bl_label = "Cloners"
    bl_idname = "CLONERS_PT_Main"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = UI_CLONER_PANEL_CATEGORY

    def draw(self, context):
        layout = self.layout

        obj = context.active_object

        if obj is None:
            layout.label(text="No active object")
            return

        # Check if this object has a cloner chain
        cloner_chain = get_cloner_chain_for_object(obj)

        # Add panel for creating new cloners
        creation_box = layout.box()
        draw_creation_interface(context, creation_box)

        # Диагностические инструменты удалены для чистоты интерфейса

        # Add toggle for showing cloner chain
        if cloner_chain:
            layout.separator()
            chain_row = layout.row()
            chain_row.scale_y = 1.5  # UI_SCALE_Y_LARGE
            chain_row.prop(context.scene, "show_cloner_chain",
                         text="Edit Cloner Chain",
                         toggle=True,
                         icon="LINKED")

            # Show cloner chain if toggled on
            if context.scene.show_cloner_chain:
                draw_cloner_chain(context, layout, obj, cloner_chain)

        # List existing cloners on this object
        draw_cloners_list(context, layout)


# Регистрация свойств для кастомных групп и состояний UI
def register_cloner_properties():
    # Keep the property for backwards compatibility, but make it True by default
    # and hide it from the UI
    bpy.types.Scene.custom_groups = bpy.props.BoolProperty(
        name="Use Custom Groups",
        description="Create cloners in custom node groups (enabled by default)",
        default=True,
        options={'HIDDEN'}  # Hide from UI
    )

    # Add an expanded state collection to track UI state
    bpy.types.Scene.cloner_expanded_states = {}

    # Add property to store selected effector for each cloner
    bpy.types.Scene.effector_to_link = StringProperty(
        name="Effector to Link",
        description="Selected effector to link to the cloner",
        default=""
    )

    # Add property to track the currently selected cloner in chain
    bpy.types.Scene.active_cloner_in_chain = StringProperty(
        name="Active Cloner in Chain",
        description="Currently active cloner in the cloner chain",
        default=""
    )

    # Add property to toggle showing cloner chain
    bpy.types.Scene.show_cloner_chain = BoolProperty(
        name="Show Cloner Chain",
        description="Show the full chain of cloners",
        default=True
    )

    # Add property for source type selection (Object/Collection)
    bpy.types.Scene.source_type_for_cloner = EnumProperty(
        name="Clone Source",
        description="What to clone: object or collection",
        items=[
            ('OBJECT', "Object", "Clone selected object"),
            ('COLLECTION', "Collection", "Clone selected collection")
        ],
        default='OBJECT',
        update=update_source_type
    )

    # Add property for collection selection
    bpy.types.Scene.collection_to_clone = StringProperty(
        name="Collection to Clone",
        description="The collection to be cloned"
    )

    # Add property to track last cloned collection
    bpy.types.Scene.last_cloned_collection = StringProperty(
        name="Last Cloned Collection",
        description="The last collection that was cloned",
        default=""
    )

    # Add property for anti-recursion setting
    bpy.types.Scene.use_anti_recursion = BoolProperty(
        name="Use Anti-Recursion",
        description="Prevent infinite recursion when cloning objects that contain cloners",
        default=True
    )

# Функции регистрации и отмены регистрации
def register():
    bpy.utils.register_class(CLONERS_PT_Main)
    register_cloner_properties()

def unregister():
    bpy.utils.unregister_class(CLONERS_PT_Main)

    # Удаление свойств при отмене регистрации
    if hasattr(bpy.types.Scene, "custom_groups"):
        del bpy.types.Scene.custom_groups
    if hasattr(bpy.types.Scene, "cloner_expanded_states"):
        del bpy.types.Scene.cloner_expanded_states
    if hasattr(bpy.types.Scene, "effector_to_link"):
        del bpy.types.Scene.effector_to_link
    if hasattr(bpy.types.Scene, "active_cloner_in_chain"):
        del bpy.types.Scene.active_cloner_in_chain
    if hasattr(bpy.types.Scene, "show_cloner_chain"):
        del bpy.types.Scene.show_cloner_chain
    if hasattr(bpy.types.Scene, "source_type_for_cloner"):
        del bpy.types.Scene.source_type_for_cloner
    if hasattr(bpy.types.Scene, "collection_to_clone"):
        del bpy.types.Scene.collection_to_clone
    if hasattr(bpy.types.Scene, "last_cloned_collection"):
        del bpy.types.Scene.last_cloned_collection
    if hasattr(bpy.types.Scene, "use_anti_recursion"):
        del bpy.types.Scene.use_anti_recursion
