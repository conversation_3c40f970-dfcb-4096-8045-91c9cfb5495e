"""
Основной модуль интерфейса полей.

Содержит главную панель UI для полей и соответствующие функции.
"""

import bpy
from bpy.types import Panel
from ...common.ui_constants import (
    UI_CLONER_PANEL_CATEGORY,
    UI_SCALE_Y_LARGE,
    ICON_FIELD
)

from ....models.fields import FIELD_TYPES
from ....core.registry import component_registry
from .field_display import draw_field_ui


class FIELD_PT_main_panel(Panel):
    """Panel for fields"""
    bl_label = "Fields"
    bl_idname = "FIELD_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = UI_CLONER_PANEL_CATEGORY
    bl_options = {'DEFAULT_CLOSED'}

    def draw(self, context):
        layout = self.layout

        # Кнопки создания полей
        grid = layout.grid_flow(columns=2, even_columns=True)
        grid.scale_y = UI_SCALE_Y_LARGE
        for field_id, field_name, _, field_icon in FIELD_TYPES:
            op = grid.operator("object.create_field", text=field_name, icon=field_icon)
            op.field_type = field_id

        # Отобразим активные поля, если есть
        obj = context.active_object
        if not obj:
            layout.label(text="Select an object")
            return

        # Находим все поля на объекте
        fields = []
        field_group_names = component_registry.get_field_group_names()
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group and any(group_name in mod.node_group.name for group_name in field_group_names.values()):
                fields.append(mod)

        # Отображаем поля только если они есть
        if fields:
            layout.separator()
            layout.label(text=f"Fields: {len(fields)}", icon=ICON_FIELD)

            # Отображаем каждое поле
            for mod in fields:
                draw_field_ui(context, layout, obj, mod)


# Функции регистрации и отмены регистрации
def register():
    bpy.utils.register_class(FIELD_PT_main_panel)

def unregister():
    bpy.utils.unregister_class(FIELD_PT_main_panel)
