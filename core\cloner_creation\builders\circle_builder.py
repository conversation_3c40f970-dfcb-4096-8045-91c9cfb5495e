"""
Построитель узлов для Circle клонера.

Отвечает за создание специфичной логики Circle клонера:
- Создание круговых узлов
- Специфичные параметры Circle клонера
"""

import bpy
from .base_builder import BaseNodeBuilder
from ....models.cloners.circle import CircleCloner
from ....core.cloner_creation.transforms import (
    apply_global_transforms
)

class CircleNodeBuilder(BaseNodeBuilder):
    """
    Построитель узлов для Circle клонера.

    Наследует от BaseNodeBuilder и добавляет специфичную логику для Circle клонера.
    """

    def __init__(self, node_group, orig_obj):
        """
        Инициализация построителя Circle узлов.

        Args:
            node_group: Группа узлов для настройки
            orig_obj: Исходный объект для клонирования
        """
        super().__init__(node_group, orig_obj, "CIRCLE")

    def setup_circle_interface(self):
        """
        Настраивает интерфейс специфичный для Circle клонера.

        UPDATED: Теперь использует новую систему параметров с fallback на ручное создание.

        Returns:
            bool: True если настройка прошла успешно
        """
        try:
            # Очищаем текущую группу узлов и добавляем только необходимые интерфейсные сокеты
            for socket in list(self.node_group.interface.items_tree):
                self.node_group.interface.remove(socket)

            # NEW: Try to use new parameter system first
            from ....core.parameters import get_component_parameters, build_interface_from_parameters

            circle_params = get_component_parameters('CLONER', 'CIRCLE')
            if circle_params:
                # Automatically create interface from parameter definitions
                success = build_interface_from_parameters(self.node_group, circle_params)
                if success:
                    print(f"✅ Circle Cloner interface created automatically from parameter definitions (CircleNodeBuilder)")
                    return True
                else:
                    print(f"❌ Failed to create interface automatically, falling back to manual creation (CircleNodeBuilder)")
            else:
                print(f"❌ Circle Cloner parameters not found, using manual interface creation (CircleNodeBuilder)")

            # LEGACY: Fallback to manual interface creation
            # Добавляем базовый выходной сокет
            self.node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

            # Основные параметры для Circle
            count_socket = self.node_group.interface.new_socket("Count", in_out='INPUT', socket_type='NodeSocketInt')
            count_socket.default_value = 8

            radius_socket = self.node_group.interface.new_socket("Radius", in_out='INPUT', socket_type='NodeSocketFloat')
            radius_socket.default_value = 5.0

            # Add Use Effector parameter for effector integration
            use_effector_socket = self.node_group.interface.new_socket("Use Effector", in_out='INPUT', socket_type='NodeSocketBool')
            use_effector_socket.default_value = True

            return True
        except Exception as e:
            print(f"Ошибка при настройке Circle интерфейса: {e}")
            return False

    def build_circle_nodes(self):
        """
        Строит узлы специфичные для Circle клонера.

        Returns:
            bool: True если построение прошло успешно
        """
        try:
            print(f"Создание Circle клонера с использованием логики из CircleCloner для объекта {self.orig_obj.name}")

            # Создаем logic_group с основной логикой клонера
            logic_group = CircleCloner.create_logic_group(f"_{self.orig_obj.name}")

            # Очищаем существующие узлы
            for node in list(self.nodes):
                self.nodes.remove(node)

            # Создаем базовые узлы заново
            if not self.create_basic_nodes():
                return False

            # Добавляем узел с логикой клонера
            cloner_logic_node = self.nodes.new('GeometryNodeGroup')
            cloner_logic_node.node_tree = logic_group
            cloner_logic_node.name = "Circle Cloner Logic"
            cloner_logic_node.location = (0, 0)

            # Соединяем инстансы объекта с инстансами клонера
            self.links.new(self.object_info.outputs[self.output_socket], cloner_logic_node.inputs['Instance Source'])

            # Соединяем основные параметры
            self.links.new(self.group_in.outputs['Count'], cloner_logic_node.inputs['Count'])
            self.links.new(self.group_in.outputs['Radius'], cloner_logic_node.inputs['Radius'])
            self.links.new(self.group_in.outputs['Instance Scale'], cloner_logic_node.inputs['Instance Scale'])
            self.links.new(self.group_in.outputs['Instance Rotation'], cloner_logic_node.inputs['Instance Rotation'])
            self.links.new(self.group_in.outputs['Random Position'], cloner_logic_node.inputs['Random Position'])
            self.links.new(self.group_in.outputs['Random Rotation'], cloner_logic_node.inputs['Random Rotation'])
            self.links.new(self.group_in.outputs['Random Scale'], cloner_logic_node.inputs['Random Scale'])
            self.links.new(self.group_in.outputs['Random Seed'], cloner_logic_node.inputs['Random Seed'])
            self.links.new(self.group_in.outputs['Pick Random Instance'], cloner_logic_node.inputs['Pick Random Instance'])

            # Сохраняем ссылку на финальный узел для дальнейшей обработки
            self.cloner_logic_node = cloner_logic_node

            return True
        except Exception as e:
            print(f"Ошибка при создании Circle узлов: {e}")
            return False

    def add_global_transform(self):
        """
        Добавляет глобальные трансформации.

        Использует новую модульную функцию глобальных трансформаций.

        Returns:
            bool: True если добавление прошло успешно
        """
        try:
            # Применяем глобальные трансформации используя новую модульную функцию
            transform = apply_global_transforms(
                self.nodes,
                self.links,
                self.cloner_logic_node,
                'Geometry',
                self.group_in
            )

            if transform:
                # Обновляем финальный узел
                self.final_node = transform
                self.final_socket = 'Geometry'
                print(f"Circle клонер создан успешно")
                return True
            else:
                return False
        except Exception as e:
            print(f"Ошибка при добавлении глобальных трансформаций: {e}")
            return False

    def build(self):
        """
        Основной метод построения Circle узлов.

        Переопределяет базовый метод для добавления Circle-специфичной логики.

        Returns:
            bool: True если построение прошло успешно
        """
        try:
            # Настраиваем Circle-специфичный интерфейс (очищает и пересоздает)
            if not self.setup_circle_interface():
                return False

            # Настраиваем общие сокеты
            if not self.setup_common_sockets():
                return False

            # Создаем Circle-специфичные узлы
            if not self.build_circle_nodes():
                return False

            # Добавляем глобальные трансформации
            if not self.add_global_transform():
                return False

            # Настраиваем анти-рекурсию
            if not self.setup_anti_recursion(self.final_node, self.final_socket):
                return False

            return True
        except Exception as e:
            print(f"Ошибка при построении Circle узлов: {e}")
            return False
