"""
Stacked Cloners Module

Этот модуль предоставляет классы стековых клонеров для создания
Circle, Grid и Linear клонеров с Geometry архитектурой.

Стековые клонеры добавляют модификатор к исходному объекту,
а не создают новый объект.
"""

import bpy
import mathutils
from ..base import ClonerBase
from ....core.registry import register_cloner


@register_cloner("STACKED_CIRCLE", "Stacked Circle Cloner", "Create stacked circle instances", "MESH_CIRCLE")
class StackedCircleCloner(ClonerBase):
    """Stacked Circle Cloner implementation with Geometry architecture"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core stacked circle cloner logic"""
        # Используем модульную систему stacked_cloner_modules
        from ....core.cloner_creation.factories.stacked_factory import create_stacked_cloner
        return create_stacked_cloner("STACKED_CIRCLE", name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a stacked circle cloner node group with Geometry input/output"""
        # Логика уже включена в create_stacked_cloner
        return logic_group

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create complete stacked circle cloner node group"""
        return cls.create_logic_group(name_suffix)


@register_cloner("STACKED_GRID", "Stacked Grid Cloner", "Create stacked grid instances", "MESH_GRID")
class StackedGridCloner(ClonerBase):
    """Stacked Grid Cloner implementation with Geometry architecture"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core stacked grid cloner logic"""
        # Используем модульную систему stacked_cloner_modules
        from ....core.cloner_creation.factories.stacked_factory import create_stacked_cloner
        return create_stacked_cloner("STACKED_GRID", name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a stacked grid cloner node group with Geometry input/output"""
        # Логика уже включена в create_stacked_cloner
        return logic_group

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create complete stacked grid cloner node group"""
        return cls.create_logic_group(name_suffix)


@register_cloner("STACKED_LINEAR", "Stacked Linear Cloner", "Create stacked linear instances", "SORTSIZE")
class StackedLinearCloner(ClonerBase):
    """Stacked Linear Cloner implementation with Geometry architecture"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core stacked linear cloner logic"""
        # Используем модульную систему stacked_cloner_modules
        from ....core.cloner_creation.factories.stacked_factory import create_stacked_cloner
        return create_stacked_cloner("STACKED_LINEAR", name_suffix)

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create a stacked linear cloner node group with Geometry input/output"""
        # Логика уже включена в create_stacked_cloner
        return logic_group

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create complete stacked linear cloner node group"""
        return cls.create_logic_group(name_suffix)


def register():
    """Register stacked cloner components"""
    pass


def unregister():
    """Unregister stacked cloner components"""
    pass
