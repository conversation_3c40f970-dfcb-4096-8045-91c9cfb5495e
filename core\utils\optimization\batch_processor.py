"""
Высокопроизводительный пакетный процессор объектов.

Объединяет множественные циклы по bpy.data.objects в один оптимизированный проход,
что значительно улучшает производительность аддона.
"""

import bpy
import time
from typing import List, Callable, Dict, Any, Optional, Set
from .performance_cache import PerformanceTimer, cached_function, _object_cache


class BatchObjectProcessor:
    """
    Высокопроизводительный процессор для пакетной обработки объектов.
    
    Заменяет множественные циклы по bpy.data.objects одним оптимизированным проходом.
    """
    
    def __init__(self):
        self.processors: List[Callable] = []
        self.filters: List[Callable] = []
        self.results: Dict[str, Any] = {}
        self._last_process_time = 0.0
        self._process_cooldown = 0.1  # 100ms между обработками
        
    def add_processor(self, processor: Callable, name: str = None):
        """
        Добавляет процессор для обработки объектов.
        
        Args:
            processor: Функция, принимающая (obj, results) и возвращающая данные
            name: Имя процессора для идентификации результатов
        """
        if name:
            processor._processor_name = name
        self.processors.append(processor)
        
    def add_filter(self, filter_func: Callable):
        """
        Добавляет фильтр для предварительной фильтрации объектов.
        
        Args:
            filter_func: Функция, принимающая obj и возвращающая bool
        """
        self.filters.append(filter_func)
        
    def process_all(self, force: bool = False) -> Dict[str, Any]:
        """
        Обрабатывает все объекты сцены одним проходом.
        
        Args:
            force: Принудительная обработка, игнорируя cooldown
            
        Returns:
            Словарь с результатами всех процессоров
        """
        current_time = time.time()
        
        # Защита от слишком частых вызовов
        if not force and (current_time - self._last_process_time) < self._process_cooldown:
            return self.results
            
        self.results.clear()
        
        with PerformanceTimer("batch_object_processor"):
            processed_count = 0
            
            # Один проход по всем объектам
            for obj in bpy.data.objects:
                # Применяем фильтры
                if not self._should_process_object(obj):
                    continue
                    
                # Применяем все процессоры к объекту
                for processor in self.processors:
                    try:
                        result = processor(obj, self.results)
                        
                        # Сохраняем результат если процессор имеет имя
                        if hasattr(processor, '_processor_name') and result is not None:
                            processor_name = processor._processor_name
                            if processor_name not in self.results:
                                self.results[processor_name] = []
                            self.results[processor_name].append(result)
                            
                    except Exception as e:
                        print(f"[ERROR] BatchProcessor: {processor.__name__}: {e}")
                        
                processed_count += 1
                
            self._last_process_time = current_time
            print(f"[BATCH] Processed {processed_count} objects with {len(self.processors)} processors")
            
        return self.results
        
    def _should_process_object(self, obj) -> bool:
        """Проверяет, должен ли объект быть обработан"""
        # Базовая фильтрация
        if obj.type != 'MESH':
            return False
            
        # Применяем пользовательские фильтры
        for filter_func in self.filters:
            try:
                if not filter_func(obj):
                    return False
            except Exception as e:
                print(f"[ERROR] BatchProcessor filter: {e}")
                return False
                
        return True


# Глобальный экземпляр процессора
_global_processor = BatchObjectProcessor()


# Специализированные процессоры для разных задач

def anti_recursion_processor(obj, results: Dict) -> Optional[Dict]:
    """Процессор для проверки анти-рекурсии клонеров"""
    issues = []
    
    for mod in obj.modifiers:
        if mod.type != 'NODES' or not mod.node_group:
            continue
            
        # Проверяем, является ли это клонером
        node_group = mod.node_group
        is_cloner = any(prefix in node_group.name for prefix in 
                       ["GridCloner", "LinearCloner", "CircleCloner", "CollectionCloner", "ObjectCloner"])
        
        if not is_cloner:
            continue
            
        # Проверяем наличие параметра Realize Instances
        has_realize_param = False
        for socket in node_group.interface.items_tree:
            if (socket.item_type == 'SOCKET' and 
                socket.in_out == 'INPUT' and 
                socket.name == "Realize Instances"):
                has_realize_param = True
                break
                
        if not has_realize_param:
            issues.append({
                'obj_name': obj.name,
                'mod_name': mod.name,
                'issue': 'missing_realize_instances'
            })
            
    return {'obj_name': obj.name, 'issues': issues} if issues else None


def cleanup_processor(obj, results: Dict) -> Optional[Dict]:
    """Процессор для поиска объектов, требующих очистки"""
    cleanup_needed = []
    
    # Проверяем дубликаты без оригиналов
    if "original_obj" in obj:
        original_name = obj["original_obj"]
        try:
            _ = bpy.data.objects[original_name]
        except KeyError:
            cleanup_needed.append({
                'type': 'orphaned_duplicate',
                'original_name': original_name
            })
            
    # Проверяем пустые коллекции клонеров
    for collection in bpy.data.collections:
        if (collection.name.startswith("cloner_") and 
            len(collection.objects) == 0):
            cleanup_needed.append({
                'type': 'empty_collection',
                'collection_name': collection.name
            })
            
    return {'obj_name': obj.name, 'cleanup': cleanup_needed} if cleanup_needed else None


def effector_links_processor(obj, results: Dict) -> Optional[Dict]:
    """Процессор для проверки связей эффекторов"""
    effector_info = []
    
    for mod in obj.modifiers:
        if mod.type != 'NODES' or not mod.node_group:
            continue
            
        # Проверяем эффекторы
        if mod.node_group.name.startswith(("RandomEffector", "NoiseEffector")):
            linked_cloners = []
            
            # Ищем связанные клонеры
            if "linked_cloners" in mod:
                linked_cloners = mod["linked_cloners"]
                
            effector_info.append({
                'mod_name': mod.name,
                'effector_type': mod.node_group.name.split('_')[0],
                'linked_cloners': linked_cloners
            })
            
    return {'obj_name': obj.name, 'effectors': effector_info} if effector_info else None


def chain_status_processor(obj, results: Dict) -> Optional[Dict]:
    """Процессор для проверки статуса цепочек клонеров"""
    chain_info = []
    
    for mod in obj.modifiers:
        if mod.type != 'NODES' or not mod.node_group:
            continue
            
        # Проверяем клонеры
        node_group = mod.node_group
        is_cloner = any(prefix in node_group.name for prefix in 
                       ["GridCloner", "LinearCloner", "CircleCloner", "CollectionCloner", "ObjectCloner"])
        
        if is_cloner:
            chain_data = {
                'mod_name': mod.name,
                'cloner_type': node_group.name.split('_')[0],
                'original_object': mod.get("original_object"),
                'next_cloners': mod.get("next_cloners", []),
                'previous_cloner': mod.get("previous_cloner")
            }
            chain_info.append(chain_data)
            
    return {'obj_name': obj.name, 'chains': chain_info} if chain_info else None


# Удобные функции для использования

def process_all_objects_batch(force: bool = False) -> Dict[str, Any]:
    """
    Обрабатывает все объекты сцены пакетно со всеми процессорами.
    
    Args:
        force: Принудительная обработка
        
    Returns:
        Результаты всех процессоров
    """
    # Настраиваем процессоры если еще не настроены
    if not _global_processor.processors:
        _global_processor.add_processor(anti_recursion_processor, "anti_recursion")
        _global_processor.add_processor(cleanup_processor, "cleanup")
        _global_processor.add_processor(effector_links_processor, "effector_links")
        _global_processor.add_processor(chain_status_processor, "chain_status")
        
    return _global_processor.process_all(force)


@cached_function(_object_cache, timeout=10.0)
def get_cached_batch_results() -> Dict[str, Any]:
    """Возвращает кэшированные результаты пакетной обработки"""
    return process_all_objects_batch()


def get_objects_needing_anti_recursion() -> List[Dict]:
    """Возвращает объекты, требующие обновления анти-рекурсии"""
    results = get_cached_batch_results()
    return results.get("anti_recursion", [])


def get_objects_needing_cleanup() -> List[Dict]:
    """Возвращает объекты, требующие очистки"""
    results = get_cached_batch_results()
    return results.get("cleanup", [])


def get_effector_links_info() -> List[Dict]:
    """Возвращает информацию о связях эффекторов"""
    results = get_cached_batch_results()
    return results.get("effector_links", [])


def get_chain_status_info() -> List[Dict]:
    """Возвращает информацию о статусе цепочек"""
    results = get_cached_batch_results()
    return results.get("chain_status", [])


# Публичный API
__all__ = [
    'BatchObjectProcessor',
    'process_all_objects_batch',
    'get_cached_batch_results',
    'get_objects_needing_anti_recursion',
    'get_objects_needing_cleanup', 
    'get_effector_links_info',
    'get_chain_status_info'
]
