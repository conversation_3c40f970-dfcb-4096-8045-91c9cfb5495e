"""
Spiral Cloner Module

Пример нового клонера, демонстрирующий простоту добавления
с использованием новой системы регистрации.

Для добавления этого клонера нужно изменить ТОЛЬКО ЭТОТ ФАЙЛ!
"""

import bpy
import math
from ..base import ClonerBase
from ....core.registry import register_cloner


@register_cloner("SPIRAL", "Spiral Cloner", "Create a spiral of instances", "FORCE_VORTEX")
class SpiralCloner(ClonerBase):
    """Spiral Cloner implementation - новый клонер добавляется одним файлом!"""

    @classmethod
    def create_logic_group(cls, name_suffix=""):
        """Create a node group with the core spiral cloner logic"""
        # Создаем новую группу узлов
        logic_group = bpy.data.node_groups.new(
            type='GeometryNodeTree',
            name=f"SpiralClonerLogic{name_suffix}"
        )

        # --- Настройка интерфейса ---
        # Выходы
        logic_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

        # Входы
        logic_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')

        count_input = logic_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
        count_input.default_value = 8
        count_input.min_value = 1
        count_input.max_value = 1000

        radius_input = logic_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
        radius_input.default_value = 2.0
        radius_input.min_value = 0.1

        height_input = logic_group.interface.new_socket(name="Height", in_out='INPUT', socket_type='NodeSocketFloat')
        height_input.default_value = 5.0

        turns_input = logic_group.interface.new_socket(name="Turns", in_out='INPUT', socket_type='NodeSocketFloat')
        turns_input.default_value = 2.0
        turns_input.min_value = 0.1

        # --- Создание узлов ---
        nodes = logic_group.nodes
        links = logic_group.links

        # Входной и выходной узлы
        group_input = nodes.new('NodeGroupInput')
        group_output = nodes.new('NodeGroupOutput')
        group_input.location = (-400, 0)
        group_output.location = (400, 0)

        # Создаем спираль через Curve Circle + Curve to Mesh
        curve_circle = nodes.new('GeometryNodeCurvePrimitiveCircle')
        curve_circle.location = (-200, 200)
        curve_circle.mode = 'RADIUS'

        # Подключаем радиус
        links.new(group_input.outputs['Radius'], curve_circle.inputs['Radius'])

        # Resample Curve для создания точек
        resample = nodes.new('GeometryNodeResampleCurve')
        resample.location = (0, 200)
        resample.mode = 'COUNT'
        links.new(curve_circle.outputs['Curve'], resample.inputs['Curve'])
        links.new(group_input.outputs['Count'], resample.inputs['Count'])

        # Curve to Points
        curve_to_points = nodes.new('GeometryNodeCurveToPoints')
        curve_to_points.location = (200, 200)
        links.new(resample.outputs['Curve'], curve_to_points.inputs['Curve'])

        # Instance on Points
        instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
        instance_on_points.location = (200, 0)
        links.new(curve_to_points.outputs['Points'], instance_on_points.inputs['Points'])
        links.new(group_input.outputs['Geometry'], instance_on_points.inputs['Instance'])

        # Выходное соединение
        links.new(instance_on_points.outputs['Instances'], group_output.inputs['Geometry'])

        return logic_group

    @classmethod
    def create_main_group(cls, logic_group, name_suffix=""):
        """Create the main spiral cloner node group"""
        # Для простоты используем логическую группу как основную
        return logic_group

    @classmethod
    def create_node_group(cls, name_suffix=""):
        """Create the complete spiral cloner node group"""
        logic_group = cls.create_logic_group(name_suffix)
        main_group = cls.create_main_group(logic_group, name_suffix)
        return main_group


def register():
    """Register spiral cloner components"""
    pass


def unregister():
    """Unregister spiral cloner components"""
    pass
