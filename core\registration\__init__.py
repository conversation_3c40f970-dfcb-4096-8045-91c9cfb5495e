"""
Core registration module for Advanced Cloners addon.

This module handles the complete registration and unregistration process
for all addon components including modules, operators, properties, and event handlers.
"""

import bpy

# Импорт фабрики автоматической регистрации
from ..factories.registration import auto_register_modules, auto_unregister_modules

# Импорт утилит клонера для очистки при отмене регистрации
from ..utils.duplication.cleanup_manager import (
    restore_original_object,
    cleanup_empty_cloner_collections
)

# Импорт модулей регистрации
from .property_registration import register_ui_properties, unregister_ui_properties


def register():
    """Register all components of the Advanced Cloners addon."""
    print("Registering Advanced Cloners addon...")

    # Register parameter definitions first (needed by components)
    try:
        from ..parameters import register_all_parameter_definitions
        register_all_parameter_definitions()
        print("✅ Parameter definitions registered")
    except Exception as e:
        print(f"❌ Error registering parameter definitions: {e}")

    # Register UI properties
    register_ui_properties()

    # UI operators will be registered automatically with UI components

    # Register GN modules с использованием автоматической регистрации
    print("Registering GN modules...")
    auto_register_modules('advanced_cloners.models.effectors')
    auto_register_modules('advanced_cloners.models.cloners')
    auto_register_modules('advanced_cloners.models.fields')
    print("GN modules registered")

    # Инициализируем реестр компонентов ПОСЛЕ регистрации модулей
    try:
        from ..registry import component_registry, process_registration_queue, get_registration_queue_size

        # Обрабатываем очередь регистрации из декораторов
        queue_size_before = get_registration_queue_size()
        if queue_size_before > 0:
            print(f"Processing {queue_size_before} components from registration queue...")
            process_registration_queue()
            queue_size_after = get_registration_queue_size()
            print(f"Registration queue processed: {queue_size_before - queue_size_after} components registered")
        else:
            print("No components in registration queue - all components registered immediately via decorators")

        print(f"Component registry initialized: {len(component_registry._cloners)} cloners, {len(component_registry._effectors)} effectors, {len(component_registry._fields)} fields")
    except Exception as e:
        print(f"Warning: Could not initialize component registry: {e}")

    # Register UI components manually to avoid import issues
    print("Registering UI components...")
    try:
        # Try to register panels first
        print("  Registering panels...")
        try:
            print("    Registering cloner interface...")
            from ...ui.panels import cloner_interface
            cloner_interface.register()
            print("    Cloner interface registered")
        except Exception as e:
            print(f"    Error registering cloner interface: {e}")

        try:
            print("    Registering effector interface...")
            from ...ui.panels import effector_interface
            effector_interface.register()
            print("    Effector interface registered")
        except Exception as e:
            print(f"    Error registering effector interface: {e}")

        try:
            print("    Registering field interface...")
            from ...ui.panels import field_interface
            field_interface.register()
            print("    Field interface registered")
        except Exception as e:
            print(f"    Error registering field interface: {e}")

        print("  Panels registered successfully")

        # Try to register operators
        print("  Registering UI operators...")
        from ...ui import operators
        operators.register()
        print("  UI operators registered successfully")

        print("UI components registered successfully")
    except Exception as e:
        print(f"Error registering UI components: {e}")
        import traceback
        traceback.print_exc()
    print("UI components registered")

    # Register operators manually to avoid core directory conflicts
    print("Registering operators...")
    try:
        from ...operations import register as register_operations
        register_operations()
        print("Operations registered successfully")
    except Exception as e:
        print(f"Error registering operations: {e}")



    print("Operators registered")

    # Регистрация объединенного обработчика событий
    try:
        from ..utils.event_handling import register as register_event_handlers
        register_event_handlers()
        print("✅ Зарегистрирован объединенный обработчик событий")
    except Exception as e:
        print(f"❌ Ошибка регистрации обработчика событий: {e}")

    # Регистрируем систему кэширования для улучшения производительности
    try:
        from ..utils.optimization import register_cache_handlers
        register_cache_handlers()
        print("Performance caching system registered")
    except Exception as e:
        print(f"Warning: Could not register performance caching: {e}")

    print("Advanced Cloners addon registered successfully")


def unregister():
    """Unregister all components of the Advanced Cloners addon."""
    print("Unregistering Advanced Cloners addon...")

    # Unregister parameter definitions
    try:
        from ..parameters import unregister_all_parameter_definitions
        unregister_all_parameter_definitions()
        print("✅ Parameter definitions unregistered")
    except Exception as e:
        print(f"❌ Error unregistering parameter definitions: {e}")

    # UI operators will be unregistered automatically with UI components

    # Отмена регистрации объединенного обработчика событий
    try:
        from ..utils.event_handling import unregister as unregister_event_handlers
        unregister_event_handlers()
        print("🧹 Отменена регистрация обработчика событий")
    except Exception as e:
        print(f"❌ Ошибка отмены регистрации обработчика событий: {e}")

    # Отменяем регистрацию системы кэширования
    try:
        from ..utils.optimization import unregister_cache_handlers, clear_all_caches
        unregister_cache_handlers()
        clear_all_caches()
        print("Performance caching system unregistered")
    except Exception as e:
        print(f"Warning: Could not unregister performance caching: {e}")

    # Восстанавливаем все оригинальные объекты и удаляем дубликаты
    try:
        # Находим и восстанавливаем все объекты с оригинальными ссылками
        for obj in bpy.data.objects:
            if "original_obj" in obj:
                restore_original_object(obj)

        # Очищаем пустые коллекции клонеров
        cleanup_empty_cloner_collections()

    except Exception as e:
        print(f"Ошибка при восстановлении оригинальных объектов: {e}")

    # Unregister operators manually to avoid core directory conflicts
    print("Unregistering operators...")
    try:
        from ...operations import unregister as unregister_operations
        unregister_operations()
        print("Operations unregistered successfully")
    except Exception as e:
        print(f"Error unregistering operations: {e}")



    print("Operators unregistered")

    # Unregister UI components manually
    print("Unregistering UI components...")
    try:
        from ...ui import unregister as unregister_ui
        unregister_ui()
        print("UI components unregistered successfully")
    except Exception as e:
        print(f"Error unregistering UI components: {e}")
    print("UI components unregistered")

    # Unregister GN modules с использованием автоматической отмены регистрации
    print("Unregistering GN modules...")
    auto_unregister_modules('advanced_cloners.models.fields')
    auto_unregister_modules('advanced_cloners.models.cloners')
    auto_unregister_modules('advanced_cloners.models.effectors')
    print("GN modules unregistered")

    # Unregister UI properties last
    unregister_ui_properties()

    print("Advanced Cloners addon unregistered successfully")
