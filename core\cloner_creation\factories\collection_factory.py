"""
Фабрика для создания клонеров коллекций.
Создает node groups для клонеров коллекций различных типов.
"""

import bpy
from ....core.factories.universal_factory import universal_factory

def create_collection_cloner(collection_type, target_collection, cloner_name, use_anti_recursion=True):
    """
    Создает node group для клонера коллекции.

    Args:
        collection_type: Тип клонера коллекции (COLLECTION_GRID, COLLECTION_LINEAR, COLLECTION_CIRCLE)
        target_collection: Целевая коллекция для клонирования
        cloner_name: Имя клонера
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        bpy.types.NodeGroup: Созданная node group или None при ошибке
    """
    try:
        # Получаем базовый тип клонера
        base_type = collection_type.replace("COLLECTION_", "")

        # Получаем класс клонера из реестра
        cloner_class = universal_factory._get_component_class('CLONER', collection_type)
        if not cloner_class:
            print(f"Unknown collection cloner type: {collection_type}")
            return None

        # Создаем имя для node group в формате, который распознается UI
        type_name = {
            "COLLECTION_GRID": "GRID",
            "COLLECTION_LINEAR": "LINEAR",
            "COLLECTION_CIRCLE": "CIRCLE"
        }.get(collection_type, base_type)

        node_group_name = f"CollectionCloner_{type_name}_{target_collection.name}"

        # Try to use new parameter system first
        from ....core.parameters import get_component_parameters, build_interface_from_parameters

        # Use collection types directly for parameter lookup (not mapped to base types)
        cloner_params = get_component_parameters('CLONER', collection_type)

        # Try to use new parameter system completely for supported collection cloners
        if cloner_params and collection_type in ['COLLECTION_LINEAR', 'COLLECTION_GRID', 'COLLECTION_CIRCLE']:
            print(f"✅ Using new parameter system for {collection_type} cloner")

            # Create node group with proper interface from parameters
            node_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=node_group_name)

            # Build interface from parameter definitions
            success = build_interface_from_parameters(node_group, cloner_params)
            if success:
                print(f"✅ {collection_type} Cloner interface created from parameters")

                # Now create the logic using existing system but with the correct interface
                from ....models.cloners.collection.collection_logic import create_collection_cloner_logic

                # Create logic in the node group we just created with the right interface
                logic_success = _create_collection_logic_in_existing_group(
                    node_group, base_type, target_collection, use_anti_recursion
                )

                if logic_success:
                    print(f"✅ {collection_type} Cloner logic created successfully")
                    return node_group
                else:
                    print(f"❌ Failed to create {collection_type} cloner logic")
                    bpy.data.node_groups.remove(node_group)
            else:
                print(f"❌ Failed to create {collection_type} cloner interface")
                bpy.data.node_groups.remove(node_group)
        else:
            print(f"❌ {collection_type} Cloner parameters not found or not supported, using existing system")

        # Теперь используем методы коллекционных клонеров напрямую
        # Создаем полную node group с рандомными трансформациями и эффекторами
        # Передаем target_collection как collection_obj для установки по умолчанию
        from ....models.cloners.collection.collection_logic import create_collection_cloner_logic
        node_group = create_collection_cloner_logic(
            base_type,
            f"_{target_collection.name}",
            target_collection,
            use_anti_recursion
        )

        if not node_group:
            print(f"Ошибка при создании node group через {cloner_class.__name__}.create_logic_group")
            return None

        # Переименовываем node group в правильный формат для UI
        node_group.name = node_group_name
        print(f"Создана полная коллекционная нод-группа: {node_group.name}")

        # Добавляем метаданные
        node_group["is_collection_cloner"] = True
        node_group["cloner_type"] = base_type
        node_group["target_collection"] = target_collection.name
        node_group["linked_effectors"] = []

        return node_group

    except Exception as e:
        print(f"Ошибка при создании клонера коллекции: {e}")
        import traceback
        traceback.print_exc()
        return None

def _create_base_sockets(node_group, base_type):
    """Создает базовые сокеты для клонера коллекции."""
    # Выходной сокет геометрии
    node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Входной сокет объекта - для коллекционных клонеров
    node_group.interface.new_socket(name="Object", in_out='INPUT', socket_type='NodeSocketObject')

def _create_grid_nodes(node_group, target_collection, use_anti_recursion):
    """Создает узлы для Grid клонера коллекции."""
    # Добавляем специфичные для Grid сокеты
    count_x = node_group.interface.new_socket(name="Count X", in_out='INPUT', socket_type='NodeSocketInt')
    count_x.default_value = 3

    count_y = node_group.interface.new_socket(name="Count Y", in_out='INPUT', socket_type='NodeSocketInt')
    count_y.default_value = 3

    count_z = node_group.interface.new_socket(name="Count Z", in_out='INPUT', socket_type='NodeSocketInt')
    count_z.default_value = 1

    spacing = node_group.interface.new_socket(name="Spacing", in_out='INPUT', socket_type='NodeSocketVector')
    spacing.default_value = (1.0, 1.0, 1.0)

    _add_common_sockets(node_group)

    # Создаем узлы (упрощенная версия для демонстрации)
    nodes = node_group.nodes
    links = node_group.links

    group_in = nodes.new('NodeGroupInput')
    group_out = nodes.new('NodeGroupOutput')

    # Получаем геометрию объекта
    object_info = nodes.new('GeometryNodeObjectInfo')
    links.new(group_in.outputs['Object'], object_info.inputs['Object'])

    # Создаем точки сетки и инстансируем на них геометрию
    grid_points = nodes.new('GeometryNodeMeshGrid')
    instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')

    # Соединяем узлы
    links.new(group_in.outputs['Count X'], grid_points.inputs['Vertices X'])
    links.new(group_in.outputs['Count Y'], grid_points.inputs['Vertices Y'])
    links.new(grid_points.outputs['Mesh'], instance_on_points.inputs['Points'])
    links.new(object_info.outputs['Geometry'], instance_on_points.inputs['Instance'])
    links.new(instance_on_points.outputs['Instances'], group_out.inputs['Geometry'])

def _create_linear_nodes(node_group, target_collection, use_anti_recursion):
    """Создает узлы для Linear клонера коллекции."""
    # Добавляем специфичные для Linear сокеты
    count = node_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
    count.default_value = 5

    offset = node_group.interface.new_socket(name="Offset", in_out='INPUT', socket_type='NodeSocketVector')
    offset.default_value = (1.0, 0.0, 0.0)

    _add_common_sockets(node_group)

    # Создаем узлы
    nodes = node_group.nodes
    links = node_group.links

    group_in = nodes.new('NodeGroupInput')
    group_out = nodes.new('NodeGroupOutput')

    # Получаем геометрию объекта
    object_info = nodes.new('GeometryNodeObjectInfo')
    links.new(group_in.outputs['Object'], object_info.inputs['Object'])

    # Простая реализация
    line_node = nodes.new('GeometryNodeMeshLine')
    line_node.mode = 'OFFSET'
    line_node.count_mode = 'TOTAL'

    instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')

    # Соединяем узлы
    links.new(group_in.outputs['Count'], line_node.inputs['Count'])
    links.new(group_in.outputs['Offset'], line_node.inputs['Offset'])
    links.new(line_node.outputs['Mesh'], instance_on_points.inputs['Points'])
    links.new(object_info.outputs['Geometry'], instance_on_points.inputs['Instance'])
    links.new(instance_on_points.outputs['Instances'], group_out.inputs['Geometry'])

def _create_circle_nodes(node_group, target_collection, use_anti_recursion):
    """Создает узлы для Circle клонера коллекции."""
    # Добавляем специфичные для Circle сокеты
    count = node_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
    count.default_value = 8

    radius = node_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
    radius.default_value = 5.0

    _add_common_sockets(node_group)

    # Создаем узлы
    nodes = node_group.nodes
    links = node_group.links

    group_in = nodes.new('NodeGroupInput')
    group_out = nodes.new('NodeGroupOutput')

    # Получаем геометрию объекта
    object_info = nodes.new('GeometryNodeObjectInfo')
    links.new(group_in.outputs['Object'], object_info.inputs['Object'])

    # Простая реализация
    circle_node = nodes.new('GeometryNodeMeshCircle')
    circle_node.fill_type = 'NONE'

    instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')

    # Соединяем узлы
    links.new(group_in.outputs['Count'], circle_node.inputs['Vertices'])
    links.new(group_in.outputs['Radius'], circle_node.inputs['Radius'])
    links.new(circle_node.outputs['Mesh'], instance_on_points.inputs['Points'])
    links.new(object_info.outputs['Geometry'], instance_on_points.inputs['Instance'])
    links.new(instance_on_points.outputs['Instances'], group_out.inputs['Geometry'])

def _add_common_sockets(node_group):
    """Добавляет общие сокеты для всех типов клонеров."""
    # Глобальные трансформации
    global_pos = node_group.interface.new_socket(name="Global Position", in_out='INPUT', socket_type='NodeSocketVector')
    global_pos.default_value = (0.0, 0.0, 0.0)

    global_rot = node_group.interface.new_socket(name="Global Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    global_rot.default_value = (0.0, 0.0, 0.0)

    # Трансформации инстансов
    instance_rot = node_group.interface.new_socket(name="Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    instance_rot.default_value = (0.0, 0.0, 0.0)
    instance_rot.subtype = 'EULER'

    instance_scale = node_group.interface.new_socket(name="Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')
    instance_scale.default_value = (1.0, 1.0, 1.0)

    # Случайность
    random_seed = node_group.interface.new_socket(name="Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
    random_seed.default_value = 0

    random_pos = node_group.interface.new_socket(name="Random Position", in_out='INPUT', socket_type='NodeSocketVector')
    random_pos.default_value = (0.0, 0.0, 0.0)

    random_rot = node_group.interface.new_socket(name="Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    random_rot.default_value = (0.0, 0.0, 0.0)

    random_scale = node_group.interface.new_socket(name="Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    random_scale.default_value = 0.0

    # Дополнительные параметры
    center_grid = node_group.interface.new_socket(name="Center Grid", in_out='INPUT', socket_type='NodeSocketBool')
    center_grid.default_value = False

    use_effector = node_group.interface.new_socket(name="Use Effector", in_out='INPUT', socket_type='NodeSocketBool')
    use_effector.default_value = True

def create_standard_collection_cloner(context, cloner_type, target_collection_name, use_custom_group=True):
    """
    Создает стандартный клонер для коллекции.

    Полная реализация без зависимости от старого файла.

    Args:
        context: Контекст Blender
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        target_collection_name: Имя коллекции для клонирования
        use_custom_group: Использовать кастомную группу узлов

    Returns:
        bool: True если клонер успешно создан, False в случае ошибки
    """
    try:
        import bmesh
        from ....core.factories.universal_factory import universal_factory
        from ....operations.helpers.parameter_setup.universal_params import setup_cloner_params
        from ....operations.helpers.common.utils import find_layer_collection
        from .chain_manager import find_previous_cloner, setup_collection_chain_properties, register_collection_chain_update

        # Get target collection
        target_collection = bpy.data.collections[target_collection_name]

        # Check if collection has objects
        if len(target_collection.objects) == 0:
            print("Selected collection is empty")
            return False

        # Преобразуем тип клонера в collection тип
        collection_type = f"COLLECTION_{cloner_type}"
        print(f"Collection тип: {collection_type}")

        # Get base modifier name
        base_mod_name = universal_factory.get_cloner_mod_name(collection_type)

        # Используем модульную систему для поиска предыдущего клонера
        previous_cloner_object = find_previous_cloner(target_collection)
        is_cloner_collection = target_collection.name.startswith("cloner_")

        # Create unique name for the cloner object
        cloner_name = f"Cloner_{target_collection.name}"
        counter = 1
        while cloner_name in bpy.data.objects:
            cloner_name = f"Cloner_{target_collection.name}_{counter:03d}"
            counter += 1

        # Create empty mesh as base for the cloner
        mesh = bpy.data.meshes.new(f"{cloner_name}_Mesh")
        cloner_obj = bpy.data.objects.new(cloner_name, mesh)

        # Create collection for the cloner if needed
        cloner_collection_name = f"cloner_{cloner_type.lower()}_{target_collection.name}"
        counter = 1
        while cloner_collection_name in bpy.data.collections:
            cloner_collection_name = f"cloner_{cloner_type.lower()}_{target_collection.name}_{counter:03d}"
            counter += 1

        cloner_collection = bpy.data.collections.new(cloner_collection_name)

        # Сохраняем ссылку на объект клонера в коллекции
        cloner_collection["cloner_obj"] = cloner_obj.name

        # Add cloner to the collection and make sure it's linked to the scene
        cloner_collection.objects.link(cloner_obj)

        # Убедимся, что коллекция клонера добавлена в сцену
        if cloner_collection.name not in context.scene.collection.children:
            try:
                context.scene.collection.children.link(cloner_collection)
            except Exception as e:
                print(f"Ошибка при добавлении коллекции в сцену: {e}")

        # Убеждаемся, что коллекция клонера видима
        layer_collection = context.view_layer.layer_collection
        layer_coll = find_layer_collection(layer_collection, cloner_collection.name)
        if layer_coll:
            # Всегда делаем коллекцию клонера видимой
            layer_coll.exclude = False

            # Гарантируем, что созданный клонер видим
            if cloner_obj:
                cloner_obj.hide_viewport = False
                cloner_obj.hide_render = False

                # Гарантируем, что объект имеет вершины
                if cloner_obj.type == 'MESH' and len(cloner_obj.data.vertices) == 0:
                    # Создаем простую геометрию, чтобы объект был видим
                    bm = bmesh.new()
                    bm.verts.new((0, 0, 0))
                    bm.to_mesh(cloner_obj.data)
                    bm.free()

        # Проверяем и обеспечиваем видимость всех коллекций клонеров в цепочке
        if is_cloner_collection and previous_cloner_object:
            for mod in previous_cloner_object.modifiers:
                if mod.type == 'NODES' and mod.get("cloner_collection"):
                    prev_collection_name = mod.get("cloner_collection")
                    prev_layer_coll = find_layer_collection(layer_collection, prev_collection_name)
                    if prev_layer_coll:
                        prev_layer_coll.exclude = False

        # Создаем уникальное имя для модификатора
        modifier_name = base_mod_name
        counter = 1
        while modifier_name in cloner_obj.modifiers:
            modifier_name = f"{base_mod_name}.{counter:03d}"
            counter += 1

        # Create the geometry nodes modifier
        modifier = cloner_obj.modifiers.new(name=modifier_name, type='NODES')

        # Create node group for cloning the entire collection
        node_group = create_collection_cloner(
            collection_type,
            target_collection,
            cloner_obj.name,
            use_anti_recursion=context.scene.use_anti_recursion
        )

        if not node_group:
            print(f"Ошибка при создании node group для {collection_type}")
            return False

        # Set the node group for the modifier
        modifier.node_group = node_group

        # Initialize effectors list
        node_group["linked_effectors"] = []

        # Save information about source collection
        modifier["source_type"] = "COLLECTION"
        modifier["original_collection"] = target_collection.name
        modifier["cloner_collection"] = cloner_collection.name

        # Устанавливаем коллекцию в сокет Collection модификатора если он есть
        for item in node_group.interface.items_tree:
            if item.item_type == 'SOCKET' and item.in_out == 'INPUT' and item.name == 'Collection':
                socket_id = item.identifier
                try:
                    modifier[socket_id] = target_collection
                    # Дополнительно ищем и устанавливаем коллекцию в узел CollectionInfo
                    for node in node_group.nodes:
                        if node.bl_idname == 'GeometryNodeCollectionInfo':
                            try:
                                node.inputs['Collection'].default_value = target_collection
                            except Exception as e:
                                print(f"Warning: Could not set collection in CollectionInfo node: {e}")
                except Exception as e:
                    print(f"Warning: Could not set Collection parameter: {e}")
                break

        # Устанавливаем параметры клонера через универсальную систему
        setup_cloner_params(modifier, collection_type)

        # Настраиваем свойства цепочки клонеров
        setup_collection_chain_properties(modifier, target_collection, cloner_obj, previous_cloner_object, cloner_collection)

        # Регистрируем обновление цепочки если есть предыдущий клонер
        if previous_cloner_object:
            register_collection_chain_update(previous_cloner_object, cloner_obj)

        # Сохраняем коллекцию для автоматического обновления UI
        if hasattr(context.scene, "last_cloned_collection"):
            context.scene.last_cloned_collection = cloner_collection_name

        # Скрываем исходную коллекцию в layer view
        target_layer_coll = find_layer_collection(layer_collection, target_collection.name)
        if target_layer_coll:
            # Сохраняем текущее состояние видимости
            was_excluded = target_layer_coll.exclude
            modifier["was_collection_excluded"] = was_excluded

            # Скрываем исходную коллекцию только если это не коллекция клонера
            if not target_collection.name.startswith("cloner_"):
                target_layer_coll.exclude = True

        # Делаем объект клонера активным
        for obj in context.selected_objects:
            obj.select_set(False)
        cloner_obj.select_set(True)
        context.view_layer.objects.active = cloner_obj

        # Обновляем UI
        context.view_layer.update()

        return True

    except Exception as e:
        print(f"Error creating collection cloner: {e}")
        return False


def _create_collection_logic_in_existing_group(node_group, base_type, target_collection, use_anti_recursion):
    """
    Создает логику коллекционного клонера в существующей node group с правильным интерфейсом.

    Args:
        node_group: Существующая node group с правильным интерфейсом
        base_type: Базовый тип клонера (GRID, LINEAR, CIRCLE)
        target_collection: Целевая коллекция
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        bool: True если логика создана успешно
    """
    try:
        # Очищаем существующие узлы (кроме Group Input/Output)
        nodes_to_remove = [node for node in node_group.nodes if node.bl_idname not in ['NodeGroupInput', 'NodeGroupOutput']]
        for node in nodes_to_remove:
            node_group.nodes.remove(node)

        # Получаем узлы Group Input и Group Output
        group_input = None
        group_output = None
        for node in node_group.nodes:
            if node.bl_idname == 'NodeGroupInput':
                group_input = node
            elif node.bl_idname == 'NodeGroupOutput':
                group_output = node

        if not group_input or not group_output:
            print("ERROR: Group Input or Group Output not found")
            return False

        # Создаем узлы для коллекционного клонера
        nodes = node_group.nodes
        links = node_group.links

        # Создаем Collection Info узел
        collection_info = nodes.new(type='GeometryNodeCollectionInfo')
        collection_info.name = "Collection Info"
        collection_info.location = (-400, 0)
        collection_info.inputs['Collection'].default_value = target_collection

        # Создаем Instance on Points узел
        instance_on_points = nodes.new(type='GeometryNodeInstanceOnPoints')
        instance_on_points.name = "Instance on Points"
        instance_on_points.location = (200, 0)

        # Создаем Points узел в зависимости от типа клонера
        if base_type == "GRID":
            points_node = nodes.new(type='GeometryNodeMeshGrid')
            points_node.name = "Mesh Grid"
            points_node.location = (-200, 0)

            # Подключаем параметры Grid
            if 'Count X' in group_input.outputs:
                links.new(group_input.outputs['Count X'], points_node.inputs['Size X'])
            if 'Count Y' in group_input.outputs:
                links.new(group_input.outputs['Count Y'], points_node.inputs['Size Y'])
            if 'Spacing' in group_input.outputs:
                links.new(group_input.outputs['Spacing'], points_node.inputs['Vertices X'])

        elif base_type == "LINEAR":
            points_node = nodes.new(type='GeometryNodeMeshLine')
            points_node.name = "Mesh Line"
            points_node.location = (-200, 0)

            # Подключаем параметры Linear
            if 'Count' in group_input.outputs:
                links.new(group_input.outputs['Count'], points_node.inputs['Count'])
            if 'Offset' in group_input.outputs:
                links.new(group_input.outputs['Offset'], points_node.inputs['Offset'])

        elif base_type == "CIRCLE":
            points_node = nodes.new(type='GeometryNodeMeshCircle')
            points_node.name = "Mesh Circle"
            points_node.location = (-200, 0)

            # Подключаем параметры Circle
            if 'Count' in group_input.outputs:
                links.new(group_input.outputs['Count'], points_node.inputs['Vertices'])
            if 'Radius' in group_input.outputs:
                links.new(group_input.outputs['Radius'], points_node.inputs['Radius'])
        else:
            print(f"ERROR: Unsupported base type: {base_type}")
            return False

        # Подключаем основную логику
        links.new(points_node.outputs['Mesh'], instance_on_points.inputs['Points'])
        links.new(collection_info.outputs['Instances'], instance_on_points.inputs['Instance'])
        links.new(instance_on_points.outputs['Instances'], group_output.inputs['Geometry'])

        print(f"✅ Collection {base_type} cloner logic created successfully")
        return True

    except Exception as e:
        print(f"ERROR: Failed to create collection cloner logic: {e}")
        return False
