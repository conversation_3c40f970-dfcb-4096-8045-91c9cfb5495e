"""
Collection Cloner Wrapper Module

Создает двухуровневую архитектуру для клонеров коллекций:
1. Внутренняя группа - чистая логика клонирования
2. Внешняя группа - внутренняя группа + эффектор + финальные узлы
"""

import bpy
from .collection_logic import create_collection_cloner_logic_old


def create_collection_cloner_core_group(cloner_type, name_suffix="", collection_obj=None, use_anti_recursion=False):
    """
    Создает ВНУТРЕННЮЮ группу с чистой логикой клонирования (без эффекторов).

    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        name_suffix: Суффикс для имени группы
        collection_obj: Коллекция для клонирования
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        Внутренняя группа с четко определенными выходами
    """
    # Создаем имя для внутренней группы
    core_group_name = f"CollectionCloner_{cloner_type}_Core{name_suffix}"
    counter = 1
    while core_group_name in bpy.data.node_groups:
        core_group_name = f"CollectionCloner_{cloner_type}_Core_{counter:03d}{name_suffix}"
        counter += 1

    # Создаем внутреннюю группу с чистой логикой клонирования
    core_group = create_collection_cloner_logic_old(
        cloner_type,
        f"_Core{name_suffix}",
        collection_obj,
        use_anti_recursion
    )

    if not core_group:
        print(f"[ERROR] Не удалось создать внутреннюю группу клонера")
        return None

    # Переименовываем в правильное имя
    core_group.name = core_group_name

    # Добавляем метаданные
    core_group["is_cloner_core"] = True
    core_group["cloner_type"] = cloner_type

    print(f"[DEBUG] Создана внутренняя группа клонера: {core_group.name}")
    return core_group


def create_collection_cloner_wrapper_group(cloner_type, name_suffix="", collection_obj=None, use_anti_recursion=False):
    """
    Создает ВНЕШНЮЮ группу-обертку с эффектором.

    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        name_suffix: Суффикс для имени группы
        collection_obj: Коллекция для клонирования
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        Внешняя группа с внутренней группой + эффектором
    """
    # Создаем внутреннюю группу
    core_group = create_collection_cloner_core_group(
        cloner_type, name_suffix, collection_obj, use_anti_recursion
    )

    if not core_group:
        return None

    # Создаем внешнюю группу-обертку
    wrapper_group_name = f"CollectionCloner_{cloner_type}{name_suffix}"
    counter = 1
    while wrapper_group_name in bpy.data.node_groups:
        wrapper_group_name = f"CollectionCloner_{cloner_type}_{counter:03d}{name_suffix}"
        counter += 1

    wrapper_group = bpy.data.node_groups.new(wrapper_group_name, 'GeometryNodeTree')

    # Копируем интерфейс из внутренней группы
    _copy_interface(core_group, wrapper_group)

    # Создаем узлы во внешней группе
    _create_wrapper_nodes(wrapper_group, core_group, use_anti_recursion)

    # Добавляем метаданные
    wrapper_group["is_collection_cloner"] = True
    wrapper_group["cloner_type"] = cloner_type
    wrapper_group["core_group"] = core_group.name
    wrapper_group["linked_effectors"] = []

    if collection_obj:
        wrapper_group["target_collection"] = collection_obj.name

    print(f"[DEBUG] Создана внешняя группа-обертка: {wrapper_group.name}")
    return wrapper_group


def _copy_interface(source_group, target_group):
    """Копирует интерфейс из исходной группы в целевую"""
    try:
        # Копируем входные сокеты
        for input_socket in source_group.interface.items_tree:
            if input_socket.in_out == 'INPUT':
                target_group.interface.new_socket(
                    input_socket.name,
                    in_out='INPUT',
                    socket_type=input_socket.socket_type
                )

        # Копируем выходные сокеты
        for output_socket in source_group.interface.items_tree:
            if output_socket.in_out == 'OUTPUT':
                target_group.interface.new_socket(
                    output_socket.name,
                    in_out='OUTPUT',
                    socket_type=output_socket.socket_type
                )

        print(f"[DEBUG] Интерфейс скопирован из {source_group.name} в {target_group.name}")

    except Exception as e:
        print(f"[ERROR] Ошибка при копировании интерфейса: {e}")


def _create_wrapper_nodes(wrapper_group, core_group, use_anti_recursion):
    """Создает узлы во внешней группе-обертке"""
    nodes = wrapper_group.nodes
    links = wrapper_group.links

    # Входной и выходной узлы
    group_input = nodes.new('NodeGroupInput')
    group_input.location = (-800, 0)

    group_output = nodes.new('NodeGroupOutput')
    group_output.location = (800, 0)

    # Узел внутренней группы клонера
    core_cloner_node = nodes.new('GeometryNodeGroup')
    core_cloner_node.node_tree = core_group
    core_cloner_node.name = "Core Cloner"
    core_cloner_node.location = (-400, 0)

    # Подключаем все входы от group_input к core_cloner_node
    for i, input_socket in enumerate(group_input.outputs):
        if i < len(core_cloner_node.inputs):
            links.new(input_socket, core_cloner_node.inputs[i])

    if use_anti_recursion:
        # Создаем placeholder для эффектора
        effector_placeholder = nodes.new('GeometryNodeGroup')
        effector_placeholder.name = "Effector_Random_Effector"
        effector_placeholder.location = (0, 0)

        # Создаем простую passthrough группу для placeholder
        _create_effector_placeholder(effector_placeholder)

        # Финальный узел Realize Instances
        final_realize = nodes.new('GeometryNodeRealizeInstances')
        final_realize.name = "Final Realize Instances"
        final_realize.location = (200, 100)
        final_realize.inputs[1].default_value = True  # Selection
        final_realize.inputs[2].default_value = True  # Realize All
        final_realize.inputs[3].default_value = 0     # Depth

        # Финальный переключатель
        final_switch = nodes.new('GeometryNodeSwitch')
        final_switch.input_type = 'GEOMETRY'
        final_switch.name = "Final Realize Switch"
        final_switch.location = (400, 0)

        # ПРАВИЛЬНЫЕ СВЯЗИ согласно AFTER.py:
        # 1. Core Cloner -> Effector
        links.new(core_cloner_node.outputs['Geometry'], effector_placeholder.inputs[0])

        # 2. Core Cloner -> Final Realize Instances (параллельно)
        links.new(core_cloner_node.outputs['Geometry'], final_realize.inputs['Geometry'])

        # 3. Effector -> Final Switch.False
        links.new(effector_placeholder.outputs[0], final_switch.inputs['False'])

        # 4. Final Realize -> Final Switch.True (для анти-рекурсии)
        links.new(final_realize.outputs['Geometry'], final_switch.inputs['True'])

        # 5. Group Input.Realize Instances -> Final Switch.Switch
        realize_input = None
        for output in group_input.outputs:
            if "realize" in output.name.lower() and "instances" in output.name.lower():
                realize_input = output
                break

        if realize_input:
            links.new(realize_input, final_switch.inputs['Switch'])

        # 6. Final Switch -> Group Output
        links.new(final_switch.outputs['Output'], group_output.inputs['Geometry'])

        print(f"[DEBUG] Создана архитектура с эффектором и анти-рекурсией")

    else:
        # Без анти-рекурсии - прямое подключение
        links.new(core_cloner_node.outputs['Geometry'], group_output.inputs['Geometry'])
        print(f"[DEBUG] Создана простая архитектура без анти-рекурсии")


def _create_effector_placeholder(effector_node):
    """Создает простую passthrough группу для placeholder эффектора"""
    try:
        passthrough_group = bpy.data.node_groups.new("Effector_Passthrough", 'GeometryNodeTree')

        # Создаем входной и выходной сокеты
        passthrough_group.interface.new_socket("Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
        passthrough_group.interface.new_socket("Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

        # Создаем узлы внутри группы
        group_input = passthrough_group.nodes.new('NodeGroupInput')
        group_output = passthrough_group.nodes.new('NodeGroupOutput')
        group_input.location = (-200, 0)
        group_output.location = (200, 0)

        # Соединяем вход с выходом (passthrough)
        passthrough_group.links.new(group_input.outputs[0], group_output.inputs[0])

        effector_node.node_tree = passthrough_group
        print(f"[DEBUG] Создана passthrough группа для placeholder эффектора")

    except Exception as e:
        print(f"[ERROR] Не удалось создать placeholder эффектора: {e}")
