"""
UI Properties registration module for Advanced Cloners addon.

Handles registration and unregistration of all bpy.types.Scene properties
used by the addon for UI state management and configuration.
"""

import bpy
from bpy.props import StringProperty, EnumProperty, BoolProperty


def register_ui_properties():
    """Register all UI properties used by the Advanced Cloners addon."""
    print("Registering Advanced Cloners UI properties...")

    # Свойства для создания клонеров
    bpy.types.Scene.source_type_for_cloner = EnumProperty(
        name="Source",
        description="What to clone: single object or entire collection",
        items=[
            ('OBJECT', "Object", "Clone selected object"),
            ('COLLECTION', "Collection", "Clone selected collection"),
        ],
        default='OBJECT'
    )

    bpy.types.Scene.collection_to_clone = StringProperty(
        name="Collection to Clone",
        description="The collection to be cloned"
    )

    # Свойства для отображения цепочки клонеров
    bpy.types.Scene.show_cloner_chain = BoolProperty(
        name="Show Cloner Chain",
        description="Show the full chain of cloners",
        default=False
    )

    bpy.types.Scene.active_cloner_in_chain = StringProperty(
        name="Active Cloner in Chain",
        description="Currently active cloner in the cloner chain",
        default=""
    )

    bpy.types.Scene.active_effector_for_cloner = StringProperty(
        name="Active Effector for Cloner",
        description="Currently selected effector for the cloner",
        default=""
    )

    # Импортируем callback для стековых модификаторов
    from ..utils.anti_recursion.anti_recursion_utils import update_stacked_modifiers_callback

    # Свойство для стековых модификаторов
    bpy.types.Scene.use_stacked_modifiers = BoolProperty(
        default=False,
        name="Use Stacked Modifiers",
        description="Create all cloners as modifiers on a single object instead of creating a chain of objects. This allows you to reorder cloners by moving modifiers up/down.",
        update=update_stacked_modifiers_callback
    )

    # Импортируем callback для анти-рекурсии
    from ..utils.anti_recursion.anti_recursion_utils import update_anti_recursion_callback

    # Свойство для автоматического применения анти-рекурсии
    bpy.types.Scene.use_anti_recursion = BoolProperty(
        default=True,
        name="Anti-Recursion",
        description="Automatically apply anti-recursion fix to all new cloners. This prevents recursion depth issues when creating chains of cloners.",
        update=update_anti_recursion_callback
    )

    # Свойство для выбора эффектора в UI
    bpy.types.Scene.effector_to_link = StringProperty(
        name="Effector to Link",
        description="Select an effector to link to the cloner",
        default=""
    )

    print("Advanced Cloners UI properties registered successfully")


def unregister_ui_properties():
    """Unregister all UI properties used by the Advanced Cloners addon."""
    print("Unregistering Advanced Cloners UI properties...")

    # Удаляем свойства для создания клонеров
    if hasattr(bpy.types.Scene, "source_type_for_cloner"):
        del bpy.types.Scene.source_type_for_cloner

    if hasattr(bpy.types.Scene, "collection_to_clone"):
        del bpy.types.Scene.collection_to_clone

    # Удаляем свойства отображения цепочки клонеров
    if hasattr(bpy.types.Scene, "show_cloner_chain"):
        del bpy.types.Scene.show_cloner_chain

    if hasattr(bpy.types.Scene, "active_cloner_in_chain"):
        del bpy.types.Scene.active_cloner_in_chain

    if hasattr(bpy.types.Scene, "active_effector_for_cloner"):
        del bpy.types.Scene.active_effector_for_cloner

    # Удаляем свойство стековых модификаторов
    if hasattr(bpy.types.Scene, "use_stacked_modifiers"):
        del bpy.types.Scene.use_stacked_modifiers

    # Удаляем свойство автоматического применения анти-рекурсии
    if hasattr(bpy.types.Scene, "use_anti_recursion"):
        del bpy.types.Scene.use_anti_recursion

    # Удаляем свойство для выбора эффектора
    if hasattr(bpy.types.Scene, "effector_to_link"):
        del bpy.types.Scene.effector_to_link

    print("Advanced Cloners UI properties unregistered successfully")
