"""
Системные утилиты для Advanced Cloners.

Содержит базовые системы для поддержки работы аддона:
- Система логирования (logging_system)
- Система валидации (validation_system)
"""

# Импорт системы логирования
from .logging_system import (
    ClonerLogger,
    logger
)

# Импорт системы валидации
from .validation_system import (
    ValidationError,
    ClonerValidator,
    PerformanceMonitor,
    performance_monitor
)


def register():
    """Регистрирует все системные компоненты"""
    # Инициализируем логгер
    logger.info("Systems components registered")
    print("✅ Systems components registered")


def unregister():
    """Отменяет регистрацию системных компонентов"""
    logger.info("Systems components unregistered")
    print("🧹 Systems components unregistered")


# Публичный API
__all__ = [
    # Logging System
    'ClonerLogger',
    'logger',

    # Validation System
    'ValidationError',
    'ClonerValidator',
    'PerformanceMonitor',
    'performance_monitor',

    # Utility functions
    'register',
    'unregister'
]
