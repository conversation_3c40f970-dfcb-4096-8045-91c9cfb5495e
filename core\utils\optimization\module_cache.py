"""
Высокопроизводительная система кэширования модулей.

Устраняет повторные импорты модулей, значительно ускоряя загрузку и работу аддона.
"""

import importlib
import sys
import time
from typing import Dict, Any, Optional, Callable, Type
from functools import wraps


class ModuleCache:
    """
    Высокопроизводительный кэш модулей с автоматической очисткой.
    
    Кэширует импортированные модули и их атрибуты для мгновенного доступа.
    """
    
    _module_cache: Dict[str, Any] = {}
    _function_cache: Dict[str, Callable] = {}
    _class_cache: Dict[str, Type] = {}
    _last_cleanup_time = 0.0
    _cleanup_interval = 300.0  # Очистка каждые 5 минут
    
    @classmethod
    def get_module(cls, module_name: str) -> Any:
        """
        Получает модуль из кэша или импортирует его.
        
        Args:
            module_name: Имя модуля для импорта
            
        Returns:
            Импортированный модуль
        """
        if module_name not in cls._module_cache:
            try:
                cls._module_cache[module_name] = importlib.import_module(module_name)
                print(f"[CACHE] Модуль {module_name} импортирован и кэширован")
            except ImportError as e:
                print(f"[ERROR] Не удалось импортировать модуль {module_name}: {e}")
                return None
                
        return cls._module_cache[module_name]
    
    @classmethod
    def get_function(cls, module_name: str, function_name: str) -> Optional[Callable]:
        """
        Получает функцию из модуля с кэшированием.
        
        Args:
            module_name: Имя модуля
            function_name: Имя функции
            
        Returns:
            Функция или None если не найдена
        """
        cache_key = f"{module_name}.{function_name}"
        
        if cache_key not in cls._function_cache:
            module = cls.get_module(module_name)
            if module and hasattr(module, function_name):
                cls._function_cache[cache_key] = getattr(module, function_name)
                print(f"[CACHE] Функция {cache_key} кэширована")
            else:
                print(f"[ERROR] Функция {function_name} не найдена в модуле {module_name}")
                return None
                
        return cls._function_cache[cache_key]
    
    @classmethod
    def get_class(cls, module_name: str, class_name: str) -> Optional[Type]:
        """
        Получает класс из модуля с кэшированием.
        
        Args:
            module_name: Имя модуля
            class_name: Имя класса
            
        Returns:
            Класс или None если не найден
        """
        cache_key = f"{module_name}.{class_name}"
        
        if cache_key not in cls._class_cache:
            module = cls.get_module(module_name)
            if module and hasattr(module, class_name):
                cls._class_cache[cache_key] = getattr(module, class_name)
                print(f"[CACHE] Класс {cache_key} кэширован")
            else:
                print(f"[ERROR] Класс {class_name} не найден в модуле {module_name}")
                return None
                
        return cls._class_cache[cache_key]
    
    @classmethod
    def clear_cache(cls):
        """Очищает весь кэш модулей"""
        cls._module_cache.clear()
        cls._function_cache.clear()
        cls._class_cache.clear()
        print("[CACHE] Кэш модулей очищен")
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, int]:
        """Возвращает статистику кэша"""
        return {
            "modules": len(cls._module_cache),
            "functions": len(cls._function_cache),
            "classes": len(cls._class_cache),
            "total": len(cls._module_cache) + len(cls._function_cache) + len(cls._class_cache)
        }
    
    @classmethod
    def cleanup_if_needed(cls):
        """Очищает кэш если прошло достаточно времени"""
        current_time = time.time()
        if current_time - cls._last_cleanup_time > cls._cleanup_interval:
            # Удаляем модули, которые больше не существуют в sys.modules
            modules_to_remove = []
            for module_name in cls._module_cache:
                if module_name not in sys.modules:
                    modules_to_remove.append(module_name)
            
            for module_name in modules_to_remove:
                del cls._module_cache[module_name]
                print(f"[CACHE] Удален неактивный модуль {module_name}")
            
            cls._last_cleanup_time = current_time


# Удобные функции-обертки для быстрого доступа

def cached_import(module_name: str) -> Any:
    """
    Быстрый импорт модуля с кэшированием.
    
    Args:
        module_name: Имя модуля
        
    Returns:
        Импортированный модуль
    """
    ModuleCache.cleanup_if_needed()
    return ModuleCache.get_module(module_name)


def cached_function(module_name: str, function_name: str) -> Optional[Callable]:
    """
    Быстрое получение функции с кэшированием.
    
    Args:
        module_name: Имя модуля
        function_name: Имя функции
        
    Returns:
        Функция или None
    """
    ModuleCache.cleanup_if_needed()
    return ModuleCache.get_function(module_name, function_name)


def cached_class(module_name: str, class_name: str) -> Optional[Type]:
    """
    Быстрое получение класса с кэшированием.
    
    Args:
        module_name: Имя модуля
        class_name: Имя класса
        
    Returns:
        Класс или None
    """
    ModuleCache.cleanup_if_needed()
    return ModuleCache.get_class(module_name, class_name)


# Декоратор для автоматического кэширования импортов в функциях

def auto_cache_imports(func: Callable) -> Callable:
    """
    Декоратор для автоматического кэширования импортов внутри функций.
    
    Заменяет importlib.import_module на cached_import.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Временно заменяем importlib.import_module на cached_import
        original_import = importlib.import_module
        importlib.import_module = cached_import
        
        try:
            result = func(*args, **kwargs)
        finally:
            # Восстанавливаем оригинальную функцию
            importlib.import_module = original_import
            
        return result
    return wrapper


# Специализированные кэши для часто используемых модулей Advanced Cloners

class AdvancedClonersModuleCache:
    """Специализированный кэш для модулей Advanced Cloners"""
    
    # Часто используемые модули
    _effector_management = None
    _collection_manager = None
    _cleanup_manager = None
    _node_operations = None
    _component_registry = None
    
    @classmethod
    def get_effector_management(cls):
        """Получает модуль управления эффекторами"""
        if cls._effector_management is None:
            cls._effector_management = cached_import("advanced_cloners.core.utils.effector_management")
        return cls._effector_management
    
    @classmethod
    def get_collection_manager(cls):
        """Получает модуль управления коллекциями"""
        if cls._collection_manager is None:
            cls._collection_manager = cached_import("advanced_cloners.core.utils.duplication.collection_manager")
        return cls._collection_manager
    
    @classmethod
    def get_cleanup_manager(cls):
        """Получает модуль очистки"""
        if cls._cleanup_manager is None:
            cls._cleanup_manager = cached_import("advanced_cloners.core.utils.duplication.cleanup_manager")
        return cls._cleanup_manager
    
    @classmethod
    def get_node_operations(cls):
        """Получает модуль операций с узлами"""
        if cls._node_operations is None:
            cls._node_operations = cached_import("advanced_cloners.core.utils.node_operations")
        return cls._node_operations
    
    @classmethod
    def get_component_registry(cls):
        """Получает реестр компонентов"""
        if cls._component_registry is None:
            cls._component_registry = cached_import("advanced_cloners.core.registry.component_registry")
        return cls._component_registry
    
    @classmethod
    def clear_cache(cls):
        """Очищает специализированный кэш"""
        cls._effector_management = None
        cls._collection_manager = None
        cls._cleanup_manager = None
        cls._node_operations = None
        cls._component_registry = None
        print("[CACHE] Специализированный кэш Advanced Cloners очищен")


# Удобные функции для получения часто используемых функций

def get_update_cloner_with_effectors():
    """Быстрое получение функции update_cloner_with_effectors"""
    module = AdvancedClonersModuleCache.get_effector_management()
    return getattr(module, 'update_cloner_with_effectors', None) if module else None


def get_create_cloner_collection():
    """Быстрое получение функции create_cloner_collection"""
    module = AdvancedClonersModuleCache.get_collection_manager()
    return getattr(module, 'create_cloner_collection', None) if module else None


def get_restore_original_object():
    """Быстрое получение функции restore_original_object"""
    module = AdvancedClonersModuleCache.get_cleanup_manager()
    return getattr(module, 'restore_original_object', None) if module else None


def get_component_registry():
    """Быстрое получение реестра компонентов"""
    module = AdvancedClonersModuleCache.get_component_registry()
    return getattr(module, 'component_registry', None) if module else None


# Публичный API
__all__ = [
    'ModuleCache',
    'cached_import',
    'cached_function', 
    'cached_class',
    'auto_cache_imports',
    'AdvancedClonersModuleCache',
    'get_update_cloner_with_effectors',
    'get_create_cloner_collection',
    'get_restore_original_object',
    'get_component_registry'
]
