"""
Field UI operators module.

This module contains all UI operators related to field functionality,
including field creation, gizmo management, and field controls.
"""

# Import all field operators from the main module
from .field_ui_ops import (
    FIELD_OT_toggle_expanded,
    FIELD_OT_create_field,
    FIELD_OT_select_gizmo,
    FIELD_OT_create_sphere_gizmo,
    FIELD_OT_adjust_field_strength
)

# Public API
__all__ = [
    'FIELD_OT_toggle_expanded',
    'FIELD_OT_create_field',
    'FIELD_OT_select_gizmo',
    'FIELD_OT_create_sphere_gizmo',
    'FIELD_OT_adjust_field_strength'
]


def register():
    """Register field UI operators"""
    from . import field_ui_ops
    field_ui_ops.register()


def unregister():
    """Unregister field UI operators"""
    from . import field_ui_ops
    field_ui_ops.unregister()
