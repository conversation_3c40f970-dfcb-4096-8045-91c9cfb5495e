"""
Noise Effector Parameter Definitions

This module defines all parameters for the Noise Effector component using the unified
parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

Noise Effector applies noise-based transformations to objects with configurable settings.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    ParameterSubtype,
    get_standard_parameter_set
)

# Basic Noise Effector parameters
NOISE_EFFECTOR_BASIC_GROUP = ParameterGroup(
    name="noise_settings",
    description="Noise Effector specific settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Uniform Scale",
            param_type=ParameterType.BOOL,
            default_value=True,
            description="Use the same noise value for all scale axes",
            ui_group="Scale Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Symmetric Translation",
            param_type=ParameterType.BOOL,
            default_value=False,
            description="Apply symmetric noise to position",
            ui_group="Position Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Symmetric Rotation",
            param_type=ParameterType.BOOL,
            default_value=False,
            description="Apply symmetric noise to rotation",
            ui_group="Rotation Settings",
            ui_order=1
        )
    ]
)

# Noise control parameters
NOISE_CONTROL_GROUP = ParameterGroup(
    name="noise_control",
    description="Noise generation control parameters",
    ui_order=20,
    parameters=[
        ParameterDefinition(
            name="Noise Scale",
            param_type=ParameterType.FLOAT,
            default_value=0.5,
            min_value=0.0,
            max_value=5.0,
            description="Overall scale of the noise pattern",
            ui_group="Noise Controls",
            ui_order=1
        ),
        ParameterDefinition(
            name="Noise Detail",
            param_type=ParameterType.FLOAT,
            default_value=2.0,
            min_value=0.0,
            max_value=16.0,
            description="Amount of noise detail (number of octaves)",
            ui_group="Noise Controls",
            ui_order=2
        ),
        ParameterDefinition(
            name="Noise Roughness",
            param_type=ParameterType.FLOAT,
            default_value=0.5,
            min_value=0.0,
            max_value=1.0,
            description="Roughness of noise pattern",
            ui_group="Noise Controls",
            ui_order=3
        ),
        ParameterDefinition(
            name="Noise Lacunarity",
            param_type=ParameterType.FLOAT,
            default_value=2.0,
            min_value=0.01,
            max_value=10.0,
            description="Gap between noise frequencies",
            ui_group="Noise Controls",
            ui_order=4
        ),
        ParameterDefinition(
            name="Noise Distortion",
            param_type=ParameterType.FLOAT,
            default_value=0.0,
            min_value=0.0,
            max_value=10.0,
            description="Amount of noise distortion",
            ui_group="Noise Controls",
            ui_order=5
        )
    ]
)

# Noise position parameters
NOISE_POSITION_GROUP = ParameterGroup(
    name="noise_position",
    description="Noise position and animation parameters",
    ui_order=30,
    parameters=[
        ParameterDefinition(
            name="Noise Position",
            param_type=ParameterType.VECTOR,
            default_value=(0.0, 0.0, 0.0),
            description="Offset position of noise pattern",
            ui_group="Noise Position",
            ui_order=1
        ),
        ParameterDefinition(
            name="Noise XYZ Scale",
            param_type=ParameterType.VECTOR,
            default_value=(1.0, 1.0, 1.0),
            description="Per-axis scale of noise pattern",
            ui_group="Noise Position",
            ui_order=2
        ),
        ParameterDefinition(
            name="Speed",
            param_type=ParameterType.FLOAT,
            default_value=0.0,
            min_value=0.0,
            max_value=10.0,
            description="Animation speed of noise pattern",
            ui_group="Noise Animation",
            ui_order=1
        ),
        ParameterDefinition(
            name="Seed",
            param_type=ParameterType.INT,
            default_value=0,
            min_value=0,
            max_value=10000,
            description="Seed for noise generation",
            ui_group="Noise Animation",
            ui_order=2
        )
    ]
)

# Complete parameter set for Noise Effector
NOISE_EFFECTOR_PARAMETERS = ComponentParameterSet(
    component_type="EFFECTOR",
    component_id="NOISE",
    description="Noise Effector parameter set - applies noise-based transformations",
    version="1.0",
    groups=[
        get_standard_parameter_set("effector_io"),  # Standard effector input/output
        get_standard_parameter_set("effector_base"),  # Base effector parameters (Enable, Strength)
        get_standard_parameter_set("transform"),  # Standard transform parameters
        NOISE_EFFECTOR_BASIC_GROUP,
        NOISE_CONTROL_GROUP,
        NOISE_POSITION_GROUP,
        get_standard_parameter_set("field_control")  # Field influence parameters
    ]
)