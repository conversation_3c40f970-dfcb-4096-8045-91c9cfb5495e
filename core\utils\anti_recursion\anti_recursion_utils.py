"""
Utilities for managing anti-recursion settings in cloners.
"""

import bpy

def update_anti_recursion_for_all_cloners(context):
    """
    Update the Realize Instances parameter for all cloners based on the current anti-recursion setting.
    Uses improved anti-recursion system that fixes red connections and effector issues.

    Args:
        context: Blender context
    """
    # Get the current anti-recursion setting
    use_anti_recursion = context.scene.use_anti_recursion

    # Используем кэшированный импорт для оптимизации
    from ..optimization import get_update_cloner_with_effectors

    try:
        update_cloner_with_effectors = get_update_cloner_with_effectors()
        if not update_cloner_with_effectors:
            print("[ERROR] Failed to get update_cloner_with_effectors function")
            return

        # Диагностические операторы удалены - функция apply_anti_recursion_to_cloner больше не доступна
        apply_anti_recursion_to_cloner = None
    except Exception as e:
        print(f"[ERROR] Failed to import required modules: {e}")
        return

    # Используем пакетную обработку для оптимизации
    from ..optimization import get_objects_needing_anti_recursion

    # Получаем объекты, требующие обновления анти-рекурсии
    objects_needing_update = get_objects_needing_anti_recursion()
    updated_count = 0

    print(f"[BATCH] Найдено {len(objects_needing_update)} объектов для обновления анти-рекурсии")

    # Обрабатываем только объекты, которые действительно нуждаются в обновлении
    for obj_data in objects_needing_update:
        obj_name = obj_data['obj_name']
        try:
            obj = bpy.data.objects[obj_name]
        except KeyError:
            continue  # Объект был удален

        # Find all geometry nodes modifiers that are cloners
        for modifier in obj.modifiers:
            if modifier.type != 'NODES' or not modifier.node_group:
                continue

            node_group = modifier.node_group

            # Check if this is a cloner node group
            is_cloner = False
            for prefix in ["GridCloner", "LinearCloner", "CircleCloner", "CollectionCloner", "ObjectCloner"]:
                if prefix in node_group.name:
                    is_cloner = True
                    break

            if not is_cloner:
                continue

            # Update or add the Realize Instances parameter
            has_realize_param = False
            realize_socket_identifier = None
            for socket in node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == "Realize Instances":
                    # Update the parameter value in interface
                    socket.default_value = use_anti_recursion
                    realize_socket_identifier = socket.identifier
                    has_realize_param = True
                    break

            # ИСПРАВЛЕНИЕ ПРОБЛЕМЫ 1: Обновляем значение напрямую в модификаторе
            if has_realize_param and realize_socket_identifier:
                try:
                    # Устанавливаем значение напрямую в модификаторе
                    modifier[realize_socket_identifier] = use_anti_recursion
                    print(f"[DEBUG] Обновлено значение Realize Instances в модификаторе {modifier.name}: {use_anti_recursion}")
                except Exception as e:
                    print(f"[ERROR] Не удалось обновить значение в модификаторе {modifier.name}: {e}")
                    # Fallback: пробуем по имени
                    try:
                        modifier["Realize Instances"] = use_anti_recursion
                        print(f"[DEBUG] Обновлено значение Realize Instances (fallback) в модификаторе {modifier.name}: {use_anti_recursion}")
                    except Exception as e2:
                        print(f"[ERROR] Fallback также не сработал для {modifier.name}: {e2}")

            # If the parameter doesn't exist, apply anti-recursion system
            if not has_realize_param:
                if apply_anti_recursion_to_cloner:
                    if apply_anti_recursion_to_cloner(node_group):
                        print(f"[DEBUG] Applied improved anti-recursion to {node_group.name}")
                        updated_count += 1
                else:
                    print(f"[DEBUG] Anti-recursion function not available for {node_group.name}")
                    # Заглушка - просто добавляем параметр Realize Instances
                    try:
                        # Добавляем базовый параметр Realize Instances
                        socket = node_group.interface.new_socket(
                            name="Realize Instances",
                            in_out='INPUT',
                            socket_type='NodeSocketBool'
                        )
                        socket.default_value = use_anti_recursion
                        updated_count += 1
                    except Exception as e:
                        print(f"[ERROR] Failed to add Realize Instances parameter: {e}")

            # Check if we need to update the node structure
            has_anti_recursion_switch = False
            has_problematic_structure = False

            for node in node_group.nodes:
                if node.name == "Anti-Recursion Switch":
                    has_anti_recursion_switch = True

                    # Check for problematic old structure (Join Geometry node)
                    for other_node in node_group.nodes:
                        if other_node.name == "Anti-Recursion Join Geometry":
                            has_problematic_structure = True
                            break

                    break

            # If we have old problematic structure, update it
            if has_anti_recursion_switch and has_problematic_structure:
                print(f"[DEBUG] Updating problematic anti-recursion structure in {node_group.name}")
                if apply_anti_recursion_to_cloner:
                    if apply_anti_recursion_to_cloner(node_group):
                        updated_count += 1
                else:
                    print(f"[DEBUG] Anti-recursion function not available for {node_group.name}")
                    # Заглушка - просто удаляем проблемные узлы
                    try:
                        for node in list(node_group.nodes):
                            if node.name == "Anti-Recursion Join Geometry":
                                node_group.nodes.remove(node)
                                print(f"[DEBUG] Removed problematic node from {node_group.name}")
                                updated_count += 1
                                break
                    except Exception as e:
                        print(f"[ERROR] Failed to remove problematic node: {e}")

            # Update cloner with effectors if it has any
            if "linked_effectors" in node_group and node_group["linked_effectors"]:
                try:
                    print(f"[DEBUG] Updating effectors for {node_group.name}")
                    update_cloner_with_effectors(obj, modifier)
                except Exception as e:
                    print(f"[ERROR] Failed to update effectors for {node_group.name}: {e}")

            # ИСПРАВЛЕНИЕ ПРОБЛЕМЫ 3: Обновляем эффекторы в клонерах коллекций
            # при изменении состояния анти-рекурсии
            _update_collection_cloner_effectors_on_anti_recursion_change(
                obj, modifier, use_anti_recursion
            )

    # Force update the view
    context.view_layer.update()

    if updated_count > 0:
        print(f"[INFO] Updated {updated_count} cloners with improved anti-recursion system")


def _update_collection_cloner_effectors_on_anti_recursion_change(obj, modifier, use_anti_recursion):
    """
    Обновляет позицию эффекторов в клонерах коллекций при изменении состояния анти-рекурсии.

    ИСПРАВЛЕНИЕ ПРОБЛЕМЫ 3: При изменении анти-рекурсии эффекторы должны быть
    перемещены в правильное место в node tree.

    Args:
        obj: Объект с клонером
        modifier: Модификатор клонера
        use_anti_recursion: Новое состояние анти-рекурсии
    """
    try:
        # Проверяем, что это клонер коллекций
        if not modifier.node_group or "CollectionCloner" not in modifier.node_group.name:
            return

        node_group = modifier.node_group
        nodes = node_group.nodes
        links = node_group.links

        # Ищем узлы эффекторов в группе
        effector_nodes = []
        for node in nodes:
            if node.name.startswith("Effector_") and node.type == 'GROUP':
                effector_nodes.append(node)

        if not effector_nodes:
            return  # Нет эффекторов для обновления

        print(f"[DEBUG] Обновляем {len(effector_nodes)} эффекторов в клонере {modifier.name}")

        # Ищем узел анти-рекурсии
        anti_recursion_switch = None
        for node in nodes:
            if node.name == "Anti-Recursion Switch":
                anti_recursion_switch = node
                break

        if not anti_recursion_switch:
            return  # Нет анти-рекурсии для обновления

        # Для каждого эффектора переподключаем его в правильное место
        for effector_node in effector_nodes:
            try:
                # Импортируем функцию для безопасного создания связей
                from ..effector_management.connection_management import safe_link_new

                # Удаляем все существующие связи эффектора
                links_to_remove = []
                for link in links:
                    if link.from_node == effector_node or link.to_node == effector_node:
                        links_to_remove.append(link)

                for link in links_to_remove:
                    links.remove(link)

                # Переподключаем эффектор в зависимости от состояния анти-рекурсии
                if use_anti_recursion:
                    # При включенной анти-рекурсии: эффектор ПОСЛЕ переключения
                    # Ищем выход анти-рекурсии и его цель
                    target_node = None
                    target_socket = None

                    for link in links:
                        if link.from_node == anti_recursion_switch:
                            target_node = link.to_node
                            target_socket = link.to_socket
                            links.remove(link)
                            break

                    if target_node and target_socket:
                        # Подключаем: Anti-Recursion Switch -> эффектор -> цель
                        safe_link_new(links, anti_recursion_switch.outputs['Output'], effector_node.inputs['Geometry'])
                        safe_link_new(links, effector_node.outputs['Geometry'], target_socket)
                        print(f"[DEBUG] Эффектор {effector_node.name} перемещен ПОСЛЕ анти-рекурсии")
                else:
                    # При отключенной анти-рекурсии: эффектор ДО переключения (в ветку False)
                    # Ищем источник для входа False
                    source_node = None
                    source_socket = None

                    for link in links:
                        if (link.to_node == anti_recursion_switch and
                            link.to_socket == anti_recursion_switch.inputs['False']):
                            source_node = link.from_node
                            source_socket = link.from_socket
                            links.remove(link)
                            break

                    if source_node and source_socket:
                        # Подключаем: источник -> эффектор -> Anti-Recursion Switch (False)
                        safe_link_new(links, source_socket, effector_node.inputs['Geometry'])
                        safe_link_new(links, effector_node.outputs['Geometry'], anti_recursion_switch.inputs['False'])
                        print(f"[DEBUG] Эффектор {effector_node.name} перемещен ДО анти-рекурсии")

            except Exception as e:
                print(f"[ERROR] Не удалось обновить эффектор {effector_node.name}: {e}")

    except Exception as e:
        print(f"[ERROR] _update_collection_cloner_effectors_on_anti_recursion_change: {e}")


def update_anti_recursion_callback(self, context):
    """
    Callback function for the anti-recursion property.

    Args:
        self: The property owner (unused but required by Blender)
        context: Blender context
    """
    # Update all cloners when the anti-recursion setting changes
    update_anti_recursion_for_all_cloners(context)
    return None


def update_stacked_modifiers_callback(self, context):
    """
    Callback function for the stacked modifiers property.

    Args:
        self: The property owner (unused but required by Blender)
        context: Blender context
    """
    # Stacked modifiers can now work together with anti-recursion
    return None

