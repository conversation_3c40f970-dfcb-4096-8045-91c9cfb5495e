"""
Cloner drawing utilities for Advanced Cloners addon.

This module provides UI drawing functions for cloner interfaces,
including settings panels, controls, and parameter displays.
"""

# Import functionality from sub-modules
# UPDATED: Removed deprecated functions after migration to new parameter system
from .settings_panels import (
    draw_collection_cloner_settings
    # REMOVED: draw_grid_cloner_settings, draw_linear_cloner_settings,
    # draw_circle_cloner_settings, draw_spiral_cloner_settings
    # All replaced by unified draw_collection_cloner_settings with generate_ui_from_parameters
)



from .parameter_display import (
    display_cloner_parameter,
    display_parameter_group,
    display_socket_parameter,
    get_parameter_value,
    set_parameter_value
)

# Utility functions
import bpy


def force_select_object(context, obj_name):
    """
    Принудительно устанавливает объект активным и выделенным.
    Обновляет интерфейс после выбора.

    Args:
        context: Контекст Blender
        obj_name: Имя объекта для выбора

    Returns:
        bool: True если объект выбран успешно
    """
    if not obj_name or obj_name not in bpy.data.objects:
        print(f"[SELECT] Ошибка: объект '{obj_name}' не найден")
        return False

    try:
        # Очищаем текущее выделение
        bpy.ops.object.select_all(action='DESELECT')

        # Выбираем нужный объект
        obj = bpy.data.objects[obj_name]
        obj.select_set(True)
        context.view_layer.objects.active = obj

        # Обновляем view_layer
        context.view_layer.update()

        # Обновляем интерфейс
        for window in context.window_manager.windows:
            for area in window.screen.areas:
                area.tag_redraw()

        print(f"[SELECT] Объект '{obj_name}' успешно выбран и установлен активным")
        return True
    except Exception as e:
        print(f"[SELECT] Ошибка при выборе объекта '{obj_name}': {e}")
        import traceback
        traceback.print_exc()
        return False


def get_cloner_type_from_modifier(modifier):
    """
    Определяет тип клонера из модификатора.

    Args:
        modifier: Модификатор клонера

    Returns:
        str: Тип клонера (GRID, LINEAR, CIRCLE)
    """
    if not modifier or not modifier.node_group:
        return "UNKNOWN"

    node_group_name = modifier.node_group.name

    if "Grid" in node_group_name:
        return "GRID"
    elif "Linear" in node_group_name:
        return "LINEAR"
    elif "Circle" in node_group_name:
        return "CIRCLE"

    # Проверяем по метаданным
    cloner_type = modifier.node_group.get("cloner_type", "")
    if cloner_type:
        return cloner_type.upper()

    return "UNKNOWN"


def is_stacked_cloner_modifier(modifier):
    """
    Проверяет, является ли модификатор стековым клонером.

    Args:
        modifier: Модификатор для проверки

    Returns:
        bool: True если это стековый клонер
    """
    if not modifier:
        return False

    # Проверяем флаг стекового клонера
    if modifier.get("is_stacked_cloner", False):
        return True

    if modifier.node_group and modifier.node_group.get("is_stacked_cloner", False):
        return True

    return False


def register():
    """Register cloner drawing components"""
    pass


def unregister():
    """Unregister cloner drawing components"""
    pass
