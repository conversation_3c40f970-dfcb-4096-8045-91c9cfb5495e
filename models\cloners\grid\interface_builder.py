"""
Grid Cloner Interface Builder

This module contains the main interface group creation for the Grid Cloner.
It handles the public interface, global transforms, and integration with the logic group.

UPDATED: Now uses the new unified parameter system for automatic interface creation.
"""

import bpy
from ..base import ClonerBase
from .logic_builder import create_logic_group


def create_main_group(logic_group, name_suffix=""):
    """
    Create an advanced 3D grid cloner node group with centering and 2D/3D switch.

    UPDATED: Uses new parameter system for automatic interface creation.
    """
    # Create new node group for the main interface
    node_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=f"GridCloner3D_Advanced{name_suffix}")

    # NEW: Use automatic interface creation from parameter definitions
    from ....core.parameters import get_component_parameters, build_interface_from_parameters

    grid_params = get_component_parameters('CLONER', 'GRID')
    if grid_params:
        # Automatically create interface from parameter definitions
        success = build_interface_from_parameters(node_group, grid_params)
        if success:
            print(f"✅ Grid Cloner interface created automatically from parameter definitions")
        else:
            print(f"❌ Failed to create interface automatically - manual fallback removed")
            print(f"⚠️ Manual interface creation was removed after migration to new parameter system")
            print(f"💡 Check GRID_CLONER_PARAMETERS definition in config/parameters/cloners/grid_cloner.py")
            return None
    else:
        print(f"❌ Grid Cloner parameters not found - manual fallback removed")
        print(f"⚠️ Manual interface creation was removed after migration to new parameter system")
        print(f"💡 Ensure GRID_CLONER_PARAMETERS is properly imported and defined")
        return None

    # --- Nodes ---
    nodes = node_group.nodes

    # Add group input and output
    group_input = nodes.new('NodeGroupInput')
    group_output = nodes.new('NodeGroupOutput')

    # Create links
    links = node_group.links

    # Setup instance input processing
    print(f"[GRID DEBUG] Setting up instance input processing...")
    object_info, instances_output = _setup_instance_input_processing(
        nodes, links, group_input
    )
    print(f"[GRID DEBUG] Instance input processing complete. ObjectInfo: {object_info.name}")

    # Create cloner logic node
    cloner_logic_node = nodes.new('GeometryNodeGroup')
    cloner_logic_node.node_tree = logic_group
    cloner_logic_node.name = "Grid Cloner Logic"
    cloner_logic_node.location = (-400, 0)

    # Setup anti-recursion processing
    switch_realize = _setup_anti_recursion_processing(
        nodes, links, group_input, instances_output
    )

    # Connect instance source to cloner logic
    print(f"[GRID DEBUG] Connecting instance source to cloner logic...")
    print(f"[GRID DEBUG] Switch realize outputs: {[out.name for out in switch_realize.outputs]}")
    print(f"[GRID DEBUG] Cloner logic inputs: {[inp.name for inp in cloner_logic_node.inputs]}")
    links.new(switch_realize.outputs[0], cloner_logic_node.inputs['Instance Source'])
    print("[GRID DEBUG] Connected instance source with realize instances option to cloner logic")

    # Connect all main inputs to the logic subgroup
    _connect_logic_inputs(links, group_input, cloner_logic_node)

    # Setup global transform
    global_transform = _setup_global_transform(nodes, links, group_input, cloner_logic_node)

    # Setup final output processing
    _setup_final_output_processing(nodes, links, group_input, global_transform, group_output)

    return node_group


# ===== MANUAL INTERFACE FUNCTION REMOVED =====
#
# _create_manual_interface() - УДАЛЕНА (80 строк дублированного кода)
#
# Функция заменена автоматическим созданием интерфейса через новую систему параметров:
# ✅ core/parameters/interface_builder.py - build_interface_from_parameters()
# ✅ config/parameters/cloners/grid_cloner.py - GRID_CLONER_PARAMETERS
#
# Новая система обеспечивает:
# ✅ Автоматическое создание всех сокетов из определений параметров
# ✅ Единый источник истины для параметров
# ✅ Легкость добавления новых параметров
# ✅ Отсутствие дублированного кода
#
# Для создания интерфейса используйте:
# from core.parameters import build_interface_from_parameters
# from config.parameters.cloners.grid_cloner import GRID_CLONER_PARAMETERS
# success = build_interface_from_parameters(node_group, GRID_CLONER_PARAMETERS)


def _setup_instance_input_processing(nodes, links, group_input):
    """Setup instance input processing nodes"""
    print(f"[GRID DEBUG] _setup_instance_input_processing called")
    print(f"[GRID DEBUG] Group input outputs: {[out.name for out in group_input.outputs]}")

    # Используем улучшенный метод setup_instance_input_nodes из базового класса
    object_info, instances_output = ClonerBase.setup_instance_input_nodes(
        nodes,
        links,
        group_input,
        realize_instances=False  # По умолчанию выключено, будет управляться через интерфейс
    )
    object_info.name = "Object Info"
    object_info.location = (-800, 0)

    print(f"[GRID DEBUG] ObjectInfo created: {object_info.name}")
    print(f"[GRID DEBUG] ObjectInfo inputs: {[inp.name for inp in object_info.inputs]}")
    print(f"[GRID DEBUG] ObjectInfo outputs: {[out.name for out in object_info.outputs]}")

    return object_info, instances_output


def _setup_anti_recursion_processing(nodes, links, group_input, instances_output):
    """Setup anti-recursion processing with realize instances"""
    # Создаем узел Realize Instances, который будет включаться/выключаться
    realize_node = nodes.new('GeometryNodeRealizeInstances')
    realize_node.name = "Realize Instances (Anti-Recursion)"
    realize_node.location = (-600, 0)

    # Создаем узел Switch для переключения между обычным и "реализованным" потоком
    switch_realize = nodes.new('GeometryNodeSwitch')
    switch_realize.input_type = 'GEOMETRY'
    switch_realize.name = "Switch Realize Mode"
    switch_realize.location = (-500, 0)

    # Соединяем выход инстансов с узлом Realize Instances
    links.new(instances_output, realize_node.inputs['Geometry'])

    # Настраиваем переключатель для выбора между обычными инстансами и "реализованными"
    links.new(group_input.outputs['Realize Instances'], switch_realize.inputs['Switch'])
    links.new(instances_output, switch_realize.inputs[False])  # Обычные инстансы
    links.new(realize_node.outputs['Geometry'], switch_realize.inputs[True])  # "Реализованные" инстансы

    return switch_realize


def _connect_logic_inputs(links, group_input, cloner_logic_node):
    """Connect all inputs from main group to logic subgroup"""
    # Connect the main inputs to the logic subgroup
    links.new(group_input.outputs['Count X'], cloner_logic_node.inputs['Count X'])
    links.new(group_input.outputs['Count Y'], cloner_logic_node.inputs['Count Y'])
    links.new(group_input.outputs['Count Z'], cloner_logic_node.inputs['Count Z'])
    links.new(group_input.outputs['Spacing'], cloner_logic_node.inputs['Spacing'])
    links.new(group_input.outputs['Instance Scale'], cloner_logic_node.inputs['Instance Scale'])
    links.new(group_input.outputs['Instance Rotation'], cloner_logic_node.inputs['Instance Rotation'])
    links.new(group_input.outputs['Random Position'], cloner_logic_node.inputs['Random Position'])
    links.new(group_input.outputs['Random Rotation'], cloner_logic_node.inputs['Random Rotation'])
    links.new(group_input.outputs['Random Scale'], cloner_logic_node.inputs['Random Scale'])
    links.new(group_input.outputs['Random Seed'], cloner_logic_node.inputs['Random Seed'])
    links.new(group_input.outputs['Pick Random Instance'], cloner_logic_node.inputs['Pick Random Instance'])
    links.new(group_input.outputs['Center Grid'], cloner_logic_node.inputs['Center Grid'])

    # Дополнительное подключение параметра Realize Instances к логике клонера
    # Это необходимо для корректной работы анти-рекурсии в грид-клонере
    if 'Realize Instances' in cloner_logic_node.inputs:
        links.new(group_input.outputs['Realize Instances'], cloner_logic_node.inputs['Realize Instances'])
        print("Connected Realize Instances parameter to cloner logic node")


def _setup_global_transform(nodes, links, group_input, cloner_logic_node):
    """Setup global transform node and connections"""
    # Global transform
    global_transform = nodes.new('GeometryNodeTransform')
    global_transform.location = (0, 0)

    # Apply Global Transform - connect cloner logic directly to global transform
    links.new(cloner_logic_node.outputs['Geometry'], global_transform.inputs['Geometry'])
    links.new(group_input.outputs['Global Position'], global_transform.inputs['Translation'])
    links.new(group_input.outputs['Global Rotation'], global_transform.inputs['Rotation'])

    return global_transform


def _setup_final_output_processing(nodes, links, group_input, global_transform, group_output):
    """Setup final output processing with optional realize instances"""
    # Создаем еще один узел Realize Instances для финального выхода
    # Это поможет предотвратить проблемы с глубиной рекурсии на выходе клонера
    final_realize = nodes.new('GeometryNodeRealizeInstances')
    final_realize.name = "Final Realize Instances"
    final_realize.location = (100, 0)

    # Создаем узел Switch для финального выхода
    final_switch = nodes.new('GeometryNodeSwitch')
    final_switch.input_type = 'GEOMETRY'
    final_switch.name = "Final Realize Switch"
    final_switch.location = (200, 0)

    # Соединяем глобальный трансформ с финальным Realize Instances
    links.new(global_transform.outputs['Geometry'], final_realize.inputs['Geometry'])

    # Настраиваем финальный переключатель
    links.new(group_input.outputs['Realize Instances'], final_switch.inputs['Switch'])
    links.new(global_transform.outputs['Geometry'], final_switch.inputs[False])  # Обычный выход
    links.new(final_realize.outputs['Geometry'], final_switch.inputs[True])  # "Реализованный" выход

    # Connect to output
    links.new(final_switch.outputs[0], group_output.inputs['Geometry'])
