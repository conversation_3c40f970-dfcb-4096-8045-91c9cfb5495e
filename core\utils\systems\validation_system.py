"""
Система валидации для Advanced Cloners addon.
"""

import bpy
from typing import Optional, List, Dict, Any
from .logging_system import logger

class ValidationError(Exception):
    """Исключение для ошибок валидации"""
    pass

class ClonerValidator:
    """Валидатор для компонентов клонера"""
    
    @staticmethod
    def validate_object(obj: bpy.types.Object) -> bool:
        """Валидация объекта Blender"""
        if not obj:
            raise ValidationError("Объект не может быть None")
        
        if not hasattr(obj, 'type'):
            raise ValidationError("Объект должен иметь атрибут type")
        
        return True
    
    @staticmethod
    def validate_modifier(modifier: bpy.types.Modifier) -> bool:
        """Валидация модификатора"""
        if not modifier:
            raise ValidationError("Модификатор не может быть None")
        
        if modifier.type != 'NODES':
            raise ValidationError("Модификатор должен быть типа NODES")
        
        if not modifier.node_group:
            raise ValidationError("Модификатор должен иметь node_group")
        
        return True
    
    @staticmethod
    def validate_node_group(node_group: bpy.types.NodeGroup) -> bool:
        """Валидация группы узлов"""
        if not node_group:
            raise ValidationError("Группа узлов не может быть None")
        
        if node_group.type != 'GeometryNodeTree':
            raise ValidationError("Группа узлов должна быть типа GeometryNodeTree")
        
        return True
    
    @staticmethod
    def validate_cloner_parameters(params: Dict[str, Any]) -> bool:
        """Валидация параметров клонера"""
        required_params = ['Count', 'Spacing']
        
        for param in required_params:
            if param not in params:
                raise ValidationError(f"Отсутствует обязательный параметр: {param}")
        
        return True

class PerformanceMonitor:
    """Монитор производительности"""
    
    def __init__(self):
        self.operation_times = {}
    
    def start_operation(self, operation_name: str):
        """Начать отслеживание операции"""
        import time
        self.operation_times[operation_name] = time.time()
    
    def end_operation(self, operation_name: str):
        """Завершить отслеживание операции"""
        import time
        if operation_name in self.operation_times:
            duration = time.time() - self.operation_times[operation_name]
            logger.debug(f"Операция '{operation_name}' заняла {duration:.3f} секунд")
            del self.operation_times[operation_name]
            return duration
        return None

# Глобальный экземпляр монитора
performance_monitor = PerformanceMonitor()
