"""
Модуль интерфейса создания клонеров.

Содержит функции для отображения UI элементов создания новых клонеров,
включая выбор типа источника и настройки стековых модификаторов.
"""

import bpy
from ...common.ui_constants import (
    UI_SCALE_Y_LARGE, UI_SCALE_Y_XLARGE,
    UI_STACKED_LABEL_TEXT, UI_STACKED_CHECKBOX_SCALE_Y,
    UI_STACK_PADDING, UI_STACK_RIGHT_PADDING, UI_STACK_ALIGNMENT,
    ICON_CLONER,
    ICON_GRID_CLONER, ICON_LINEAR_CLONER, ICON_CIRCLE_CLONER
)
from ....core.utils.optimization import fast_get_cloner_types


def draw_creation_interface(context, layout):
    """
    Отображает элементы интерфейса для создания новых клонеров.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
    """
    # Object/Collection selection as radio buttons
    source_row = layout.row(align=True)
    source_row.scale_y = UI_SCALE_Y_LARGE
    source_row.label(text="Clone Source:")
    source_row.prop(context.scene, "source_type_for_cloner", expand=True)

    # Collection selector (only visible when Collection is selected)
    if context.scene.source_type_for_cloner == 'COLLECTION':
        draw_collection_source_options(context, layout)
    else:
        draw_object_source_options(context, layout)

    # Cloner type selection with prominent buttons
    layout.separator()

    # Label for cloner types
    cloner_label_row = layout.row()
    cloner_label_row.label(text="Cloner Type:", icon=ICON_CLONER)

    # Big buttons for cloner types
    draw_cloner_type_buttons(context, layout)




def draw_collection_source_options(context, layout):
    """
    Отображает опции для клонирования коллекций.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
    """
    # Collection selector
    coll_row = layout.row(align=True)
    coll_row.scale_y = UI_SCALE_Y_LARGE
    coll_row.label(text="Collection:")
    coll_row.prop_search(context.scene, "collection_to_clone", bpy.data, "collections", text="")

    # Добавляем чекбокс Anti-Recursion для коллекций
    anti_recursion_row = layout.row(align=True)
    anti_recursion_row.scale_y = UI_STACKED_CHECKBOX_SCALE_Y

    # Добавляем пустой элемент для смещения группы вправо (аналог CSS margin-left: auto)
    ar_spacer = anti_recursion_row.column()
    ar_spacer.alignment = 'EXPAND'
    ar_spacer.scale_x = UI_STACK_ALIGNMENT
    ar_spacer.label(text="")

    # Создаем подгруппу для текста и чекбокса с компактным выравниванием
    ar_group = anti_recursion_row.row(align=True)
    ar_group.alignment = 'RIGHT'

    # Добавляем текст с отступом справа (как CSS padding-right)
    ar_group.label(text="Anti-Recursion")

    # Добавляем чекбокс с прижатием к правому краю
    # Чекбокс всегда активен, независимо от состояния стековых модификаторов
    ar_checkbox = ar_group.row()
    ar_checkbox.enabled = True
    ar_checkbox.prop(context.scene, "use_anti_recursion", text="")

    # Добавляем небольшой отступ справа (как CSS padding-right)
    ar_right_padding = anti_recursion_row.column()
    ar_right_padding.scale_x = UI_STACK_RIGHT_PADDING / 100
    ar_right_padding.label(text="")


def draw_object_source_options(context, layout):
    """
    Отображает опции для клонирования объектов.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
    """
    # Создаем свойство в сцене, если его еще нет
    if not hasattr(context.scene, "use_stacked_modifiers"):
        bpy.types.Scene.use_stacked_modifiers = bpy.props.BoolProperty(
            default=False,
            name="Use Stacked Modifiers",
            description="Create all cloners as modifiers on a single object instead of creating a chain of objects. This allows you to reorder cloners by moving modifiers up/down."
        )

    # Создаем чистую современную компоновку в стиле CSS для чекбокса Stack
    stack_row = layout.row(align=True)
    stack_row.scale_y = UI_STACKED_CHECKBOX_SCALE_Y

    # Добавляем пустой элемент для смещения группы вправо (аналог CSS margin-left: auto)
    spacer = stack_row.column()
    spacer.alignment = 'EXPAND'
    spacer.scale_x = UI_STACK_ALIGNMENT
    spacer.label(text="")

    # Создаем подгруппу для текста и чекбокса с компактным выравниванием
    stack_group = stack_row.row(align=True)
    stack_group.alignment = 'RIGHT'

    # Добавляем текст с отступом справа (как CSS padding-right)
    stack_group.label(text=UI_STACKED_LABEL_TEXT)

    # Добавляем чекбокс с прижатием к правому краю
    stack_group.prop(context.scene, "use_stacked_modifiers", text="")

    # Добавляем небольшой отступ справа (как CSS padding-right)
    right_padding = stack_row.column()
    right_padding.scale_x = UI_STACK_RIGHT_PADDING / 100
    right_padding.label(text="")

    # Добавляем чекбокс Anti-Recursion под Stacking Cloners
    anti_recursion_row = layout.row(align=True)
    anti_recursion_row.scale_y = UI_STACKED_CHECKBOX_SCALE_Y

    # Добавляем пустой элемент для смещения группы вправо (аналог CSS margin-left: auto)
    ar_spacer = anti_recursion_row.column()
    ar_spacer.alignment = 'EXPAND'
    ar_spacer.scale_x = UI_STACK_ALIGNMENT
    ar_spacer.label(text="")

    # Создаем подгруппу для текста и чекбокса с компактным выравниванием
    ar_group = anti_recursion_row.row(align=True)
    ar_group.alignment = 'RIGHT'

    # Добавляем текст с отступом справа (как CSS padding-right)
    ar_group.label(text="Anti-Recursion")

    # Добавляем чекбокс с прижатием к правому краю
    # Чекбокс всегда активен, независимо от состояния стековых модификаторов
    ar_checkbox = ar_group.row()
    ar_checkbox.enabled = True
    ar_checkbox.prop(context.scene, "use_anti_recursion", text="")

    # Добавляем небольшой отступ справа (как CSS padding-right)
    ar_right_padding = anti_recursion_row.column()
    ar_right_padding.scale_x = UI_STACK_RIGHT_PADDING / 100
    ar_right_padding.label(text="")


def draw_cloner_type_buttons(context, layout):
    """
    Отображает кнопки для выбора типа клонера.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
    """
    # Получаем только базовые типы клонеров (без COLLECTION_ и STACKED_ префиксов)
    all_cloner_types = fast_get_cloner_types()
    cloner_types = [
        (cloner_id, display_name, description, icon)
        for cloner_id, display_name, description, icon in all_cloner_types
        if not cloner_id.startswith(('COLLECTION_', 'STACKED_'))
    ]

    # Определяем количество колонок на основе количества клонеров
    num_columns = min(len(cloner_types), 3)  # Максимум 3 колонки

    # Big buttons for cloner types
    type_grid = layout.grid_flow(columns=num_columns, even_columns=True, even_rows=True)
    type_grid.scale_y = UI_SCALE_Y_XLARGE  # Make buttons larger and more prominent

    # Маппинг иконок для обратной совместимости
    icon_mapping = {
        "GRID": ICON_GRID_CLONER,
        "LINEAR": ICON_LINEAR_CLONER,
        "CIRCLE": ICON_CIRCLE_CLONER,
        "SPIRAL": "FORCE_VORTEX"  # Новый клонер автоматически получает иконку
    }

    for cloner_id, display_name, description, icon in cloner_types:
        # Используем иконку из метаданных или fallback
        button_icon = icon_mapping.get(cloner_id, icon)

        # Create button with depress=True to make it more obvious it's a button
        button = type_grid.operator(
            "object.create_cloner",
            text=display_name.replace(" Cloner", ""),  # Убираем "Cloner" для краткости
            icon=button_icon,
            depress=False
        )
        button.cloner_type = cloner_id
        button.source_type = context.scene.source_type_for_cloner

        # If collection is selected, pass the collection name
        if context.scene.source_type_for_cloner == 'COLLECTION' and context.scene.collection_to_clone:
            button.target_collection = context.scene.collection_to_clone

        button.use_custom_group = True  # Always use custom groups

        # Важно: устанавливаем параметр stacked_modifiers здесь
        if context.scene.source_type_for_cloner == 'OBJECT':
            button.use_stacked_modifiers = context.scene.use_stacked_modifiers
