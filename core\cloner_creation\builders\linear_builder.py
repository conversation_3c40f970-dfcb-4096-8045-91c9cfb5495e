"""
Построитель узлов для Linear клонера.

Отвечает за создание специфичной логики Linear клонера:
- Создание линейных узлов
- Логика линии
- Специфичные параметры Linear клонера
"""

from .base_builder import BaseNodeBuilder
from ....core.cloner_creation.transforms import (
    apply_global_transforms,
    apply_instance_and_random_transforms
)

class LinearNodeBuilder(BaseNodeBuilder):
    """
    Построитель узлов для Linear клонера.

    Наследует от BaseNodeBuilder и добавляет специфичную логику для Linear клонера.
    """

    def __init__(self, node_group, orig_obj):
        """
        Инициализация построителя Linear узлов.

        Args:
            node_group: Группа узлов для настройки
            orig_obj: Исходный объект для клонирования
        """
        super().__init__(node_group, orig_obj, "LINEAR")

    def setup_linear_interface(self):
        """
        Настраивает интерфейс специфичный для Linear клонера.

        UPDATED: Теперь использует новую систему параметров с fallback на ручное создание.

        Returns:
            bool: True если настройка прошла успешно
        """
        try:
            # NEW: Try to use new parameter system first
            from ....core.parameters import get_component_parameters, build_interface_from_parameters

            linear_params = get_component_parameters('CLONER', 'LINEAR')
            if linear_params:
                # Automatically create interface from parameter definitions
                success = build_interface_from_parameters(self.node_group, linear_params)
                if success:
                    print(f"✅ Linear Cloner interface created automatically from parameter definitions (LinearNodeBuilder)")
                    return True
                else:
                    print(f"❌ Failed to create interface automatically, falling back to manual creation (LinearNodeBuilder)")
            else:
                print(f"❌ Linear Cloner parameters not found, using manual interface creation (LinearNodeBuilder)")

            # LEGACY: Fallback to manual interface creation
            # Добавляем основные входные сокеты для Linear клонера
            count_socket = self.node_group.interface.new_socket("Count", in_out='INPUT', socket_type='NodeSocketInt')
            count_socket.default_value = 5

            offset_socket = self.node_group.interface.new_socket("Offset", in_out='INPUT', socket_type='NodeSocketVector')
            offset_socket.default_value = (3.0, 0.0, 0.0)

            # Add Use Effector parameter for effector integration
            use_effector_socket = self.node_group.interface.new_socket("Use Effector", in_out='INPUT', socket_type='NodeSocketBool')
            use_effector_socket.default_value = True

            return True
        except Exception as e:
            print(f"Ошибка при настройке Linear интерфейса: {e}")
            return False

    def build_linear_nodes(self):
        """
        Строит узлы специфичные для Linear клонера.

        Returns:
            bool: True если построение прошло успешно
        """
        try:
            # Создаем узлы для линейного клонера
            line_node = self.nodes.new('GeometryNodeMeshLine')
            if hasattr(line_node, "mode"):
                line_node.mode = 'OFFSET'
            if hasattr(line_node, "count_mode"):
                line_node.count_mode = 'TOTAL'
            line_node.location = (-400, 200)

            # Соединяем параметры Count и Offset
            self.links.new(self.group_in.outputs['Count'], line_node.inputs['Count'])

            # Используем универсальный подход для подключения вектора смещения
            offset_input = None
            for name in ['Offset', 'End Point', 'Length']:
                if name in line_node.inputs:
                    offset_input = name
                    break

            if offset_input:
                self.links.new(self.group_in.outputs['Offset'], line_node.inputs[offset_input])

            # Преобразуем меш в точки
            mesh_to_points = self.nodes.new('GeometryNodeMeshToPoints')
            mesh_to_points.location = (-200, 200)
            self.links.new(line_node.outputs['Mesh'], mesh_to_points.inputs['Mesh'])

            # Инстансирование на точках (БЕЗ базовых трансформаций)
            instance_on_points = self.nodes.new('GeometryNodeInstanceOnPoints')
            instance_on_points.location = (0, 200)
            self.links.new(mesh_to_points.outputs['Points'], instance_on_points.inputs['Points'])
            self.links.new(self.object_info.outputs[self.output_socket], instance_on_points.inputs['Instance'])
            # НЕ подключаем Instance Rotation и Instance Scale - они будут применены через наши функции

            # Применяем ВСЕ трансформации через унифицированную функцию
            final_instances = self.apply_all_transforms(instance_on_points)

            # Сохраняем ссылку на финальный узел для анти-рекурсии
            self.final_node = final_instances
            self.final_socket = 'Instances'

            return True
        except Exception as e:
            print(f"Ошибка при создании Linear узлов: {e}")
            return False

    def apply_all_transforms(self, instance_node):
        """
        Применяет ВСЕ трансформации к инстансам через унифицированную функцию.

        Включает:
        1. Базовые трансформации инстансов (Instance Rotation, Instance Scale)
        2. Случайные трансформации (Random Position, Random Rotation, Random Scale)
        3. НЕ включает глобальные трансформации (они применяются отдельно)

        Args:
            instance_node: Узел инстансов

        Returns:
            node: Финальный узел с примененными трансформациями
        """
        try:
            # Применяем базовые и случайные трансформации (БЕЗ глобальных)
            return apply_instance_and_random_transforms(
                self.nodes,
                self.links,
                instance_node,
                self.group_in,
                "LINEAR"
            )
        except Exception as e:
            print(f"Ошибка при применении трансформаций: {e}")
            return instance_node

    def add_global_transform(self):
        """
        Добавляет глобальные трансформации.

        Использует новую модульную функцию глобальных трансформаций.

        Returns:
            bool: True если добавление прошло успешно
        """
        try:
            # Применяем глобальные трансформации используя новую модульную функцию
            transform = apply_global_transforms(
                self.nodes,
                self.links,
                self.final_node,
                self.final_socket,
                self.group_in
            )

            if transform:
                # Обновляем финальный узел
                self.final_node = transform
                self.final_socket = 'Geometry'
                return True
            else:
                return False
        except Exception as e:
            print(f"Ошибка при добавлении глобальных трансформаций: {e}")
            return False

    def build(self):
        """
        Основной метод построения Linear узлов.

        Переопределяет базовый метод для добавления Linear-специфичной логики.

        Returns:
            bool: True если построение прошло успешно
        """
        try:
            # Вызываем базовую последовательность
            if not super().build():
                return False

            # Добавляем Linear-специфичные сокеты
            if not self.setup_linear_interface():
                return False

            # Создаем Linear-специфичные узлы
            if not self.build_linear_nodes():
                return False

            # Добавляем глобальные трансформации
            if not self.add_global_transform():
                return False

            # Настраиваем анти-рекурсию
            if not self.setup_anti_recursion(self.final_node, self.final_socket):
                return False

            return True
        except Exception as e:
            print(f"Ошибка при построении Linear узлов: {e}")
            return False
