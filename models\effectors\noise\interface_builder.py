"""
Noise Effector Interface Builder

This module contains the main interface group creation for the Noise Effector.
It handles the public interface and integration with the logic group.

Uses the unified parameter system for automatic interface creation.
"""

import bpy
from ..base import EffectorBase
from .logic_builder import create_logic_group

def create_main_group(logic_group, name_suffix=""):
    """
    Create a main group for the Noise Effector that uses the logic group.
    
    This function creates the node group that provides the public interface
    and connects it to the logic group.
    
    Args:
        logic_group: The logic group to use
        name_suffix: Optional suffix for the node group name
        
    Returns:
        The created node group
    """
    # Создаем новую группу узлов
    main_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=f"NoiseEffector{name_suffix}")

    # --- Сначала создаем основные сокеты вручную ---
    # Выходы
    main_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Входы
    main_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    main_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
    main_group.interface.new_socket(name="Strength", in_out='INPUT', socket_type='NodeSocketFloat')

    # Параметры трансформации
    main_group.interface.new_socket(name="Position", in_out='INPUT', socket_type='NodeSocketVector')
    main_group.interface.new_socket(name="Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    main_group.interface.new_socket(name="Scale", in_out='INPUT', socket_type='NodeSocketVector')

    # Специфичные для NoiseEffector
    # Базовые параметры
    uniform_scale_input = main_group.interface.new_socket(name="Uniform Scale", in_out='INPUT', socket_type='NodeSocketBool')
    uniform_scale_input.default_value = True

    symmetric_translation_input = main_group.interface.new_socket(name="Symmetric Translation", in_out='INPUT', socket_type='NodeSocketBool')
    symmetric_translation_input.default_value = False

    symmetric_rotation_input = main_group.interface.new_socket(name="Symmetric Rotation", in_out='INPUT', socket_type='NodeSocketBool')
    symmetric_rotation_input.default_value = False

    # Параметры шума
    noise_scale_input = main_group.interface.new_socket(name="Noise Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_scale_input.default_value = 0.5
    
    noise_detail_input = main_group.interface.new_socket(name="Noise Detail", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_detail_input.default_value = 2.0
    
    noise_roughness_input = main_group.interface.new_socket(name="Noise Roughness", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_roughness_input.default_value = 0.5
    
    noise_lacunarity_input = main_group.interface.new_socket(name="Noise Lacunarity", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_lacunarity_input.default_value = 2.0
    
    noise_distortion_input = main_group.interface.new_socket(name="Noise Distortion", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_distortion_input.default_value = 0.0

    # Параметры позиции шума
    noise_position_input = main_group.interface.new_socket(name="Noise Position", in_out='INPUT', socket_type='NodeSocketVector')
    noise_position_input.default_value = (0.0, 0.0, 0.0)
    
    noise_xyz_scale_input = main_group.interface.new_socket(name="Noise XYZ Scale", in_out='INPUT', socket_type='NodeSocketVector')
    noise_xyz_scale_input.default_value = (1.0, 1.0, 1.0)
    
    speed_input = main_group.interface.new_socket(name="Speed", in_out='INPUT', socket_type='NodeSocketFloat')
    speed_input.default_value = 0.0
    
    seed_input = main_group.interface.new_socket(name="Seed", in_out='INPUT', socket_type='NodeSocketInt')
    seed_input.default_value = 0
    seed_input.min_value = 0

    # --- Поддержка полей (опциональная) ---
    # Создаем сокеты для поддержки полей
    main_group.interface.new_socket(name="Use Field", in_out='INPUT', socket_type='NodeSocketBool')
    field_input = main_group.interface.new_socket(name="Field", in_out='INPUT', socket_type='NodeSocketFloat')

    # Используем автоматическую систему параметров только для получения значений по умолчанию
    from ....core.parameters import get_component_parameters
    from ....operations.helpers.parameter_setup.universal_params import setup_noise_effector_params

    noise_params = get_component_parameters('EFFECTOR', 'NOISE')
    if noise_params:
        print(f"✅ Noise Effector parameters found - will be used for UI generation")
    else:
        print(f"⚠️ Noise Effector parameters not found - using hardcoded UI generation")

    # --- Создание узлов ---
    nodes = main_group.nodes
    links = main_group.links

    group_input = nodes.new('NodeGroupInput')
    group_output = nodes.new('NodeGroupOutput')

    # Добавляем узел логической группы
    logic_node = nodes.new('GeometryNodeGroup')
    logic_node.node_tree = logic_group

    # Соединяем общие входы с логической группой
    links.new(group_input.outputs['Geometry'], logic_node.inputs['Geometry'])
    links.new(group_input.outputs['Enable'], logic_node.inputs['Enable'])
    links.new(group_input.outputs['Strength'], logic_node.inputs['Strength'])
    links.new(group_input.outputs['Position'], logic_node.inputs['Position'])
    links.new(group_input.outputs['Rotation'], logic_node.inputs['Rotation'])
    links.new(group_input.outputs['Scale'], logic_node.inputs['Scale'])

    # Соединяем специфичные входы для NoiseEffector
    links.new(group_input.outputs['Uniform Scale'], logic_node.inputs['Uniform Scale'])
    links.new(group_input.outputs['Symmetric Translation'], logic_node.inputs['Symmetric Translation'])
    links.new(group_input.outputs['Symmetric Rotation'], logic_node.inputs['Symmetric Rotation'])
    
    # Параметры шума
    links.new(group_input.outputs['Noise Scale'], logic_node.inputs['Noise Scale'])
    links.new(group_input.outputs['Noise Detail'], logic_node.inputs['Noise Detail'])
    links.new(group_input.outputs['Noise Roughness'], logic_node.inputs['Noise Roughness'])
    links.new(group_input.outputs['Noise Lacunarity'], logic_node.inputs['Noise Lacunarity'])
    links.new(group_input.outputs['Noise Distortion'], logic_node.inputs['Noise Distortion'])
    
    # Параметры позиции шума
    links.new(group_input.outputs['Noise Position'], logic_node.inputs['Noise Position'])
    links.new(group_input.outputs['Noise XYZ Scale'], logic_node.inputs['Noise XYZ Scale'])
    links.new(group_input.outputs['Speed'], logic_node.inputs['Speed'])
    links.new(group_input.outputs['Seed'], logic_node.inputs['Seed'])

    # --- Настройка модификации через поле (если поддерживается) ---
    # Получаем фактор влияния поля
    field_factor = EffectorBase.setup_nodes_for_field_control(
        nodes, links, group_input,
        'Enable', 'Field'
    )

    # Соединяем выход с группой логики
    links.new(logic_node.outputs['Geometry'], group_output.inputs['Geometry'])

    return main_group