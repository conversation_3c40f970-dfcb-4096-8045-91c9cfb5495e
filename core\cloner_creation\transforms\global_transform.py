"""
Унифицированные глобальные трансформации для всех типов клонеров.

Содержит логику глобальных трансформаций, которая используется
всеми типами клонеров (object, collection, stacked).

Все типы клонеров используют одинаковую логику глобальных трансформаций.
"""

import bpy
from typing import Optional

def apply_global_transforms(nodes, links, input_node, input_socket: str, group_in,
                          mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет глобальные трансформации к узлу.

    Унифицирует логику из object, collection и stacked модулей.
    Все типы клонеров используют одинаковую логику Transform узла.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        input_node: Входной узел для трансформации
        input_socket: Имя входного сокета ('Instances', 'Geometry')
        group_in: Входной узел группы
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел Transform с примененными глобальными трансформациями или None
    """
    try:
        # Создаем узел глобальных трансформаций
        # Логика одинакова для всех типов клонеров
        transform = nodes.new('GeometryNodeTransform')

        # Устанавливаем позицию в зависимости от режима
        if mode == "object":
            # Из object_cloner_modules/transforms/global_transforms.py (строка 31)
            transform.location = (400, 0)
        elif mode == "stacked":
            # Из stacked_cloner_modules/transforms/stacked_instance.py (строка 41)
            transform.location = (input_node.location.x + 100, input_node.location.y)
            transform.name = "Global Transform"
        elif mode == "collection":
            # Из collection_cloner.py (строка 693)
            transform.location = (800, 100)
        else:
            # Дефолтная позиция
            transform.location = (400, 0)

        # Соединяем входной узел с трансформацией
        # Логика одинакова для всех типов клонеров
        links.new(input_node.outputs[input_socket], transform.inputs['Geometry'])

        # Подключаем глобальные параметры
        # Логика одинакова для всех типов клонеров
        links.new(group_in.outputs['Global Position'], transform.inputs['Translation'])
        links.new(group_in.outputs['Global Rotation'], transform.inputs['Rotation'])

        return transform

    except Exception as e:
        print(f"Ошибка при применении глобальных трансформаций ({mode}): {e}")
        return input_node

def create_global_transform_node(nodes, mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Создает узел глобальных трансформаций без подключений.

    Вспомогательная функция для создания Transform узла с правильным расположением.

    Args:
        nodes: Коллекция узлов
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел Transform или None
    """
    try:
        transform = nodes.new('GeometryNodeTransform')

        # Устанавливаем позицию в зависимости от режима
        if mode == "object":
            transform.location = (400, 0)
        elif mode == "stacked":
            transform.name = "Global Transform"
            # Позиция будет установлена относительно входного узла
        elif mode == "collection":
            transform.location = (800, 100)
        else:
            transform.location = (400, 0)

        return transform

    except Exception as e:
        print(f"Ошибка при создании узла глобальных трансформаций ({mode}): {e}")
        return None

def connect_global_inputs(links, transform_node, group_in) -> bool:
    """
    Подключает глобальные входы к Transform узлу.

    Вспомогательная функция для подключения Global Position и Global Rotation.

    Args:
        links: Коллекция связей
        transform_node: Узел Transform
        group_in: Входной узел группы

    Returns:
        bool: True если подключение прошло успешно
    """
    try:
        # Подключаем глобальные параметры
        links.new(group_in.outputs['Global Position'], transform_node.inputs['Translation'])
        links.new(group_in.outputs['Global Rotation'], transform_node.inputs['Rotation'])
        return True

    except Exception as e:
        print(f"Ошибка при подключении глобальных входов: {e}")
        return False

def apply_global_transforms_to_geometry(nodes, links, geometry_node, group_in,
                                      mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет глобальные трансформации к геометрии.

    Упрощенная версия для случаев, когда входной сокет всегда 'Geometry'.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        geometry_node: Узел с геометрией
        group_in: Входной узел группы
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел Transform с примененными глобальными трансформациями или None
    """
    return apply_global_transforms(nodes, links, geometry_node, 'Geometry', group_in, mode)

def apply_global_transforms_to_instances(nodes, links, instances_node, group_in,
                                       mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет глобальные трансформации к инстансам.

    Упрощенная версия для случаев, когда входной сокет всегда 'Instances'.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instances_node: Узел с инстансами
        group_in: Входной узел группы
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел Transform с примененными глобальными трансформациями или None
    """
    return apply_global_transforms(nodes, links, instances_node, 'Instances', group_in, mode)
