"""
Random Effector module.

This module contains the random effector implementation,
which provides random transformations for instances.
"""

# Import the random effector class and functions
from .random_effector import (
    RandomEffector,
    randomeffector_node_group
)

# Public API
__all__ = [
    'RandomEffector',
    'randomeffector_node_group'
]


def register():
    """Register random effector components"""
    pass


def unregister():
    """Unregister random effector components"""
    pass
