"""
Модуль управления цепочками клонеров.

Содержит функции для управления цепочками и связями между клонерами:
- chain_utils: Основные функции управления цепочками
"""

# Реэкспорт всех функций для обратной совместимости
from .chain_utils import (
    delete_cloner,
    move_cloner_modifier,
    ClonerChainUpdateHandler,
    update_cloner_chain,
    select_previous_cloner_in_chain
)

# Публичный API
__all__ = [
    'delete_cloner',
    'move_cloner_modifier',
    'ClonerChainUpdateHandler',
    'update_cloner_chain',
    'select_previous_cloner_in_chain'
]

def register():
    """Register chain management components"""
    pass

def unregister():
    """Unregister chain management components"""
    pass
