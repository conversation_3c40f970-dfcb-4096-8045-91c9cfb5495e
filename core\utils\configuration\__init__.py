"""
Configuration utilities for the Advanced Cloners addon.

This module provides configuration management, global variables,
and type utilities for the cloner system.
"""

# Import all functionality from configuration modules
from .config_utils import *
from .globals import (
    _effector_last_parameters,
    _effector_handler_blocked,
    _effector_handler_call_count,
    _EFFECTOR_HANDLER_MAX_CALLS
)
from .type_utils import (
    handle_clone_type_switch,
    save_cloner_settings,
    load_cloner_settings
)

# Public API
__all__ = [
    # From config_utils
    'get_addon_preferences',
    'get_config_value',
    'set_config_value',

    # From globals
    '_effector_last_parameters',
    '_effector_handler_blocked',
    '_effector_handler_call_count',
    '_EFFECTOR_HANDLER_MAX_CALLS',

    # From type_utils
    'handle_clone_type_switch',
    'save_cloner_settings',
    'load_cloner_settings'
]

def register():
    """Register configuration components"""
    pass

def unregister():
    """Unregister configuration components"""
    pass
