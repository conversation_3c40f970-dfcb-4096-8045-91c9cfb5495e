"""
Collection management utilities for cloner duplicates.

This module handles creation and management of collections used to store
cloner duplicate objects.
"""

import bpy
from typing import Optional


def create_cloner_collection(base_name="cloner"):
    """
    Creates a collection for storing cloner duplicate objects.
    
    Args:
        base_name (str): Base name for the collection
        
    Returns:
        bpy.types.Collection: Created collection
    """
    # Create unique name for collection
    collection_name = base_name
    counter = 1
    while collection_name in bpy.data.collections:
        collection_name = f"{base_name}_{counter:03d}"
        counter += 1
    
    # Create new collection
    collection = bpy.data.collections.new(collection_name)
    
    # Add collection to scene
    try:
        bpy.context.scene.collection.children.link(collection)
    except Exception as e:
        print(f"Failed to add collection to scene: {e}")
        # Try to find any collection to add it to
        for coll in bpy.data.collections:
            try:
                coll.children.link(collection)
                break
            except:
                continue
    
    return collection


def get_parent_collection(obj):
    """
    Finds the first collection containing the object.
    
    Args:
        obj (bpy.types.Object): Object to find parent collection for
        
    Returns:
        bpy.types.Collection or None: Parent collection or None if not found
    """
    for collection in bpy.data.collections:
        if obj.name in collection.objects:
            return collection
    
    # If object is in the master scene collection
    if obj.name in bpy.context.scene.collection.objects:
        return bpy.context.scene.collection
    
    return None


def find_cloner_collections():
    """
    Finds all collections that appear to be cloner collections.
    
    Returns:
        list: List of cloner collections
    """
    cloner_collections = []
    for collection in bpy.data.collections:
        if (collection.name.startswith("cloner") or 
            collection.name.startswith("cloner_") or
            "cloner" in collection.name.lower()):
            cloner_collections.append(collection)
    
    return cloner_collections


def is_collection_empty(collection):
    """
    Checks if a collection is empty (no objects and no child collections).
    
    Args:
        collection (bpy.types.Collection): Collection to check
        
    Returns:
        bool: True if collection is empty
    """
    return len(collection.objects) == 0 and len(collection.children) == 0


def get_collection_hierarchy_depth(collection, max_depth=10):
    """
    Gets the depth of a collection in the hierarchy.
    
    Args:
        collection (bpy.types.Collection): Collection to check
        max_depth (int): Maximum depth to prevent infinite recursion
        
    Returns:
        int: Depth of collection (0 for root collections)
    """
    if max_depth <= 0:
        return 0
    
    # Check if this collection is a child of any other collection
    for parent_collection in bpy.data.collections:
        if collection.name in [child.name for child in parent_collection.children]:
            return 1 + get_collection_hierarchy_depth(parent_collection, max_depth - 1)
    
    # Check if it's a child of the scene collection
    if collection.name in [child.name for child in bpy.context.scene.collection.children]:
        return 1
    
    return 0


def organize_cloner_collections():
    """
    Organizes cloner collections by moving them to a dedicated parent collection.
    
    Returns:
        bpy.types.Collection: The parent cloner collection
    """
    # Create or find the main cloner collection
    main_cloner_collection_name = "Advanced_Cloners"
    
    if main_cloner_collection_name in bpy.data.collections:
        main_collection = bpy.data.collections[main_cloner_collection_name]
    else:
        main_collection = bpy.data.collections.new(main_cloner_collection_name)
        bpy.context.scene.collection.children.link(main_collection)
    
    # Find all cloner collections
    cloner_collections = find_cloner_collections()
    
    # Move cloner collections to the main collection
    for collection in cloner_collections:
        if collection != main_collection and collection.name != main_cloner_collection_name:
            # Remove from current parent
            for parent in bpy.data.collections:
                if collection.name in [child.name for child in parent.children]:
                    try:
                        parent.children.unlink(collection)
                    except:
                        pass
            
            # Remove from scene collection if it's there
            if collection.name in [child.name for child in bpy.context.scene.collection.children]:
                try:
                    bpy.context.scene.collection.children.unlink(collection)
                except:
                    pass
            
            # Add to main cloner collection
            try:
                main_collection.children.link(collection)
            except:
                pass
    
    return main_collection


def register():
    """Register collection manager components"""
    pass


def unregister():
    """Unregister collection manager components"""
    pass
