"""
Унифицированные функции именования для всех типов клонеров.

Содержит логику именования, которая используется
всеми типами клонеров (object, collection, stacked).
"""

import bpy

def generate_cloner_name(source_name: str, cloner_type: str = None, mode: str = "object") -> str:
    """
    Генерирует уникальное имя для объекта клонера.

    Унифицирует логику из всех модулей, сохраняя оригинальное поведение.

    Args:
        source_name: Имя исходного объекта или коллекции
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE) - для object/stacked режимов
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        str: Уникальное имя клонера
    """
    try:
        if mode == "object":
            # Object mode: включает тип клонера в имя
            if not cloner_type:
                raise ValueError("cloner_type required for object mode")
            cloner_name = f"Cloner_{source_name}_{cloner_type}"
            counter = 1
            while cloner_name in bpy.data.objects:
                cloner_name = f"Cloner_{source_name}_{cloner_type}_{counter:03d}"
                counter += 1
            return cloner_name

        elif mode == "collection":
            # Collection mode: простое имя без типа клонера
            cloner_name = f"Cloner_{source_name}"
            counter = 1
            while cloner_name in bpy.data.objects:
                cloner_name = f"Cloner_{source_name}_{counter:03d}"
                counter += 1
            return cloner_name

        elif mode == "stacked":
            # Для stacked клонеров используем object логику
            if not cloner_type:
                raise ValueError("cloner_type required for stacked mode")
            cloner_name = f"Cloner_{source_name}_{cloner_type}"
            counter = 1
            while cloner_name in bpy.data.objects:
                cloner_name = f"Cloner_{source_name}_{cloner_type}_{counter:03d}"
                counter += 1
            return cloner_name

        else:
            raise ValueError(f"Unknown mode: {mode}")

    except Exception as e:
        print(f"Ошибка при генерации имени клонера: {e}")
        return f"Cloner_{source_name}"

def generate_collection_name(source_name: str, cloner_type: str, mode: str = "object") -> str:
    """
    Генерирует уникальное имя для коллекции клонера.

    Унифицирует логику из всех модулей, сохраняя оригинальное поведение.

    Args:
        source_name: Имя исходного объекта или коллекции
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        str: Уникальное имя коллекции клонера
    """
    try:
        # Логика одинакова для всех режимов
        cloner_collection_name = f"cloner_{cloner_type.lower()}_{source_name}"
        counter = 1
        while cloner_collection_name in bpy.data.collections:
            cloner_collection_name = f"cloner_{cloner_type.lower()}_{source_name}_{counter:03d}"
            counter += 1
        return cloner_collection_name

    except Exception as e:
        print(f"Ошибка при генерации имени коллекции: {e}")
        return f"cloner_{cloner_type.lower()}_{source_name}"

def generate_modifier_name(target_obj: bpy.types.Object, cloner_type: str, mode: str = "object") -> str:
    """
    Генерирует уникальное имя для модификатора клонера.

    Унифицирует логику из всех модулей, сохраняя оригинальное поведение.

    Args:
        target_obj: Объект, к которому добавляется модификатор
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        str: Уникальное имя модификатора
    """
    try:
        if mode == "object":
            # Object mode: генерируем уникальное имя модификатора
            # Получаем базовое имя через universal_factory
            from ....core.factories.universal_factory import universal_factory
            base_name = universal_factory.get_cloner_mod_name(cloner_type)

            modifier_name = base_name
            counter = 1
            while modifier_name in target_obj.modifiers:
                modifier_name = f"{base_name}.{counter:03d}"
                counter += 1
            return modifier_name

        elif mode == "collection":
            # Collection mode: используем базовое имя без счетчика
            from ....core.factories.universal_factory import universal_factory
            return universal_factory.get_cloner_mod_name(cloner_type)

        elif mode == "stacked":
            # Stacked mode: генерируем уникальное имя модификатора
            from ....core.factories.universal_factory import universal_factory
            base_mod_name = universal_factory.get_cloner_mod_name(cloner_type)

            modifier_name = f"{base_mod_name}_Cloner"
            counter = 1
            while modifier_name in target_obj.modifiers:
                modifier_name = f"{base_mod_name}_Cloner_{counter:03d}"
                counter += 1
            return modifier_name

        else:
            raise ValueError(f"Unknown mode: {mode}")

    except Exception as e:
        print(f"Ошибка при генерации имени модификатора: {e}")
        return f"{cloner_type}_Cloner"

def generate_node_group_name(source_name: str, cloner_type: str, mode: str = "object") -> str:
    """
    Генерирует уникальное имя для node группы клонера.

    Унифицирует логику из всех модулей, сохраняя оригинальное поведение.

    Args:
        source_name: Имя исходного объекта или коллекции
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        str: Уникальное имя node группы
    """
    try:
        if mode == "object":
            # Object mode: создает имя с префиксом ObjectCloner
            node_group_name = f"ObjectCloner_{cloner_type}_{source_name}"
            counter = 1
            while node_group_name in bpy.data.node_groups:
                node_group_name = f"ObjectCloner_{cloner_type}_{source_name}_{counter:03d}"
                counter += 1
            return node_group_name

        elif mode == "collection":
            # Collection mode: создает имя с префиксом CollectionCloner
            node_group_name = f"CollectionCloner_{cloner_type}_{source_name}"
            counter = 1
            while node_group_name in bpy.data.node_groups:
                node_group_name = f"CollectionCloner_{cloner_type}_{source_name}_{counter:03d}"
                counter += 1
            return node_group_name

        elif mode == "stacked":
            # Stacked mode: создает имя с суффиксом Stack
            type_name = {
                "GRID": "Grid",
                "LINEAR": "Linear",
                "CIRCLE": "Circle"
            }.get(cloner_type, cloner_type)

            return f"{type_name}_Stack_{source_name}"

        else:
            raise ValueError(f"Unknown mode: {mode}")

    except Exception as e:
        print(f"Ошибка при генерации имени node группы: {e}")
        return f"{cloner_type}_Cloner_{source_name}"

# Дополнительные утилиты для обеспечения уникальности
def ensure_unique_modifier_name(target_obj: bpy.types.Object, base_name: str) -> str:
    """
    Обеспечивает уникальность имени модификатора в пределах объекта.

    Args:
        target_obj: Исходный объект
        base_name: Базовое имя модификатора

    Returns:
        str: Уникальное имя модификатора
    """
    modifier_name = base_name
    counter = 1
    while modifier_name in target_obj.modifiers:
        modifier_name = f"{base_name}_{counter:03d}"
        counter += 1
    return modifier_name

def ensure_unique_node_group_name(base_name: str) -> str:
    """
    Обеспечивает уникальность имени node group в сцене.

    Общая логика для всех типов клонеров.

    Args:
        base_name: Базовое имя node group

    Returns:
        str: Уникальное имя node group
    """
    node_group_name = base_name
    counter = 1
    while node_group_name in bpy.data.node_groups:
        node_group_name = f"{base_name}_{counter:03d}"
        counter += 1
    return node_group_name
