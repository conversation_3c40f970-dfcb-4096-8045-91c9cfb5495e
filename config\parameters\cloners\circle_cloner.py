"""
Parameter definitions for Circle Cloner.

This module defines all parameters for the Circle Cloner component
using the new unified parameter system.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    get_standard_parameter_set
)


# Basic Circle Cloner parameters
CIRCLE_BASIC_GROUP = ParameterGroup(
    name="basic",
    description="Basic circle cloner settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Count",
            param_type=ParameterType.INT,
            default_value=8,
            min_value=3,
            max_value=1000,
            description="Number of instances around the circle",
            ui_group="Basic Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Radius",
            param_type=ParameterType.FLOAT,
            default_value=2.0,
            min_value=0.0,
            description="Radius of the circle",
            ui_group="Basic Settings",
            ui_order=2
        )
        # Height parameter removed - not needed for basic Circle cloner
    ]
)

# Input/Output sockets - using standard cloner IO parameters
# CIRCLE_IO_GROUP removed - now using get_standard_parameter_set("cloner_io")

# Complete parameter set for Circle Cloner
CIRCLE_CLONER_PARAMETERS = ComponentParameterSet(
    component_type="CLONER",
    component_id="CIRCLE",
    description="Circle Cloner parameter set - creates instances in a circular pattern",
    version="1.0",
    groups=[
        get_standard_parameter_set("cloner_io"),  # Using standard cloner IO parameters
        CIRCLE_BASIC_GROUP,
        get_standard_parameter_set("global_transform"),
        get_standard_parameter_set("instance"),
        get_standard_parameter_set("random"),
        get_standard_parameter_set("effector_control")
    ]
)
