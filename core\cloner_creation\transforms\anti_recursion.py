"""
Унифицированная анти-рекурсия для всех типов клонеров.

ОБЪЕДИНЕНО: Содержит логику анти-рекурсии из transforms/anti_recursion.py
и object_anti_recursion.py для устранения дублирования.

Содержит логику анти-рекурсии, которая используется
всеми типами клонеров (object, collection, stacked).

Зоны ответственности:
- Создание узлов анти-рекурсии для всех типов клонеров
- Настройка интерфейса для анти-рекурсии
- Создание узлов для подключения эффекторов
- Получение настроек анти-рекурсии из сцены
"""

import bpy
from typing import Optional

def create_anti_recursion(node_group, transform_node, group_in, group_out,
                         use_anti_recursion: bool = True, mode: str = "stacked") -> Optional[bpy.types.Node]:
    """
    Создает узлы анти-рекурсии для клонера.

    Унифицирует логику из stacked, object и collection модулей.
    Создает стандартную схему:
    1. Join Geometry - объединяет входную геометрию с результатом клонера
    2. Realize Instances - реализует инстансы
    3. Switch - переключает между режимами по параметру "Realize Instances"

    Args:
        node_group: Node group для создания узлов
        transform_node: Узел трансформации (выход клонера)
        group_in: Входной узел группы
        group_out: Выходной узел группы
        use_anti_recursion: Использовать анти-рекурсию
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Финальный Switch узел или None в случае ошибки
    """
    try:
        nodes = node_group.nodes
        links = node_group.links

        if use_anti_recursion:
            if mode == "stacked":
                # СТЕКОВЫЕ КЛОНЕРЫ: Используем старую проверенную логику
                # НЕ объединяем с входной геометрией, только реализуем инстансы

                # 1. Join Geometry node - только для разрыва иерархии инстансов
                join_geometry = nodes.new('GeometryNodeJoinGeometry')
                join_geometry.name = "Anti-Recursion Join Geometry"
                join_geometry.location = (transform_node.location.x + 100, transform_node.location.y)

                # 2. Realize Instances node - преобразует инстансы в реальную геометрию
                realize_instances = nodes.new('GeometryNodeRealizeInstances')
                realize_instances.name = "Anti-Recursion Realize"
                realize_instances.location = (transform_node.location.x + 200, transform_node.location.y)

                # 3. Switch node - переключает между режимами
                switch_node = nodes.new('GeometryNodeSwitch')
                switch_node.name = "Anti-Recursion Switch"
                switch_node.input_type = 'GEOMETRY'
                switch_node.location = (transform_node.location.x + 300, transform_node.location.y)

                # Подключаем узлы (ТОЧНАЯ КОПИЯ СТАРОЙ ЛОГИКИ ДЛЯ СТЕКОВЫХ КЛОНЕРОВ)
                # Join Geometry: только выход клонера (используется только для realize_instances)
                links.new(transform_node.outputs['Geometry'], join_geometry.inputs['Geometry'])

                # Realize Instances: реализуем инстансы из join_geometry
                links.new(join_geometry.outputs['Geometry'], realize_instances.inputs['Geometry'])

                # Switch: переключаем между режимами
                # FALSE (анти-рекурсия ВЫКЛЮЧЕНА) = прямой выход клонера
                links.new(transform_node.outputs['Geometry'], switch_node.inputs['False'])
                # TRUE (анти-рекурсия ВКЛЮЧЕНА) = реализованная геометрия
                links.new(realize_instances.outputs['Geometry'], switch_node.inputs['True'])
            else:
                # ОБЪЕКТНЫЕ И КОЛЛЕКЦИОННЫЕ КЛОНЕРЫ: Используем новую логику с объединением

                # 1. Join Geometry node - объединяет входную геометрию с результатом клонера
                join_geometry = nodes.new('GeometryNodeJoinGeometry')
                join_geometry.name = "Anti-Recursion Join Geometry"
                join_geometry.location = (400, 0)

                # 2. Realize Instances node - преобразует инстансы в реальную геометрию
                realize_instances = nodes.new('GeometryNodeRealizeInstances')
                realize_instances.name = "Anti-Recursion Realize"
                realize_instances.location = (500, 0)

                # 3. Switch node - переключает между режимами
                switch_node = nodes.new('GeometryNodeSwitch')
                switch_node.name = "Anti-Recursion Switch"
                switch_node.input_type = 'GEOMETRY'
                switch_node.location = (600, 0)

                # Подключаем узлы (НОВАЯ ЛОГИКА ДЛЯ ОБЪЕКТНЫХ/КОЛЛЕКЦИОННЫХ КЛОНЕРОВ)
                # Join Geometry: объединяем входную геометрию с результатом клонера
                links.new(group_in.outputs['Geometry'], join_geometry.inputs['Geometry'])
                links.new(transform_node.outputs['Geometry'], join_geometry.inputs['Geometry'])

                # Realize Instances: реализуем инстансы
                links.new(join_geometry.outputs['Geometry'], realize_instances.inputs['Geometry'])

                # Switch: переключаем между режимами
                links.new(join_geometry.outputs['Geometry'], switch_node.inputs['False'])  # Без реализации
                links.new(realize_instances.outputs['Geometry'], switch_node.inputs['True'])  # С реализацией

            # Проверяем, существует ли сокет 'Realize Instances' в group_in
            realize_instances_socket = None
            for output in group_in.outputs:
                if output.name == 'Realize Instances':
                    realize_instances_socket = output
                    break

            if realize_instances_socket:
                links.new(realize_instances_socket, switch_node.inputs['Switch'])
                print(f"[DEBUG] Подключен сокет 'Realize Instances' к Switch узлу для {mode} клонера")
            else:
                # Если сокет не найден, используем значение по умолчанию из настроек
                switch_node.inputs['Switch'].default_value = use_anti_recursion
                print(f"[DEBUG] Сокет 'Realize Instances' НЕ найден для {mode} клонера, используем значение по умолчанию: {use_anti_recursion}")

            # Подключаем к выходу
            links.new(switch_node.outputs['Output'], group_out.inputs['Geometry'])

            return switch_node
        else:
            # Без анти-рекурсии - прямое подключение
            links.new(transform_node.outputs['Geometry'], group_out.inputs['Geometry'])
            return transform_node

    except Exception as e:
        print(f"Ошибка при создании анти-рекурсии ({mode}): {e}")
        return None

def create_stacked_anti_recursion(node_group, transform_node, group_in, group_out,
                                use_anti_recursion: bool = True) -> Optional[bpy.types.Node]:
    """
    Создает узлы анти-рекурсии для стекового клонера.

    ТОЧНАЯ КОПИЯ СТАРОЙ РАБОЧЕЙ РЕАЛИЗАЦИИ из stacked_linear_builder.py

    Args:
        node_group: Node group для создания узлов
        transform_node: Узел трансформации (выход клонера)
        group_in: Входной узел группы
        group_out: Выходной узел группы
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        Node: Финальный Switch узел или None в случае ошибки
    """
    try:
        nodes = node_group.nodes
        links = node_group.links

        # ТОЧНАЯ КОПИЯ СТАРОЙ ЛОГИКИ ИЗ stacked_linear_builder.py
        # Join Geometry node
        join_geometry = nodes.new('GeometryNodeJoinGeometry')
        join_geometry.name = "Anti-Recursion Join Geometry"

        # Realize Instances node
        realize_instances = nodes.new('GeometryNodeRealizeInstances')
        realize_instances.name = "Anti-Recursion Realize"

        # Switch node
        switch_node = nodes.new('GeometryNodeSwitch')
        switch_node.input_type = 'GEOMETRY'
        switch_node.name = "Anti-Recursion Switch"

        # Соединяем узлы (ТОЧНАЯ КОПИЯ СТАРОЙ ЛОГИКИ)
        links.new(transform_node.outputs['Geometry'], join_geometry.inputs[0])
        links.new(join_geometry.outputs[0], realize_instances.inputs['Geometry'])
        links.new(group_in.outputs['Realize Instances'], switch_node.inputs['Switch'])
        links.new(transform_node.outputs['Geometry'], switch_node.inputs[False])
        links.new(realize_instances.outputs['Geometry'], switch_node.inputs[True])

        # Подключаем к выходу
        links.new(switch_node.outputs['Output'], group_out.inputs['Geometry'])

        print(f"[DEBUG] Создана старая анти-рекурсия для стекового клонера")
        return switch_node

    except Exception as e:
        print(f"Ошибка при создании анти-рекурсии для стекового клонера: {e}")
        return None

def create_object_anti_recursion(node_group, transform_node, group_in, group_out,
                                use_anti_recursion: bool = True) -> Optional[bpy.types.Node]:
    """
    Создает узлы анти-рекурсии для object клонера.

    Wrapper для object клонеров.

    Args:
        node_group: Node group для создания узлов
        transform_node: Узел трансформации (выход клонера)
        group_in: Входной узел группы
        group_out: Выходной узел группы
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        Node: Финальный Switch узел или None в случае ошибки
    """
    return create_anti_recursion(node_group, transform_node, group_in, group_out, use_anti_recursion, "object")

def create_collection_anti_recursion(node_group, transform_node, group_in, group_out,
                                   use_anti_recursion: bool = True) -> Optional[bpy.types.Node]:
    """
    Создает узлы анти-рекурсии для collection клонера.

    Wrapper для collection клонеров.

    Args:
        node_group: Node group для создания узлов
        transform_node: Узел трансформации (выход клонера)
        group_in: Входной узел группы
        group_out: Выходной узел группы
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        Node: Финальный Switch узел или None в случае ошибки
    """
    return create_anti_recursion(node_group, transform_node, group_in, group_out, use_anti_recursion, "collection")


# ========================================
# ФУНКЦИИ ИЗ object_anti_recursion.py
# ========================================

def setup_anti_recursion_interface(node_group, use_anti_recursion=False):
    """
    Настраивает интерфейс для анти-рекурсии.

    ПЕРЕНЕСЕНО из object_anti_recursion.py для унификации.

    Args:
        node_group: Группа узлов
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        bool: True если настройка прошла успешно
    """
    try:
        # Add parameter for enabling/disabling instance realization only if anti-recursion is enabled
        print(f"[DEBUG] setup_anti_recursion_interface: use_anti_recursion = {use_anti_recursion}")
        if use_anti_recursion:
            realize_instances_input = node_group.interface.new_socket(name="Realize Instances", in_out='INPUT', socket_type='NodeSocketBool')
            realize_instances_input.default_value = use_anti_recursion
            realize_instances_input.description = "Enable to prevent recursion depth issues when creating chains of cloners"
            print(f"[DEBUG] Добавлен сокет 'Realize Instances' со значением {use_anti_recursion}")
        else:
            print(f"[DEBUG] Сокет 'Realize Instances' НЕ добавлен (анти-рекурсия выключена)")
        return True
    except Exception as e:
        print(f"Ошибка при настройке интерфейса анти-рекурсии: {e}")
        return False

def create_effector_input_node(nodes, output_node):
    """
    Создает специальный узел для подключения эффекторов.

    ПЕРЕНЕСЕНО из object_anti_recursion.py для унификации.

    Args:
        nodes: Коллекция узлов
        output_node: Выходной узел

    Returns:
        node: Созданный узел эффектора или None
    """
    try:
        # Создаем специальный узел для подключения эффекторов
        effector_input_node = nodes.new('GeometryNodeGroup')
        effector_input_node.name = "Effector_Input"
        effector_input_node.location = (output_node.location.x + 50, output_node.location.y - 150)

        # Создаем пустую группу узлов для этого узла
        if not bpy.data.node_groups.get("EffectorInputGroup"):
            effector_group = bpy.data.node_groups.new(name="EffectorInputGroup", type='GeometryNodeTree')
            # Создаем входы и выходы
            effector_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
            effector_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

            # Создаем узлы внутри группы
            group_in_node = effector_group.nodes.new('NodeGroupInput')
            group_out_node = effector_group.nodes.new('NodeGroupOutput')
            group_in_node.location = (-200, 0)
            group_out_node.location = (200, 0)

            # Соединяем вход с выходом
            effector_group.links.new(group_in_node.outputs['Geometry'], group_out_node.inputs['Geometry'])

        # Устанавливаем группу узлов
        effector_input_node.node_tree = bpy.data.node_groups.get("EffectorInputGroup")
        return effector_input_node
    except Exception as e:
        print(f"Ошибка при создании узла эффектора: {e}")
        return None

def create_anti_recursion_nodes(nodes, links, output_node, output_socket_name, group_in, group_out, use_anti_recursion=False):
    """
    Создает узлы для анти-рекурсии (object builders версия).

    ПЕРЕНЕСЕНО из object_anti_recursion.py для унификации.

    ПРИМЕЧАНИЕ: Эта функция используется в base_builder.py для object клонеров.
    Отличается от create_anti_recursion() тем, что имеет другую сигнатуру и логику.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        output_node: Выходной узел
        output_socket_name: Имя выходного сокета
        group_in: Входной узел группы
        group_out: Выходной узел группы
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        node: Финальный узел с анти-рекурсией или None
    """
    try:
        if use_anti_recursion:
            # Создаем узел Realize Instances для финального выхода
            final_realize = nodes.new('GeometryNodeRealizeInstances')
            final_realize.name = "Final Realize Instances"
            final_realize.location = (output_node.location.x + 100, output_node.location.y)

            # Создаем узел Switch для финального выхода (используем имя, которое ищут эффекторы)
            switch_node = nodes.new('GeometryNodeSwitch')
            switch_node.input_type = 'GEOMETRY'
            switch_node.name = "Final Realize Switch"
            switch_node.location = (output_node.location.x + 200, output_node.location.y)

            # Соединяем трансформ с финальным Realize Instances
            links.new(output_node.outputs[output_socket_name], final_realize.inputs['Geometry'])

            # Настраиваем финальный переключатель
            if 'Realize Instances' in group_in.outputs:
                links.new(group_in.outputs['Realize Instances'], switch_node.inputs['Switch'])
            else:
                switch_node.inputs['Switch'].default_value = use_anti_recursion

            links.new(output_node.outputs[output_socket_name], switch_node.inputs[False])  # Обычный выход
            links.new(final_realize.outputs['Geometry'], switch_node.inputs[True])  # "Реализованный" выход

            # Соединяем переключатель с выходом
            links.new(switch_node.outputs[0], group_out.inputs['Geometry'])
            return switch_node
        else:
            # Если анти-рекурсия выключена, создаем простой Switch узел для совместимости с эффекторами
            switch_node = nodes.new('GeometryNodeSwitch')
            switch_node.input_type = 'GEOMETRY'
            switch_node.name = "Final Realize Switch"  # Используем то же имя для совместимости
            switch_node.location = (output_node.location.x + 200, output_node.location.y)

            # Устанавливаем Switch в режим False (без анти-рекурсии)
            switch_node.inputs['Switch'].default_value = False

            # Подключаем исходный узел к обоим входам Switch (False и True одинаковы)
            links.new(output_node.outputs[output_socket_name], switch_node.inputs[False])
            links.new(output_node.outputs[output_socket_name], switch_node.inputs[True])

            # Соединяем переключатель с выходом
            links.new(switch_node.outputs[0], group_out.inputs['Geometry'])
            return switch_node
    except Exception as e:
        print(f"Ошибка при создании узлов анти-рекурсии: {e}")
        return None

def get_anti_recursion_setting():
    """
    Получает настройку анти-рекурсии из сцены.

    ПЕРЕНЕСЕНО из object_anti_recursion.py для унификации.

    Returns:
        bool: True если анти-рекурсия включена
    """
    try:
        if hasattr(bpy.context.scene, "use_anti_recursion"):
            return bpy.context.scene.use_anti_recursion
        return False
    except Exception as e:
        print(f"Ошибка при получении настройки анти-рекурсии: {e}")
        return False
