"""
Effector UI operators module.

This module contains all UI operators related to effector functionality,
including field management, auto-linking, and effector controls.
"""

# Import all effector operators from the main module
from .effector_ui_ops import (
    EFFECTOR_OT_toggle_expanded,
    EFFECTOR_OT_add_field,
    EFFECTOR_OT_remove_field,
    EFFECTOR_OT_auto_link,
    EFFECTOR_OT_update_stacked_cloners
)

# Public API
__all__ = [
    'EFFECTOR_OT_toggle_expanded',
    'EFFECTOR_OT_add_field',
    'EFFECTOR_OT_remove_field',
    'EFFECTOR_OT_auto_link',
    'EFFECTOR_OT_update_stacked_cloners'
]


def register():
    """Register effector UI operators"""
    from . import effector_ui_ops
    effector_ui_ops.register()


def unregister():
    """Unregister effector UI operators"""
    from . import effector_ui_ops
    effector_ui_ops.unregister()
