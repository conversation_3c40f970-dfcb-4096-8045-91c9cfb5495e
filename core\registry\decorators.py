"""
Декораторы для автоматической регистрации компонентов.

Система использует отложенную регистрацию для избежания циклических импортов
и обеспечения правильного порядка инициализации компонентов.
"""

from typing import Type, Callable
from .component_registry import ComponentMeta

# Глобальная очередь для отложенной регистрации компонентов
_registration_queue = []

def _queue_component_registration(component_type: str, cls: Type, meta: ComponentMeta):
    """
    Добавляет компонент в очередь для отложенной регистрации.

    Args:
        component_type: Тип компонента (CLONER, EFFECTOR, FIELD)
        cls: Класс компонента
        meta: Метаданные компонента
    """
    _registration_queue.append((component_type, cls, meta))

def _try_immediate_registration(component_type: str, cls: Type, meta: ComponentMeta) -> bool:
    """
    Пытается выполнить немедленную регистрацию компонента.

    Returns:
        bool: True если регистрация прошла успешно, False если нужна отложенная регистрация
    """
    try:
        # Пытаемся безопасно импортировать реестр
        from .component_registry import component_registry

        # Проверяем, что реестр инициализирован
        if not hasattr(component_registry, '_cloners'):
            return False

        # Выполняем регистрацию
        if component_type == 'CLONER':
            component_registry.register_cloner(cls, meta)
        elif component_type == 'EFFECTOR':
            component_registry.register_effector(cls, meta)
        elif component_type == 'FIELD':
            component_registry.register_field(cls, meta)

        return True
    except (ImportError, AttributeError):
        # Реестр еще не готов, используем отложенную регистрацию
        return False

def process_registration_queue():
    """
    Обрабатывает очередь отложенной регистрации компонентов.

    Вызывается из системы регистрации после инициализации реестра.
    """
    global _registration_queue

    try:
        from .component_registry import component_registry

        for component_type, cls, meta in _registration_queue:
            if component_type == 'CLONER':
                component_registry.register_cloner(cls, meta)
            elif component_type == 'EFFECTOR':
                component_registry.register_effector(cls, meta)
            elif component_type == 'FIELD':
                component_registry.register_field(cls, meta)

        # Очищаем очередь после обработки
        _registration_queue.clear()

    except ImportError:
        # Реестр все еще недоступен, оставляем очередь для следующей попытки
        pass

def get_registration_queue_size() -> int:
    """Возвращает размер очереди регистрации для диагностики."""
    return len(_registration_queue)

def register_cloner(component_id: str,
                   display_name: str,
                   description: str,
                   icon: str) -> Callable[[Type], Type]:
    """
    Декоратор для автоматической регистрации клонера.

    Использует гибридную систему регистрации:
    1. Пытается зарегистрировать немедленно
    2. При неудаче добавляет в очередь для отложенной регистрации

    Args:
        component_id: Уникальный идентификатор клонера
        display_name: Отображаемое имя для UI
        description: Описание функциональности
        icon: Иконка для UI

    Usage:
        @register_cloner("SPIRAL", "Spiral Cloner", "Create spiral instances", "FORCE_VORTEX")
        class SpiralCloner(ClonerBase):
            ...
    """
    def decorator(cls: Type) -> Type:
        meta = ComponentMeta(component_id, display_name, description, icon, "CLONER")
        cls._component_meta = meta
        cls._component_type = "CLONER"
        cls._component_id = component_id

        # Пытаемся зарегистрировать немедленно
        if not _try_immediate_registration("CLONER", cls, meta):
            # При неудаче добавляем в очередь
            _queue_component_registration("CLONER", cls, meta)

        return cls
    return decorator

def register_effector(component_id: str,
                     display_name: str,
                     description: str,
                     icon: str) -> Callable[[Type], Type]:
    """
    Декоратор для автоматической регистрации эффектора.

    Использует гибридную систему регистрации:
    1. Пытается зарегистрировать немедленно
    2. При неудаче добавляет в очередь для отложенной регистрации

    Args:
        component_id: Уникальный идентификатор эффектора
        display_name: Отображаемое имя для UI
        description: Описание функциональности
        icon: Иконка для UI

    Usage:
        @register_effector("WAVE", "Wave Effector", "Apply wave transformations", "FORCE_WAVE")
        class WaveEffector(EffectorBase):
            ...
    """
    def decorator(cls: Type) -> Type:
        meta = ComponentMeta(component_id, display_name, description, icon, "EFFECTOR")
        cls._component_meta = meta
        cls._component_type = "EFFECTOR"
        cls._component_id = component_id

        # Пытаемся зарегистрировать немедленно
        if not _try_immediate_registration("EFFECTOR", cls, meta):
            # При неудаче добавляем в очередь
            _queue_component_registration("EFFECTOR", cls, meta)

        return cls
    return decorator

def register_field(component_id: str,
                  display_name: str,
                  description: str,
                  icon: str) -> Callable[[Type], Type]:
    """
    Декоратор для автоматической регистрации поля.

    Использует гибридную систему регистрации:
    1. Пытается зарегистрировать немедленно
    2. При неудаче добавляет в очередь для отложенной регистрации

    Args:
        component_id: Уникальный идентификатор поля
        display_name: Отображаемое имя для UI
        description: Описание функциональности
        icon: Иконка для UI

    Usage:
        @register_field("BOX", "Box Field", "Create box influence area", "MESH_CUBE")
        class BoxField(FieldBase):
            ...
    """
    def decorator(cls: Type) -> Type:
        meta = ComponentMeta(component_id, display_name, description, icon, "FIELD")
        cls._component_meta = meta
        cls._component_type = "FIELD"
        cls._component_id = component_id

        # Пытаемся зарегистрировать немедленно
        if not _try_immediate_registration("FIELD", cls, meta):
            # При неудаче добавляем в очередь
            _queue_component_registration("FIELD", cls, meta)

        return cls
    return decorator
