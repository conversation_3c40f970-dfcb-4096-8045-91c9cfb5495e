"""
Collection Circle Cloner Parameter Definitions

This module defines all parameters for the Collection Circle Cloner component using the new
unified parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

Collection Circle Cloner creates instances of collections in a circular pattern.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    get_standard_parameter_set
)


# Basic Collection Circle Cloner parameters
COLLECTION_CIRCLE_BASIC_GROUP = ParameterGroup(
    name="basic",
    description="Basic collection circle cloner settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Count",
            param_type=ParameterType.INT,
            default_value=8,
            min_value=3,
            max_value=1000,
            description="Number of instances around the circle",
            ui_group="Basic Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Radius",
            param_type=ParameterType.FLOAT,
            default_value=2.0,
            min_value=0.0,
            description="Radius of the circle",
            ui_group="Basic Settings",
            ui_order=2
        )
    ]
)

# Complete parameter set for Collection Circle Cloner
COLLECTION_CIRCLE_CLONER_PARAMETERS = ComponentParameterSet(
    component_type="CLONER",
    component_id="COLLECTION_CIRCLE",
    description="Collection Circle Cloner parameter set - creates instances of collections in a circular pattern",
    version="1.0",
    groups=[
        get_standard_parameter_set("collection_cloner_io"),  # Using collection cloner IO parameters
        COLLECTION_CIRCLE_BASIC_GROUP,
        get_standard_parameter_set("global_transform"),
        get_standard_parameter_set("instance"),
        get_standard_parameter_set("random"),
        get_standard_parameter_set("effector_control")
    ]
)
