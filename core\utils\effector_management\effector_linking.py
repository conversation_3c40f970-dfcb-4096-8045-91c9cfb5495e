"""
Effector linking utilities for the effector system.

This module handles the creation and management of effector chains,
finding insertion points, and linking effectors to cloners.
"""

import bpy
from ....models.effectors import EFFECTOR_NODE_GROUP_PREFIXES
from .connection_management import safe_link_new


def link_effector_to_cloner(obj, cloner_mod, effector_mod):
    """
    Связывает эффектор с клонером.

    Args:
        obj: Объект с клонером
        cloner_mod: Модификатор клонера
        effector_mod: Модификатор эффектора

    Returns:
        bool: True если связывание успешно
    """
    try:
        # Получаем список связанных эффекторов
        linked_effectors_prop = cloner_mod.node_group.get("linked_effectors", [])
        # ИСПРАВЛЕНИЕ: Преобразуем IDPropertyArray в обычный список Python
        linked_effectors = list(linked_effectors_prop)

        # Добавляем эффектор в список, если его там нет
        if effector_mod.name not in linked_effectors:
            linked_effectors.append(effector_mod.name)
            cloner_mod.node_group["linked_effectors"] = linked_effectors
            print(f"[DEBUG] Эффектор {effector_mod.name} добавлен к клонеру {cloner_mod.name}")

        # Применяем эффектор к клонеру
        from .effector_application import apply_effector_to_cloner
        return apply_effector_to_cloner(obj, cloner_mod, effector_mod)

    except Exception as e:
        print(f"[ERROR] link_effector_to_cloner: {e}")
        return False


def unlink_effector_from_cloner(obj, cloner_mod, effector_mod):
    """
    Отвязывает эффектор от клонера и восстанавливает клонер.

    Args:
        obj: Объект с клонером
        cloner_mod: Модификатор клонера
        effector_mod: Модификатор эффектора

    Returns:
        bool: True если отвязывание успешно
    """
    try:
        # Проверяем, что клонер имеет нод-группу
        if not cloner_mod or not cloner_mod.node_group:
            print(f"[ERROR] Клонер не имеет нод-группы")
            return False
            
        # Получаем список связанных эффекторов
        linked_effectors = list(cloner_mod.node_group.get("linked_effectors", []))
        print(f"[DEBUG] Текущие связанные эффекторы: {linked_effectors}")

        # Удаляем эффектор из списка
        if effector_mod.name in linked_effectors:
            linked_effectors.remove(effector_mod.name)
            cloner_mod.node_group["linked_effectors"] = linked_effectors
            print(f"[DEBUG] Эффектор {effector_mod.name} удален из клонера {cloner_mod.name}")

            # Удаляем узел эффектора из группы узлов клонера
            nodes = cloner_mod.node_group.nodes
            effector_node_name = f"Effector_{effector_mod.name}"
            if effector_node_name in nodes:
                nodes.remove(nodes[effector_node_name])
                print(f"[DEBUG] Узел эффектора {effector_node_name} удален")
                
            # Проверяем, был ли это последний эффектор
            if not linked_effectors:
                # Это был последний эффектор, восстанавливаем прямые связи
                print(f"[DEBUG] Последний эффектор отвязан, восстанавливаем оригинальное состояние клонера")
                
                # Восстанавливаем прямые связи
                from .connection_management import restore_direct_connection_improved
                restore_direct_connection_improved(cloner_mod.node_group)
                
                # Проверяем и восстанавливаем Realize узел для анти-рекурсии
                from .update_synchronization import restore_realize_node_for_anti_recursion
                
                # Находим Switch узел (может быть Anti-Recursion Switch или Final Realize Switch)
                switch_node = None
                for node in cloner_mod.node_group.nodes:
                    if node.name in ["Anti-Recursion Switch", "Final Realize Switch"]:
                        switch_node = node
                        break
                        
                if switch_node:
                    restore_realize_node_for_anti_recursion(cloner_mod.node_group, switch_node)
                
                # Сбрасываем кэш для обновления всех зависимостей
                try:
                    # Сбрасываем кэш всех нод-групп, чтобы Blender пересчитал все связи
                    cloner_mod.node_group.update_tag()
                    bpy.context.view_layer.update()
                except Exception as e:
                    print(f"[ERROR] Ошибка при обновлении кэша: {e}")
                
            # Обновляем клонер с оставшимися эффекторами
            from .update_synchronization import update_cloner_with_effectors
            update_cloner_with_effectors(obj, cloner_mod)
            
            # ИСПРАВЛЕНИЕ: Восстанавливаем видимость эффектора при отвязке
            effector_mod.show_viewport = True
            effector_mod.show_render = True
            print(f"[DEBUG] Восстановлена видимость эффектора {effector_mod.name} после отвязки")
            
            # Обновляем изменения во view_layer для правильного отображения
            import bpy
            bpy.context.view_layer.update()

        return True

    except Exception as e:
        print(f"[ERROR] unlink_effector_from_cloner: {e}")
        import traceback
        traceback.print_exc()
        return False
        return False


def replace_problematic_nodes_with_effectors(obj, node_group, linked_effectors):
    """
    Заменяет проблемные узлы анти-рекурсии на эффекторы с сохранением связей.

    Args:
        obj: Объект с модификатором
        node_group: Группа узлов клонера
        linked_effectors: Список связанных эффекторов
    """
    nodes = node_group.nodes
    links = node_group.links

    # Находим проблемные узлы
    problematic_nodes = []
    realize_nodes_to_remove = []

    for node in nodes:
        if node.name in ["Anti-Recursion Join Geometry", "Effector_Input"]:
            problematic_nodes.append(node)
            print(f"[DEBUG] Найден проблемный узел для замены: {node.name}")
        elif node.name == "Anti-Recursion Realize":
            # При привязке эффекторов удаляем Realize узел
            realize_nodes_to_remove.append(node)
            print(f"[DEBUG] Найден Realize узел для удаления при привязке эффекторов: {node.name}")

    # Если есть Realize узлы для удаления, добавляем их к проблемным узлам
    if realize_nodes_to_remove:
        print(f"[DEBUG] Добавляем {len(realize_nodes_to_remove)} Realize узлов к проблемным узлам")
        problematic_nodes.extend(realize_nodes_to_remove)

    # Если нет проблемных узлов (включая Realize узлы), используем стандартную логику
    if not problematic_nodes:
        print("[DEBUG] Проблемные узлы не найдены, используем стандартную логику")
        create_standard_effector_chain(obj, node_group, linked_effectors)
        return

    # Заменяем первый проблемный узел на цепочку эффекторов
    target_node = problematic_nodes[0]

    # ВАЖНО: Сохраняем позицию узла ДО его удаления
    pos_x = target_node.location.x
    pos_y = target_node.location.y

    # Сохраняем входящие и исходящие связи проблемного узла
    incoming_links = []
    outgoing_links = []

    for link in links:
        if link.to_node == target_node:
            incoming_links.append((link.from_node, link.from_socket, link.to_socket))
        elif link.from_node == target_node:
            outgoing_links.append((link.from_socket, link.to_node, link.to_socket))

    print(f"[DEBUG] Сохранено {len(incoming_links)} входящих и {len(outgoing_links)} исходящих связей")

    # Удаляем все проблемные узлы
    for node in problematic_nodes:
        try:
            nodes.remove(node)
            print(f"[DEBUG] Удален проблемный узел: {node.name}")
        except:
            print(f"[DEBUG] Узел уже был удален")

    # Создаем цепочку эффекторов на месте проблемного узла
    current_output = None
    first_effector_input = None

    # Импортируем функцию копирования параметров
    from .effector_application import copy_effector_parameters

    for i, effector_name in enumerate(linked_effectors):
        effector_mod = obj.modifiers.get(effector_name)
        if not effector_mod or not effector_mod.node_group:
            print(f"[DEBUG] Пропускаем неверный эффектор: {effector_name}")
            continue

        # Создаем узел эффектора
        effector_node = nodes.new('GeometryNodeGroup')
        effector_node.name = f"Effector_{effector_name}"
        effector_node.node_tree = effector_mod.node_group
        effector_node.location = (pos_x + i * 250, pos_y)

        # Запоминаем первый эффектор для входящих связей
        if first_effector_input is None:
            first_effector_input = effector_node.inputs['Geometry']

        # Подключаем к цепочке
        if current_output is not None:
            links.new(current_output, effector_node.inputs['Geometry'])

        current_output = effector_node.outputs['Geometry']

        # Копируем параметры эффектора
        copy_effector_parameters(effector_mod, effector_node)

        # Отключаем рендер оригинального эффектора
        effector_mod.show_render = False
        effector_mod.show_viewport = True

        print(f"[DEBUG] Создан узел эффектора: {effector_name}")

    # Восстанавливаем входящие связи к первому эффектору
    _restore_incoming_connections(incoming_links, first_effector_input, nodes, links)

    # Восстанавливаем исходящие связи от последнего эффектора
    _restore_outgoing_connections(outgoing_links, current_output, nodes, links)

    # Дополнительная проверка: убеждаемся, что эффектор подключен к Switch узлу
    _ensure_effector_switch_connection(current_output, nodes, links)


def _restore_incoming_connections(incoming_links, first_effector_input, nodes, links):
    """Восстанавливает входящие связи к первому эффектору."""
    input_connected = False
    first_effector_node = None

    # Находим первый созданный эффектор
    for node in nodes:
        if node.name.startswith('Effector_'):
            first_effector_node = node
            break

    for from_node, from_socket, to_socket in incoming_links:
        if first_effector_input and first_effector_node:
            try:
                # ВАЖНО: Проверяем, что не подключаем узел сам к себе
                if from_node == first_effector_node:
                    print(f"[DEBUG] Пропущена связь (самоподключение): {from_node.name} -> сам себе")
                    continue

                # Дополнительная проверка по имени узла (для Noise эффектора)
                if hasattr(from_node, 'name') and hasattr(first_effector_node, 'name'):
                    if from_node.name == first_effector_node.name:
                        print(f"[DEBUG] Пропущена связь (самоподключение по имени): {from_node.name} -> {first_effector_node.name}")
                        continue

                # Подключаем к входу Geometry первого эффектора
                if to_socket.name == 'Geometry' or 'Geometry' in to_socket.name:
                    print(f"[DEBUG] Попытка подключения: {from_node.name}.{from_socket.name} -> {first_effector_node.name}.{first_effector_input.name}")
                    if safe_link_new(links, from_socket, first_effector_input):
                        print(f"[DEBUG] Восстановлена входящая связь: {from_node.name}.{from_socket.name} -> первый эффектор")
                        input_connected = True
                else:
                    print(f"[DEBUG] Пропущена входящая связь (не Geometry): {to_socket.name}")
            except Exception as e:
                print(f"[DEBUG] Не удалось восстановить входящую связь: {e}")

    # Если входящие связи не были восстановлены, ищем Transform Geometry узел
    if not input_connected and first_effector_input:
        print("[DEBUG] Входящие связи не восстановлены, ищем Transform Geometry узел")
        for node in nodes:
            if ('Transform' in node.name or 'Transform' in getattr(node, 'bl_idname', '')) and hasattr(node, 'outputs'):
                for output in node.outputs:
                    if output.name == 'Geometry':
                        try:
                            if safe_link_new(links, output, first_effector_input):
                                print(f"[DEBUG] Подключен {node.name}.{output.name} к первому эффектору")
                                input_connected = True
                                break
                        except Exception as e:
                            print(f"[DEBUG] Не удалось подключить {node.name}: {e}")
                if input_connected:
                    break


def _restore_outgoing_connections(outgoing_links, current_output, nodes, links):
    """Восстанавливает исходящие связи от последнего эффектора."""
    last_effector_node = None

    # Находим последний созданный эффектор
    effector_nodes = [n for n in nodes if n.name.startswith('Effector_')]
    if effector_nodes:
        last_effector_node = effector_nodes[-1]

    for from_socket, to_node, to_socket in outgoing_links:
        if current_output and last_effector_node:
            try:
                # ВАЖНО: Проверяем, что не подключаем узел сам к себе
                if to_node == last_effector_node:
                    print(f"[DEBUG] Пропущена связь (самоподключение): последний эффектор -> сам себе")
                    continue

                # Дополнительная проверка по имени узла (для Noise эффектора)
                if hasattr(to_node, 'name') and hasattr(last_effector_node, 'name'):
                    if to_node.name == last_effector_node.name:
                        print(f"[DEBUG] Пропущена связь (самоподключение по имени): {last_effector_node.name} -> {to_node.name}")
                        continue

                # Подключаем выход последнего эффектора
                if from_socket.name == 'Geometry' or 'Geometry' in from_socket.name:
                    print(f"[DEBUG] Попытка подключения: {last_effector_node.name}.{current_output.name} -> {to_node.name}.{to_socket.name}")
                    if safe_link_new(links, current_output, to_socket):
                        print(f"[DEBUG] Восстановлена исходящая связь: последний эффектор -> {to_node.name}.{to_socket.name}")
                else:
                    print(f"[DEBUG] Пропущена исходящая связь (не Geometry): {from_socket.name}")
            except Exception as e:
                print(f"[DEBUG] Не удалось восстановить исходящую связь: {e}")


def _ensure_effector_switch_connection(current_output, nodes, links):
    """Убеждается, что эффектор подключен к Switch узлу."""
    if current_output:
        # Ищем Switch узел в клонере
        switch_node = None
        for node in nodes:
            if node.name == "Anti-Recursion Switch" or (hasattr(node, 'bl_idname') and 'Switch' in node.bl_idname):
                switch_node = node
                break

        if switch_node:
            # Проверяем, подключен ли эффектор к Switch
            connected_to_switch = False
            for link in links:
                if link.from_node.name.startswith('Effector_') and link.to_node == switch_node:
                    connected_to_switch = True
                    break

            if not connected_to_switch:
                # Пытаемся подключить к False входу Switch (обычно это индекс 1)
                try:
                    if len(switch_node.inputs) > 1:
                        links.new(current_output, switch_node.inputs[1])  # False вход
                        print(f"[DEBUG] Дополнительно подключен эффектор к Switch узлу (False вход)")
                    elif len(switch_node.inputs) > 0:
                        links.new(current_output, switch_node.inputs[0])  # Первый доступный вход
                        print(f"[DEBUG] Дополнительно подключен эффектор к Switch узлу (первый вход)")
                except Exception as e:
                    print(f"[DEBUG] Не удалось дополнительно подключить к Switch: {e}")


def create_standard_effector_chain(obj, node_group, linked_effectors):
    """
    Создает стандартную цепочку эффекторов (старая логика).

    Args:
        obj: Объект с модификатором
        node_group: Группа узлов клонера
        linked_effectors: Список связанных эффекторов
    """
    nodes = node_group.nodes
    links = node_group.links

    # Удаляем старые узлы эффекторов
    old_effector_nodes = [n for n in nodes if n.name.startswith('Effector_')]
    for node in old_effector_nodes:
        nodes.remove(node)
        print(f"[DEBUG] Удален старый узел эффектора: {node.name}")

    # Находим анти-рекурсию (старую или новую структуру)
    anti_recursion_switch = None
    final_realize_switch = None
    for node in nodes:
        if node.name == "Anti-Recursion Switch":
            anti_recursion_switch = node
        elif node.name == "Final Realize Switch":
            final_realize_switch = node

    # Используем Final Realize Switch если он есть, иначе Anti-Recursion Switch
    target_switch = final_realize_switch if final_realize_switch else anti_recursion_switch

    # Находим точку подключения эффекторов
    effector_insertion_point = find_effector_insertion_point(node_group, target_switch)

    if not effector_insertion_point:
        print("[DEBUG] Не найдена точка подключения эффекторов")
        return

    source_node, source_output, target_node, target_input = effector_insertion_point

    # Создаем цепочку эффекторов
    current_output = source_output
    pos_x = source_node.location.x + 200
    pos_y = source_node.location.y

    print(f"[DEBUG] Создаем стандартную цепочку из {len(linked_effectors)} эффекторов")

    # Импортируем функцию копирования параметров
    from .effector_application import copy_effector_parameters

    for i, effector_name in enumerate(linked_effectors):
        effector_mod = obj.modifiers.get(effector_name)
        if not effector_mod or not effector_mod.node_group:
            print(f"[DEBUG] Пропускаем неверный эффектор: {effector_name}")
            continue

        # Создаем узел эффектора
        effector_node = nodes.new('GeometryNodeGroup')
        effector_node.name = f"Effector_{effector_name}"
        effector_node.node_tree = effector_mod.node_group
        effector_node.location = (pos_x + i * 250, pos_y)

        # Подключаем к цепочке
        if safe_link_new(links, current_output, effector_node.inputs['Geometry']):
            current_output = effector_node.outputs['Geometry']

        # Копируем параметры эффектора
        copy_effector_parameters(effector_mod, effector_node)

        # Отключаем рендер оригинального эффектора
        effector_mod.show_render = False
        effector_mod.show_viewport = True

        print(f"[DEBUG] Создан узел эффектора: {effector_name}")

    # Подключаем последний эффектор к целевому узлу
    if target_node and target_input:
        # Удаляем старую связь с целевым узлом
        links_to_remove = [link for link in links
                          if link.to_node == target_node and link.to_socket == target_input]
        for link in links_to_remove:
            links.remove(link)

        # Подключаем цепочку эффекторов
        safe_link_new(links, current_output, target_input)
        print(f"[DEBUG] Подключена цепочка эффекторов к {target_node.name}")

        # Если у нас есть анти-рекурсия, убеждаемся что True путь тоже настроен правильно
        if target_switch and target_node == target_switch:
            setup_anti_recursion_true_path(node_group, target_switch, current_output)


def find_effector_insertion_point(node_group, anti_recursion_switch):
    """
    Находит оптимальную точку для вставки эффекторов.
    ИСПРАВЛЕНО: Учитывает правильную структуру анти-рекурсии и новую структуру грид клонера.

    Args:
        node_group: Группа узлов
        anti_recursion_switch: Узел Switch для анти-рекурсии (может быть None)

    Returns:
        tuple: (source_node, source_output, target_node, target_input) или None
    """
    nodes = node_group.nodes
    links = node_group.links

    # Находим выходной узел
    group_output = None
    for node in nodes:
        if node.type == 'GROUP_OUTPUT':
            group_output = node
            break

    if not group_output:
        return None

    # Ищем узлы Switch для новой структуры анти-рекурсии (как в линейном и круговом клонерах)
    final_realize_switch = None
    switch_realize_mode = None

    for node in nodes:
        if node.name == "Final Realize Switch":
            final_realize_switch = node
        elif node.name == "Switch Realize Mode":
            switch_realize_mode = node

    # Если есть новая структура анти-рекурсии (Final Realize Switch)
    if final_realize_switch:
        print("[DEBUG] Поиск точки вставки для новой структуры анти-рекурсии (Final Realize Switch)")

        # Ищем узел, подключенный к False входу Final Realize Switch (прямой путь)
        source_node = None
        source_output = None

        for link in links:
            if (link.to_node == final_realize_switch and
                link.to_socket == final_realize_switch.inputs[False]):
                source_node = link.from_node
                source_output = link.from_socket
                break

        if source_node and source_output:
            return (source_node, source_output,
                   final_realize_switch, final_realize_switch.inputs[False])
        else:
            print("[DEBUG] Не найден источник для False входа Final Realize Switch")

    # Если есть старая структура анти-рекурсии (Anti-Recursion Switch)
    elif anti_recursion_switch:
        print("[DEBUG] Поиск точки вставки для старой структуры анти-рекурсии")

        # Ищем узел, подключенный к False входу Switch (прямой путь)
        source_node = None
        source_output = None

        for link in links:
            if (link.to_node == anti_recursion_switch and
                link.to_socket == anti_recursion_switch.inputs[False]):
                source_node = link.from_node
                source_output = link.from_socket
                break

        if source_node and source_output:
            return (source_node, source_output,
                   anti_recursion_switch, anti_recursion_switch.inputs[False])
        else:
            print("[DEBUG] Не найден источник для False входа анти-рекурсии")

    # Если нет узла анти-рекурсии, подключаем эффекторы перед выходом
    print("[DEBUG] Поиск точки вставки для системы без анти-рекурсии")

    # Ищем узел, подключенный к выходу
    source_node = None
    source_output = None

    for link in links:
        if link.to_node == group_output and link.to_socket.name == 'Geometry':
            source_node = link.from_node
            source_output = link.from_socket
            break

    if source_node and source_output:
        return (source_node, source_output,
               group_output, group_output.inputs['Geometry'])
    else:
        print("[DEBUG] Не найден источник для выходного узла")

        # Пытаемся найти любой подходящий узел
        for node in nodes:
            if (node != group_output and node.type != 'GROUP_INPUT' and
                not node.name.startswith('Effector_')):
                for output in node.outputs:
                    if output.name in ['Geometry', 'Instances']:
                        return (node, output, group_output, group_output.inputs['Geometry'])
        return None


def setup_anti_recursion_true_path(node_group, anti_recursion_switch, effector_chain_output):
    """
    Настраивает True путь для узла анти-рекурсии после подключения эффекторов.
    ИСПРАВЛЕНО: Учитывает существующие Realize узлы и новую структуру.

    Args:
        node_group: Группа узлов
        anti_recursion_switch: Узел Switch для анти-рекурсии
        effector_chain_output: Выходной сокет цепочки эффекторов
    """
    nodes = node_group.nodes
    links = node_group.links

    # Проверяем, какой тип Switch узла у нас есть
    is_final_realize_switch = anti_recursion_switch.name == "Final Realize Switch"

    if is_final_realize_switch:
        # Для новой структуры (Final Realize Switch) ищем Final Realize Instances
        existing_realize = None
        for node in nodes:
            if node.name == "Final Realize Instances":
                existing_realize = node
                break

        if existing_realize:
            # Обновляем вход Final Realize Instances
            links_to_remove = []
            for link in links:
                if link.to_node == existing_realize and link.to_socket.name == 'Geometry':
                    links_to_remove.append(link)

            for link in links_to_remove:
                links.remove(link)

            # Подключаем цепочку эффекторов к существующему Final Realize узлу
            links.new(effector_chain_output, existing_realize.inputs['Geometry'])
            print("[DEBUG] Обновлен существующий Final Realize узел для True пути")
        else:
            print("[DEBUG] Final Realize Instances узел не найден")
    else:
        # Для старой структуры (Anti-Recursion Switch) ищем Anti-Recursion Realize
        existing_realize = None
        for node in nodes:
            if node.name == "Anti-Recursion Realize":
                existing_realize = node
                break

        if existing_realize:
            # Используем существующий Realize узел
            # Обновляем его вход
            links_to_remove = []
            for link in links:
                if link.to_node == existing_realize and link.to_socket.name == 'Geometry':
                    links_to_remove.append(link)

            for link in links_to_remove:
                links.remove(link)

            # Подключаем цепочку эффекторов к существующему Realize узлу
            links.new(effector_chain_output, existing_realize.inputs['Geometry'])
            print("[DEBUG] Обновлен существующий Anti-Recursion Realize узел для True пути")
        else:
            # Проверяем, подключен ли уже True вход
            true_input_connected = False
            for link in links:
                if (link.to_node == anti_recursion_switch and
                    link.to_socket == anti_recursion_switch.inputs[True]):
                    true_input_connected = True
                    break

            if not true_input_connected:
                # Создаем новый Realize узел для True пути
                realize_true = nodes.new('GeometryNodeRealizeInstances')
                realize_true.name = "Effector Chain Realize"
                realize_true.location = (anti_recursion_switch.location.x - 150,
                                       anti_recursion_switch.location.y + 150)

                # Подключаем цепочку эффекторов к Realize узлу и далее к True входу
                links.new(effector_chain_output, realize_true.inputs['Geometry'])
                links.new(realize_true.outputs['Geometry'], anti_recursion_switch.inputs[True])

                print("[DEBUG] Создан новый Realize узел для True пути анти-рекурсии")
