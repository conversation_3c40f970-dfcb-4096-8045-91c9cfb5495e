"""
Anti-recursion utilities for cloners.

This module provides utilities for managing anti-recursion settings in cloners,
preventing infinite loops and ensuring proper cloner behavior.
"""

# Import only available functionality from anti_recursion_utils
from .anti_recursion_utils import (
    update_anti_recursion_for_all_cloners,
    update_anti_recursion_callback,
    update_stacked_modifiers_callback
)

# Public API
__all__ = [
    'update_anti_recursion_for_all_cloners',
    'update_anti_recursion_callback',
    'update_stacked_modifiers_callback'
]

def register():
    """Register anti-recursion components"""
    pass

def unregister():
    """Unregister anti-recursion components"""
    pass
