"""
Универсальная система установки параметров для всех типов компонентов.

Заменяет множественные функции setup_*_params единой системой,
работающей через реестр компонентов.

UPDATED: Клонеры и некоторые эффекторы теперь используют новую унифицированную систему параметров.
Только часть эффекторов и поля пока используют legacy систему до их полной миграции.
"""

from ....core.registry import component_registry
from ....core.utils.configuration.config_utils import apply_cloner_config, apply_field_config
from ....core.parameters import get_component_parameters, set_values_from_parameters

def setup_component_params(modifier, component_type, component_id):
    """
    Универсальная функция установки параметров для любого компонента.

    Args:
        modifier: Модификатор компонента
        component_type: Тип компонента ('CLONER', 'EFFECTOR', 'FIELD')
        component_id: ID компонента (например, 'GRID', 'RANDOM', 'SPHERE')
    """
    try:
        # Пытаемся применить конфигурацию из JSON файла
        config_applied = False

        if component_type == 'CLONER':
            config_applied = apply_cloner_config(modifier, component_id)
        elif component_type == 'EFFECTOR':
            config_applied = apply_effector_config(modifier, component_id)
        elif component_type == 'FIELD':
            config_applied = apply_field_config(modifier, component_id)

        if config_applied:
            print(f"Applied {component_id} {component_type.lower()} config from JSON file")
            return True

        # Если конфигурация не применилась, используем значения по умолчанию из системы параметров
        print(f"Using default {component_id} {component_type.lower()} parameters")
        
        # Используем новую систему параметров для клонеров и поддерживаемых эффекторов
        if component_type == 'CLONER' or (component_type == 'EFFECTOR' and component_id in ['RANDOM']):
            return _apply_params_from_parameter_system(modifier, component_type, component_id)
        else:
            # Для остальных компонентов используем старый подход
            return _apply_default_params(modifier, component_type, component_id)

    except Exception as e:
        print(f"Ошибка при установке параметров {component_id} {component_type}: {e}")
        return False

def setup_cloner_params(modifier, cloner_type):
    """
    Устанавливает параметры для клонера указанного типа.

    UPDATED: Теперь пытается использовать новую систему параметров первой,
    с fallback на старую систему для обратной совместимости.

    Args:
        modifier: Модификатор клонера
        cloner_type: Тип клонера (например, 'GRID', 'LINEAR', 'CIRCLE', 'SPIRAL', 'STACKED_GRID', etc.)
    """
    # Для стековых клонеров убираем префикс STACKED_ для получения базового типа
    base_type = cloner_type
    if cloner_type.startswith('STACKED_'):
        base_type = cloner_type.replace('STACKED_', '')
        print(f"Стековый клонер {cloner_type} -> базовый тип {base_type}")
    elif cloner_type.startswith('COLLECTION_'):
        base_type = cloner_type.replace('COLLECTION_', '')
        print(f"Коллекционный клонер {cloner_type} -> базовый тип {base_type}")

    # NEW: Use new parameter system for all cloners
    try:
        from ....core.parameters import get_component_parameters, set_values_from_parameters

        component_params = get_component_parameters('CLONER', base_type)
        if component_params:
            success = set_values_from_parameters(modifier, component_params)
            if success:
                print(f"✅ Applied {base_type} cloner parameters using new parameter system")
                return True
            else:
                print(f"❌ Failed to set {base_type} cloner parameters using new parameter system")
                return False
        else:
            print(f"❌ {base_type} cloner parameters not found in new system")
            # For cloners not yet migrated, fall back to component system
            if base_type not in ['CIRCLE', 'GRID', 'LINEAR', 'SPIRAL']:
                print(f"Using component parameter system for {base_type} cloner")
                return setup_component_params(modifier, 'CLONER', base_type)
            else:
                print(f"ERROR: {base_type} cloner should be in new system but parameters not found!")
                return False
    except Exception as e:
        print(f"❌ Error using new parameter system for {base_type}: {e}")
        return False

def setup_effector_params(modifier, effector_type):
    """
    Устанавливает параметры для эффектора указанного типа.

    Args:
        modifier: Модификатор эффектора
        effector_type: Тип эффектора (например, 'RANDOM', 'NOISE')
    """
    return setup_component_params(modifier, 'EFFECTOR', effector_type)

def setup_field_params(modifier, field_type):
    """
    Устанавливает параметры для поля указанного типа.

    Args:
        modifier: Модификатор поля
        field_type: Тип поля (например, 'SPHERE')
    """
    return setup_component_params(modifier, 'FIELD', field_type)

def _apply_params_from_parameter_system(modifier, component_type, component_id):
    """Применяет значения параметров из новой унифицированной системы параметров."""
    try:
        # Получаем параметры из системы параметров
        parameter_set = get_component_parameters(component_type, component_id)
        if not parameter_set:
            print(f"❌ Parameter set not found for {component_type}.{component_id}")
            return False
            
        # Устанавливаем значения из определений параметров
        success = set_values_from_parameters(modifier, parameter_set)
        if success:
            print(f"✅ Applied default values from parameter system for {component_type}.{component_id}")
        else:
            print(f"⚠️ Only partial success applying default values for {component_type}.{component_id}")
            
        return success
        
    except Exception as e:
        print(f"❌ Error applying values from parameter system: {e}")
        return False

def _apply_default_params(modifier, component_type, component_id):
    """Применяет значения параметров по умолчанию для компонента указанного типа используя устаревший метод."""
    try:
        # Определяем для каждого компонента указанного типа параметры по умолчанию
        if component_type == 'CLONER':
            # CLONER параметры устанавливаются через новую систему параметров
            print(f"⚠️ _apply_default_params called for CLONER {component_id} - should use new parameter system")
            return True
        elif component_type == 'EFFECTOR' and component_id in ['RANDOM']:
            # Эти эффекторы должны использовать новую систему параметров
            print(f"⚠️ _apply_default_params called for EFFECTOR {component_id} - should use new parameter system")
            return _apply_params_from_parameter_system(modifier, component_type, component_id)
        elif component_type == 'EFFECTOR':
            # Другие EFFECTOR параметры пока настраиваются через effector_params.py
            print(f"⚠️ _apply_default_params called for EFFECTOR {component_id} - using dedicated effector parameter setup")
            return True
        elif component_type == 'FIELD':
            # FIELD параметры настраиваются через field_params.py
            print(f"⚠️ _apply_default_params called for FIELD {component_id} - using dedicated field parameter setup")
            return True

        return True

    except Exception as e:
        print(f"Ошибка при применении параметров по умолчанию: {e}")
        return False

# _apply_cloner_defaults() удалена - используется автоматическая настройка через set_values_from_parameters()
# _apply_effector_defaults() удалена - используется настройка через effector_params.py
# _apply_field_defaults() удалена - используется настройка через field_params.py





def setup_random_effector_params(modifier):
    """Настройка параметров для Random эффектора"""
    # Используем новую систему параметров для Random эффектора
    parameter_set = get_component_parameters('EFFECTOR', 'RANDOM')
    if parameter_set:
        success = set_values_from_parameters(modifier, parameter_set)
        print(f"{'✅ Applied' if success else '⚠️ Partially applied'} parameter values for Random Effector using new parameter system")
        return success
    else:
        print(f"❌ Parameter set not found for EFFECTOR.RANDOM - falling back to legacy method")
        # Если параметры не найдены, пробуем применить старый метод
        return setup_effector_params(modifier, "RANDOM")

def setup_noise_effector_params(modifier):
    """Настройка параметров для Noise эффектора"""
    return setup_effector_params(modifier, "NOISE")

def setup_sphere_field_params(modifier):
    """Настройка параметров для Sphere поля"""
    return setup_field_params(modifier, "SPHERE")
