"""
Трансформации для системы создания клонеров.

ПЕРЕНЕСЕНО из common/transforms/ на верхний уровень для лучшей доступности.

Содержит унифицированные трансформации, которые используются
всеми типами клонеров (object, collection, stacked).

Модули:
- random_transforms: Унифицированные случайные трансформации
- instance_transforms: Унифицированные instance трансформации  
- global_transform: Унифицированные глобальные трансформации
- complex_transforms: Комплексные комбинированные трансформации
- anti_recursion: Унифицированная анти-рекурсия (объединены 3 модуля)
"""

# Импортируем все трансформации для удобства
from .random_transforms import (
    create_random_transforms,
    apply_random_transforms,
    setup_random_position,
    setup_random_rotation,
    setup_random_scale,
    apply_stacked_random_transforms_complete,
    combine_base_and_random_rotation,
    combine_base_and_random_scale
)

# Импортируем глобальные трансформации
from .global_transform import (
    apply_global_transforms,
    create_global_transform_node,
    connect_global_inputs,
    apply_global_transforms_to_geometry,
    apply_global_transforms_to_instances
)

# Импортируем instance трансформации
from .instance_transforms import (
    apply_instance_rotation,
    apply_instance_scale,
    apply_circle_face_center_rotation,
    apply_instance_transforms,
    create_instance_transforms
)

# Импортируем комплексные трансформации
from .complex_transforms import (
    apply_instance_and_random_transforms,
    apply_linear_random_transforms,
    apply_unified_transforms
)

# Импортируем анти-рекурсию (ОБЪЕДИНЕНО: включает функции из object_anti_recursion.py)
from .anti_recursion import (
    # Основные функции создания узлов
    create_anti_recursion,
    create_stacked_anti_recursion,
    create_object_anti_recursion,
    create_collection_anti_recursion,
    # Функции из object_anti_recursion.py (для builders)
    setup_anti_recursion_interface,
    create_effector_input_node,
    create_anti_recursion_nodes,
    get_anti_recursion_setting
)

__all__ = [
    # From random_transforms
    'create_random_transforms',
    'apply_random_transforms',
    'setup_random_position',
    'setup_random_rotation',
    'setup_random_scale',
    'apply_stacked_random_transforms_complete',
    'combine_base_and_random_rotation',
    'combine_base_and_random_scale',
    # From global_transform
    'apply_global_transforms',
    'create_global_transform_node',
    'connect_global_inputs',
    'apply_global_transforms_to_geometry',
    'apply_global_transforms_to_instances',
    # From instance_transforms
    'apply_instance_rotation',
    'apply_instance_scale',
    'apply_circle_face_center_rotation',
    'apply_instance_transforms',
    'create_instance_transforms',
    # From complex_transforms
    'apply_instance_and_random_transforms',
    'apply_linear_random_transforms',
    'apply_unified_transforms',
    # From anti_recursion (ОБЪЕДИНЕНО: включает функции из object_anti_recursion.py)
    'create_anti_recursion',
    'create_stacked_anti_recursion',
    'create_object_anti_recursion',
    'create_collection_anti_recursion',
    # Функции из object_anti_recursion.py (для builders)
    'setup_anti_recursion_interface',
    'create_effector_input_node',
    'create_anti_recursion_nodes',
    'get_anti_recursion_setting'
]

def register():
    """Register transform components"""
    pass

def unregister():
    """Unregister transform components"""
    pass
