"""
Модуль отображения эффекторов.

Содержит функции для отображения UI эффекторов и их полей.

UPDATED: Добавлена поддержка автоматической генерации UI на основе параметров эффекторов.
"""

import bpy
from ...common.ui_utils import is_element_expanded
from ...common.effector_drawing.effector_controls import draw_effector_ui as original_draw_effector_ui
from ...common.effector_drawing.effector_controls import draw_effector_header
from ...common.ui_constants import (
    ICON_ADD, ICON_REMOVE
)
from ....core.parameters import get_component_parameters, generate_ui_from_parameters
from ....core.registry import component_registry


def draw_effector_ui(context, layout, obj, mod):
    """
    Отображает UI для эффектора.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
        obj: Объект, к которому прикреплен эффектор
        mod: Модификатор эффектора
    """
    box = layout.box() # Основной контейнер для эффектора

    # --- Определение component_id эффектора ---
    effector_id = "UNKNOWN"
    if mod.node_group:
        if mod.node_group.get("component_id"): # Предпочтительный способ
             effector_id = mod.node_group.get("component_id")
        else: # Fallback по имени, если component_id не сохранен в группе
            for comp_id, meta in component_registry._effector_meta.items():
                if meta.display_name.replace(" ", "") in mod.node_group.name:
                    effector_id = comp_id
                    break
    # --- Конец определения component_id ---

    # --- Заголовок эффектора ---
    draw_effector_header(context, box, obj, mod) # Рисуем заголовок
    
    expanded = is_element_expanded(context, obj.name, mod.name, "effector_expanded_states")
    if not expanded:
        return
    # --- Конец заголовка ---

    if mod.node_group and hasattr(mod.node_group, 'interface'):
        # --- Информация о связях с клонерами ---
        # Получаем связанные клонеры
        linked_cloners = []
        for obj_in_scene in context.scene.objects:
            for m in obj_in_scene.modifiers:
                if m.type == 'NODES' and m.node_group and "Cloner" in m.node_group.name:
                    # Проверяем, привязан ли эффектор к этому клонеру
                    if m.node_group.get("linked_effectors") and mod.name in m.node_group.get("linked_effectors"):
                        linked_cloners.append((obj_in_scene, m))

        # Отображение информации о связях
        if linked_cloners:
            link_box = box.box()
            link_row = link_box.row()
            link_row.label(text="Linked to cloners:", icon='LINKED')
            
            for linked_obj, linked_mod in linked_cloners:
                row = link_box.row()
                row.label(text=f"{linked_obj.name} - {linked_mod.name}")
                
        # Панель автоматической привязки к клонерам
        # Показываем если это выбранный эффектор и на объекте есть клонеры (обычные или стековые)
        # Проверяем наличие клонеров на объекте
        cloners_found = False
        for m in obj.modifiers:
            if m.type == 'NODES' and m.node_group and (
                # Обычные клонеры
                "Cloner" in m.node_group.name or 
                "_Cloner" in m.name or 
                # Стековые клонеры
                m.get("is_stacked_cloner") or 
                (m.node_group and m.node_group.get("is_stacked_cloner"))
            ):
                cloners_found = True
                break
        
        if cloners_found:
            auto_link_box = box.box()
            auto_link_row = auto_link_box.row()
            op = auto_link_row.operator("object.auto_link_effector", text="Auto-link", icon='LINKED')
            op.effector_name = mod.name
            
        # Добавляем разделитель перед параметрами
        box.separator()
        
        # --- АВТОМАТИЧЕСКАЯ ГЕНЕРАЦИЯ UI ---
        effector_params = get_component_parameters('EFFECTOR', effector_id)
        if effector_params:
            generate_ui_from_parameters(box, mod, effector_params)
            print(f"✅ Generated UI for {effector_id} effector using new parameter system")
        else:
            # Если параметры не найдены, используем стандартный UI
            print(f"⚠️ Parameters not found for {effector_id} - using legacy UI")
            # Fallback - только если параметры не найдены
            box.label(text=f"Legacy UI for {effector_id}")
            
            # Базовые настройки
            base_box = box.box()
            base_box.label(text="Основные настройки")
            col = base_box.column(align=True)
            
            # Пытаемся добавить основные параметры, если они есть
            try:
                col.prop(mod, '["Enable"]', text="Включить")
            except:
                pass
                
            try:
                col.prop(mod, '["Strength"]', text="Сила", slider=True)
            except:
                pass
                
            # Трансформации
            transform_box = box.box()
            transform_box.label(text="Трансформации")
            col = transform_box.column(align=True)
            
            try:
                col.prop(mod, '["Position"]', text="Позиция")
            except:
                pass
                
            try:
                col.prop(mod, '["Rotation"]', text="Вращение")
            except:
                pass
                
            try:
                col.prop(mod, '["Scale"]', text="Масштаб")
            except:
                pass
            
            # Специфичные для RandomEffector
            if "random" in effector_id.lower():
                random_box = box.box()
                random_box.label(text="Случайные настройки")
                col = random_box.column(align=True)
                
                try:
                    col.prop(mod, '["Uniform Scale"]', text="Равномерный масштаб")
                except:
                    pass
                    
                try:
                    col.prop(mod, '["Seed"]', text="Зерно")
                except:
                    pass
                
            # Для эффекторов типа Random, добавляем настройки полей
            if "random" in effector_id.lower() or "noise" in effector_id.lower():
                # Поля
                field_box = box.box()
                field_box.label(text="Поле")
                
                try:
                    row = field_box.row(align=True)
                    row.prop(mod, '["Use Field"]', text="Использовать поле")
                    
                    # Если поле используется, показываем дополнительные настройки
                    if mod.get("Use Field", False):
                        sub = field_box.column(align=True)
                        sub.prop(mod, '["Field"]', text="Сила поля", slider=True)
                except:
                    field_box.label(text="Настройки полей недоступны")
                    pass


def draw_field_ui(context, box, obj, mod, socket):
    """
    Отрисовка UI для поля эффектора.

    Args:
        context: Контекст Blender
        box: Элемент UI для добавления интерфейса
        obj: Объект, к которому прикреплен эффектор
        mod: Модификатор эффектора
        socket: Сокет группы узлов для поля
    """
    row = box.row(align=True)
    row.use_property_split = True
    row.use_property_decorate = False

    # Определяем, использует ли эффектор поле
    use_field = mod.get("Use Field", False)

    # Если есть Use Field, включаем/отключаем его
    if "Use Field" in [s.name for s in mod.node_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']:
        row.prop(mod, f'["{socket.identifier}"]', text=socket.name)

        # Чекбокс для Use Field
        for s in mod.node_group.interface.items_tree:
            if s.item_type == 'SOCKET' and s.in_out == 'INPUT' and s.name == 'Use Field':
                row.prop(mod, f'["{s.identifier}"]', text="")
                use_field = mod[s.identifier]
                break
    else:
        row.prop(mod, f'["{socket.identifier}"]', text=socket.name)

    # Если поле используется, предлагаем возможность отключить его
    if use_field:
        remove_row = box.row()
        op = remove_row.operator("object.effector_remove_field", text="Disconnect Field", icon=ICON_REMOVE)
        op.effector_name = mod.name
    else:
        # Если поле не используется, предлагаем возможность подключить его
        add_row = box.row()
        op = add_row.operator("object.effector_add_field", text="Connect Field", icon=ICON_ADD)
        op.effector_name = mod.name
