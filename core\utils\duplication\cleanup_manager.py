"""
Cleanup and restoration utilities for cloner duplicates.

This module handles restoration of original objects and cleanup of
duplicate objects and collections.
"""

import bpy
from typing import List, Optional
from .chain_tracker import cloner_hierarchy_map


def restore_original_object(duplicate_obj):
    """
    Restores original object visibility and deletes duplicate.

    Args:
        duplicate_obj (bpy.types.Object): Duplicate object

    Returns:
        bool: True if successful, False in case of error
    """
    # Check that this is actually a duplicate
    if "original_obj" not in duplicate_obj:
        return False

    # Find original object
    original_name = duplicate_obj["original_obj"]
    if original_name not in bpy.data.objects:
        return False

    original_obj = bpy.data.objects[original_name]

    # Clean up hierarchy chain tracking
    if duplicate_obj.name in cloner_hierarchy_map:
        del cloner_hierarchy_map[duplicate_obj.name]

    # Remove this duplicate from any hierarchy chains
    for obj_name, chain in cloner_hierarchy_map.items():
        updated_chain = []
        for entry in chain:
            if entry["duplicate"] != duplicate_obj.name:
                updated_chain.append(entry)

        if len(updated_chain) != len(chain):
            cloner_hierarchy_map[obj_name] = updated_chain

    # Restore visibility
    if "original_hide_viewport" in duplicate_obj:
        original_obj.hide_viewport = duplicate_obj["original_hide_viewport"]
    else:
        original_obj.hide_viewport = False

    if "original_hide_render" in duplicate_obj:
        original_obj.hide_render = duplicate_obj["original_hide_render"]

    # Remove duplicate from cache
    from . import mesh_duplicates_cache
    keys_to_remove = []
    for key, (obj, coll) in mesh_duplicates_cache.items():
        if obj == duplicate_obj:
            keys_to_remove.append(key)

    for key in keys_to_remove:
        mesh_duplicates_cache.pop(key, None)

    # Remove duplicate data
    if duplicate_obj.type == 'MESH' and duplicate_obj.data.users == 1:
        mesh_data = duplicate_obj.data
        bpy.data.objects.remove(duplicate_obj)
        bpy.data.meshes.remove(mesh_data)
    elif duplicate_obj.type == 'CURVE' and duplicate_obj.data.users == 1:
        curve_data = duplicate_obj.data
        bpy.data.objects.remove(duplicate_obj)
        bpy.data.curves.remove(curve_data)
    elif duplicate_obj.type == 'FONT' and duplicate_obj.data.users == 1:
        text_data = duplicate_obj.data
        bpy.data.objects.remove(duplicate_obj)
        bpy.data.curves.remove(text_data)
    else:
        bpy.data.objects.remove(duplicate_obj)

    return True


def cleanup_empty_cloner_collections():
    """
    Deletes empty cloner collections.

    Returns:
        int: Number of deleted collections
    """
    count = 0

    # Find all cloner collections
    cloner_collections = [c for c in bpy.data.collections
                          if c.name.startswith("cloner") or c.name.startswith("cloner_")]

    # Delete empty collections
    for collection in cloner_collections:
        if len(collection.objects) == 0:
            try:
                bpy.data.collections.remove(collection)
                count += 1
            except Exception as e:
                print(f"Failed to delete collection {collection.name}: {e}")

    return count


def cleanup_orphaned_duplicates():
    """
    Removes duplicate objects whose original objects no longer exist.

    Returns:
        int: Number of cleaned up duplicates
    """
    count = 0
    duplicates_to_remove = []

    # Используем пакетную обработку для оптимизации
    from ..optimization import get_objects_needing_cleanup

    cleanup_data = get_objects_needing_cleanup()
    print(f"[BATCH] Найдено {len(cleanup_data)} объектов для очистки")

    # Обрабатываем только объекты, которые действительно нуждаются в очистке
    for obj_data in cleanup_data:
        obj_name = obj_data['obj_name']
        try:
            obj = bpy.data.objects[obj_name]
            for cleanup_item in obj_data['cleanup']:
                if cleanup_item['type'] == 'orphaned_duplicate':
                    duplicates_to_remove.append(obj)
        except KeyError:
            continue  # Объект был удален

    # Remove orphaned duplicates
    for duplicate_obj in duplicates_to_remove:
        try:
            restore_original_object(duplicate_obj)
            count += 1
        except Exception as e:
            print(f"Failed to cleanup duplicate {duplicate_obj.name}: {e}")

    return count


def cleanup_unused_cache_entries():
    """
    Removes cache entries for objects that no longer exist.

    Returns:
        int: Number of cleaned up cache entries
    """
    from . import mesh_duplicates_cache
    count = 0
    keys_to_remove = []

    for key, (obj, coll) in mesh_duplicates_cache.items():
        if not obj or obj.name not in bpy.data.objects:
            keys_to_remove.append(key)

    for key in keys_to_remove:
        mesh_duplicates_cache.pop(key, None)
        count += 1

    return count


def cleanup_all_cloner_data():
    """
    Performs a complete cleanup of all cloner-related data.

    Returns:
        dict: Statistics about the cleanup operation
    """
    stats = {
        "orphaned_duplicates": 0,
        "empty_collections": 0,
        "cache_entries": 0,
        "hierarchy_entries": 0
    }

    # Cleanup orphaned duplicates
    stats["orphaned_duplicates"] = cleanup_orphaned_duplicates()

    # Cleanup empty collections
    stats["empty_collections"] = cleanup_empty_cloner_collections()

    # Cleanup cache entries
    stats["cache_entries"] = cleanup_unused_cache_entries()

    # Cleanup hierarchy map entries for non-existent objects
    keys_to_remove = []
    for obj_name in cloner_hierarchy_map.keys():
        if obj_name not in bpy.data.objects:
            keys_to_remove.append(obj_name)

    for key in keys_to_remove:
        del cloner_hierarchy_map[key]
        stats["hierarchy_entries"] += 1

    return stats


def restore_all_original_objects():
    """
    Restores all original objects by removing their duplicates.

    Returns:
        int: Number of restored objects
    """
    count = 0
    duplicates_to_restore = []

    # Find all duplicate objects
    for obj in bpy.data.objects:
        if "original_obj" in obj:
            duplicates_to_restore.append(obj)

    # Restore all duplicates
    for duplicate_obj in duplicates_to_restore:
        try:
            if restore_original_object(duplicate_obj):
                count += 1
        except Exception as e:
            print(f"Failed to restore original for {duplicate_obj.name}: {e}")

    return count


def register():
    """Register cleanup manager components"""
    pass


def unregister():
    """Unregister cleanup manager components"""
    pass
