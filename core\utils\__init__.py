"""
Utility functions for Advanced Cloners addon.

This module provides organized utilities grouped by functionality:
- anti_recursion: Anti-recursion management for cloners
- configuration: Configuration and global settings
- duplication: Object duplication and chain tracking
- effector_management: Effector application and management
- event_handling: Event handlers for cloner system
- node_operations: Geometry Nodes operations
- services: Core service utilities
"""

# Import from organized sub-modules
from . import anti_recursion
from . import configuration
from . import duplication
from . import effector_management
from . import event_handling
from . import node_operations
from . import optimization
from . import services
from . import systems

# Основные функции для прямого использования
from .anti_recursion import (
    update_anti_recursion_for_all_cloners,
    update_anti_recursion_callback
)
from .duplication import (
    get_or_create_duplicate_for_cloner,
    create_cloner_collection
)
from .effector_management import (
    update_cloner_with_effectors,
    apply_effector_to_cloner,
    link_effector_to_cloner,
    unlink_effector_from_cloner
)
from .node_operations import (
    find_socket_by_name,
    connect_sockets
)
from .services import (
    force_update_cloners
)


# Public API
__all__ = [
    # Sub-modules
    'anti_recursion',
    'configuration',
    'duplication',
    'effector_management',
    'event_handling',
    'node_operations',
    'services',
    'optimization',
    'systems',

    # Основные функции
    'update_anti_recursion_for_all_cloners',
    'update_anti_recursion_callback',
    'get_or_create_duplicate_for_cloner',
    'create_cloner_collection',
    'update_cloner_with_effectors',
    'apply_effector_to_cloner',
    'find_socket_by_name',
    'connect_sockets',
    'force_update_cloners'
]

def register():
    """Register all utility components"""
    anti_recursion.register()
    # cloner_operations.register()  # Удалено - пустая функция
    configuration.register()
    duplication.register()
    effector_management.register()
    event_handling.register()
    node_operations.register()
    # property_management.register()  # REMOVED - unused system
    services.register()

def unregister():
    """Unregister all utility components"""
    services.unregister()
    # property_management.unregister()  # REMOVED - unused system
    node_operations.unregister()
    event_handling.unregister()
    effector_management.unregister()
    duplication.unregister()
    configuration.unregister()
    # cloner_operations.unregister()  # Удалено - пустая функция
    anti_recursion.unregister()