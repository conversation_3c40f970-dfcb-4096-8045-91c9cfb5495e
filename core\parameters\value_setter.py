"""
Automatic value setter for component parameters.

This module provides functionality to automatically set parameter values
on Blender modifiers based on parameter definitions, eliminating the need
for manual value setting in parameter_setup modules.
"""

import bpy
from typing import Dict, Any, Optional, List
from .parameter_definition import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet
)
from ..utils.node_operations.node_utils import find_socket_by_name


class ValueSetter:
    """
    Sets parameter values on Blender modifiers automatically from parameter definitions.

    This class takes parameter definitions and applies their default values
    to the corresponding sockets in a Blender modifier.
    """

    def __init__(self, modifier: bpy.types.NodesModifier):
        """
        Initialize value setter for a specific modifier.

        Args:
            modifier: The Blender modifier to set values on
        """
        self.modifier = modifier
        self.set_values = {}  # Track which values were set

    def set_from_parameter_set(self, parameter_set: ComponentParameterSet) -> bool:
        """
        Set all values from a component parameter set.

        Args:
            parameter_set: Complete parameter set for the component

        Returns:
            bool: True if all values were set successfully
        """
        try:
            print(f"Setting values for {parameter_set.component_type}.{parameter_set.component_id}")

            success_count = 0
            total_count = 0

            # Set values for each group
            for group in parameter_set.groups:
                group_success, group_total = self.set_from_parameter_group(group)
                success_count += group_success
                total_count += group_total

            print(f"Set {success_count}/{total_count} parameter values")
            # Consider it successful if at least 80% of parameters were set
            success_rate = success_count / total_count if total_count > 0 else 0
            return success_rate >= 0.8

        except Exception as e:
            print(f"Error setting values from parameter set: {e}")
            return False

    def set_from_parameter_group(self, group: ParameterGroup) -> tuple[int, int]:
        """
        Set values from a single parameter group.

        Args:
            group: Parameter group to set values from

        Returns:
            tuple: (success_count, total_count)
        """
        try:
            print(f"Setting values for group: {group.name}")

            success_count = 0
            total_count = 0

            # Set value for each parameter
            for param in group.parameters:
                total_count += 1
                if self.set_parameter_value(param):
                    success_count += 1

            return success_count, total_count

        except Exception as e:
            print(f"Error setting values from parameter group {group.name}: {e}")
            return 0, len(group.parameters)

    def set_parameter_value(self, param: ParameterDefinition) -> bool:
        """
        Set value for a single parameter.

        Args:
            param: Parameter definition to set value from

        Returns:
            bool: True if value was set successfully
        """
        try:
            # Skip output parameters (they don't have settable values)
            if not param.is_input:
                return True

            # Skip parameters without default values
            if param.default_value is None:
                print(f"Skipping parameter {param.name} (no default value)")
                return True

            # Find the socket ID
            socket_id = find_socket_by_name(self.modifier, param.name)
            if not socket_id:
                print(f"Warning: Socket not found for parameter: {param.name}")
                return False

            # Process value if processor is defined
            value = param.default_value
            if param.processor:
                try:
                    value = param.processor(value)
                except Exception as e:
                    print(f"Warning: Value processor failed for {param.name}: {e}")
                    # Continue with original value

            # Validate value if validator is defined
            if param.validator and not param.validator(value):
                print(f"Warning: Value validation failed for {param.name}: {value}")
                return False

            # Set the value
            try:
                self.modifier[socket_id] = value
                self.set_values[param.name] = value
                print(f"Set {param.name} = {value}")
                return True

            except (TypeError, ValueError) as e:
                print(f"Error setting value for {param.name}: {e}")
                return False

        except Exception as e:
            print(f"Error setting parameter value {param.name}: {e}")
            return False

    def get_set_value(self, name: str) -> Any:
        """
        Get a value that was set by this setter.

        Args:
            name: Name of the parameter

        Returns:
            The value that was set, or None if not found
        """
        return self.set_values.get(name)

    def get_set_parameter_names(self) -> List[str]:
        """Get list of all parameter names that were set."""
        return list(self.set_values.keys())

    def override_parameter_value(self, param_name: str, value: Any) -> bool:
        """
        Override a parameter value after initial setting.

        Args:
            param_name: Name of the parameter to override
            value: New value to set

        Returns:
            bool: True if value was set successfully
        """
        try:
            socket_id = find_socket_by_name(self.modifier, param_name)
            if not socket_id:
                print(f"Warning: Socket not found for parameter: {param_name}")
                return False

            self.modifier[socket_id] = value
            self.set_values[param_name] = value
            print(f"Override {param_name} = {value}")
            return True

        except Exception as e:
            print(f"Error overriding parameter value {param_name}: {e}")
            return False


def set_values_from_parameters(
    modifier: bpy.types.NodesModifier,
    parameter_set: ComponentParameterSet
) -> bool:
    """
    Convenience function to set values from parameter set.

    Args:
        modifier: The modifier to set values on
        parameter_set: Parameter set defining the values

    Returns:
        bool: True if all values were set successfully
    """
    setter = ValueSetter(modifier)
    return setter.set_from_parameter_set(parameter_set)


def set_values_from_groups(
    modifier: bpy.types.NodesModifier,
    groups: List[ParameterGroup]
) -> bool:
    """
    Convenience function to set values from parameter groups.

    Args:
        modifier: The modifier to set values on
        groups: List of parameter groups to set values from

    Returns:
        bool: True if all values were set successfully
    """
    setter = ValueSetter(modifier)

    success_count = 0
    total_count = 0

    for group in groups:
        group_success, group_total = setter.set_from_parameter_group(group)
        success_count += group_success
        total_count += group_total

    return success_count == total_count


def set_single_parameter_value(
    modifier: bpy.types.NodesModifier,
    param: ParameterDefinition
) -> bool:
    """
    Set value for a single parameter.

    Args:
        modifier: The modifier to set value on
        param: Parameter definition

    Returns:
        bool: True if value was set successfully
    """
    setter = ValueSetter(modifier)
    return setter.set_parameter_value(param)
