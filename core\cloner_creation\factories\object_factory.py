"""
Фабрика для создания объектных клонеров.

Этот модуль содержит основную логику координации создания клонеров,
объединяя все компоненты системы.

"""

import bpy
from .object_manager import (
    create_cloner_object,
    create_cloner_collection,
    create_modifier,
    add_object_to_collection
)
from .chain_manager import setup_chain_properties
from ..common.naming import generate_node_group_name
from ..common.visibility import (
    setup_cloner_visibility,
    setup_collection_visibility,
    hide_original_source,
    ensure_chain_visibility
)
from ..builders import build_node_structure
# Импорт из новой модульной системы stacked_cloner_modules
from .stacked_factory import create_standard_stacked_cloner
from ....operations.helpers.parameter_setup.universal_params import setup_cloner_params

def create_object_cloner(context, cloner_type, orig_obj, use_stacked_modifiers=False, use_custom_group=True):
    """
    Создает клонер для объекта.

    Args:
        context: Контекст Blender
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для клонирования
        use_stacked_modifiers: Использовать стековые модификаторы
        use_custom_group: Использовать кастомную группу узлов

    Returns:
        bool: True если клонер успешно создан, False в случае ошибки
    """
    # Проверяем режим работы - стековые модификаторы или обычные клонеры
    is_stacked_mode = use_stacked_modifiers
    print(f"DEBUG: use_stacked_modifiers = {use_stacked_modifiers}, is_stacked_mode = {is_stacked_mode}")

    # В зависимости от режима вызываем соответствующую функцию
    if is_stacked_mode:
        # Создаем стековый клонер на том же объекте
        modifier, success = create_standard_stacked_cloner(context, cloner_type, orig_obj)
        return success
    else:
        # Создаем обычный клонер (новый объект с модификатором)
        success = create_standard_object_cloner(context, cloner_type, orig_obj, use_custom_group)
        return success

def create_standard_object_cloner(context, cloner_type, orig_obj, use_custom_group=True):
    """
    Создает обычный (не стековый) клонер для объекта.

    Args:
        context: Контекст Blender
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для клонирования
        use_custom_group: Использовать кастомную группу узлов

    Returns:
        bool: True если клонер успешно создан, False в случае ошибки
    """
    try:
        # Создаем объект клонера (используем object_manager)
        cloner_obj = create_cloner_object(orig_obj, cloner_type)
        if not cloner_obj:
            print("Ошибка при создании объекта клонера")
            return False

        # Создаем коллекцию для клонера (используем object_manager)
        cloner_collection = create_cloner_collection(context, orig_obj, cloner_type)
        if not cloner_collection:
            print("Ошибка при создании коллекции клонера")
            return False

        # Добавляем клонер-объект в коллекцию (используем object_manager)
        if not add_object_to_collection(cloner_obj, cloner_collection):
            print("Ошибка при добавлении объекта в коллекцию")
            return False

        # Настраиваем видимость (используем новые утилиты)
        setup_collection_visibility(context, cloner_collection)
        setup_cloner_visibility(cloner_obj)
        ensure_chain_visibility(context, orig_obj)

        # Создаем модификатор для клонера (используем object_manager)
        modifier = create_modifier(cloner_obj, cloner_type)
        if not modifier:
            print("Ошибка при создании модификатора клонера")
            return False

        # НОВАЯ АРХИТЕКТУРА: Используем двухуровневую систему с wrapper группой
        from ....models.cloners.object.object_wrapper import create_object_cloner_wrapper_group

        # Создаем wrapper группу с внутренней группой клонера + эффектором
        node_group = create_object_cloner_wrapper_group(
            cloner_type,
            orig_obj,
            f"_{orig_obj.name}",
            context.scene.use_anti_recursion
        )

        if not node_group:
            print("Ошибка при создании wrapper группы объектного клонера")
            return False

        # НЕ применяем apply_anti_recursion_to_cloner для новых клонеров
        # Новые построители узлов уже создают правильную структуру анти-рекурсии
        # apply_anti_recursion_to_cloner предназначен для исправления старых клонеров

        # Устанавливаем свойства для клонера
        modifier["is_chained_cloner"] = True
        modifier["source_type"] = "OBJECT"
        modifier["cloner_collection"] = cloner_collection.name

        # Устанавливаем группу узлов для модификатора
        modifier.node_group = node_group

        # КРИТИЧЕСКИ ВАЖНО: Устанавливаем объект в модификаторе после установки node_group
        print(f"[OBJECT_FACTORY DEBUG] Setting object {orig_obj.name} in modifier for {cloner_type} cloner...")
        _set_object_in_modifier(modifier, orig_obj)

        # Инициализируем список эффекторов
        node_group["linked_effectors"] = []

        # Сохраняем тип источника и другие метаданные
        modifier["source_type"] = "OBJECT"
        modifier["original_object"] = orig_obj.name
        modifier["cloner_collection"] = cloner_collection.name

        print(f"CLONER DEBUG: Создаем клонер для объекта {orig_obj.name}")
        print(f"CLONER DEBUG: Установлен original_object = {orig_obj.name}")

        # Сохраняем состояние видимости оригинального объекта
        modifier["original_hide_viewport"] = orig_obj.hide_viewport
        modifier["original_hide_render"] = orig_obj.hide_render

        # Скрываем оригинальный объект (используем унифицированную функцию)
        hide_original_source(context, orig_obj, mode="object")

        # Делаем клонер-объект активным
        for obj in context.selected_objects:
            obj.select_set(False)
        cloner_obj.select_set(True)
        context.view_layer.objects.active = cloner_obj

        # Настраиваем свойства цепочки клонеров (используем chain_manager)
        if not setup_chain_properties(modifier, orig_obj, cloner_obj):
            print("Ошибка при настройке свойств цепочки")
            # Продолжаем выполнение, так как это не критическая ошибка

        # ТЕПЕРЬ устанавливаем параметры клонера (после установки chain_source_object)
        print(f"PARAMS DEBUG: Устанавливаем параметры для {cloner_type} клонера")
        print(f"PARAMS DEBUG: chain_source_object = {modifier.get('chain_source_object', 'НЕ УСТАНОВЛЕН')}")

        # Устанавливаем параметры клонера через универсальную систему
        setup_cloner_params(modifier, cloner_type)

        # Обновляем UI
        context.view_layer.update()

        return True

    except Exception as e:
        print(f"Error creating object cloner: {e}")
        return False


def _set_object_in_modifier(modifier, orig_obj):
    """
    Устанавливает объект в модификаторе через Object сокет.

    Args:
        modifier: Модификатор геометрических узлов
        orig_obj: Объект для установки
    """
    try:
        if not modifier.node_group:
            print(f"[OBJECT_FACTORY DEBUG] WARNING: No node group in modifier")
            return False

        print(f"[OBJECT_FACTORY DEBUG] Node group: {modifier.node_group.name}")
        print(f"[OBJECT_FACTORY DEBUG] Interface items: {[item.name for item in modifier.node_group.interface.items_tree if item.item_type == 'SOCKET' and item.in_out == 'INPUT']}")

        # Ищем Object сокет в интерфейсе группы узлов
        for item in modifier.node_group.interface.items_tree:
            if item.item_type == 'SOCKET' and item.in_out == 'INPUT' and item.name == 'Object':
                socket_id = item.identifier
                print(f"[OBJECT_FACTORY DEBUG] Found Object socket with ID: {socket_id}")
                try:
                    modifier[socket_id] = orig_obj
                    print(f"[OBJECT_FACTORY DEBUG] Объект {orig_obj.name} установлен в модификаторе через сокет {socket_id}")

                    # Дополнительно устанавливаем объект в узлах ObjectInfo внутри группы
                    _set_object_in_object_info_nodes(modifier.node_group, orig_obj)

                    return True
                except Exception as e:
                    print(f"[OBJECT_FACTORY DEBUG] ERROR: Не удалось установить объект в модификаторе: {e}")
                    return False

        print(f"[OBJECT_FACTORY DEBUG] WARNING: Object сокет не найден в интерфейсе группы {modifier.node_group.name}")
        return False

    except Exception as e:
        print(f"[ERROR] Ошибка при установке объекта в модификаторе: {e}")
        return False


def _set_object_in_object_info_nodes(node_group, orig_obj):
    """
    Устанавливает объект во всех узлах ObjectInfo в группе узлов.

    Args:
        node_group: Группа узлов
        orig_obj: Объект для установки
    """
    try:
        # Рекурсивно ищем узлы ObjectInfo во всех группах
        def find_and_set_object_info(group):
            for node in group.nodes:
                if node.bl_idname == 'GeometryNodeObjectInfo':
                    try:
                        node.inputs['Object'].default_value = orig_obj
                        print(f"[DEBUG] Объект {orig_obj.name} установлен в узле ObjectInfo: {node.name}")
                    except Exception as e:
                        print(f"[WARNING] Не удалось установить объект в узле ObjectInfo {node.name}: {e}")
                elif node.bl_idname == 'GeometryNodeGroup' and node.node_tree:
                    # Рекурсивно обрабатываем вложенные группы
                    find_and_set_object_info(node.node_tree)

        find_and_set_object_info(node_group)

    except Exception as e:
        print(f"[ERROR] Ошибка при установке объекта в узлах ObjectInfo: {e}")
