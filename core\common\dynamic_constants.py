"""
Динамические константы, генерируемые из реестра компонентов.

Заменяет статические константы автоматически генерируемыми
на основе зарегистрированных компонентов.

Содержит все константы для клонеров, эффекторов и полей.
"""

# Статические константы для эффекторов (пока не в реестре)
EFFECTOR_TYPES = [
    ("RANDOM", "Random", "Apply random transformations to clones", "RNDCURVE"),
    ("NOISE", "Noise", "Apply noise-based transformations to clones", "FORCE_TURBULENCE"),
]

# Константы для типов эффекторов и их идентификации
EFFECTOR_TYPE_RANDOM = "RandomEffector"
EFFECTOR_TYPE_NOISE = "NoiseEffector"

# Константы для идентификации сокетов
SOCKET_INSTANCE_SCALE = "Socket_5"  # instance_scale
SOCKET_GLOBAL_POSITION = "Socket_8"  # global_position
SOCKET_GLOBAL_ROTATION = "Socket_9"  # global_rotation
SOCKET_RANDOM_SEED = "Socket_10"     # random_seed
SOCKET_RANDOM_POSITION = "Socket_6"  # random_position
SOCKET_RANDOM_ROTATION = "Socket_7"  # random_rotation
SOCKET_RANDOM_SCALE = "Socket_11"    # random_scale

def get_cloner_types():
    """Получает список типов клонеров из реестра"""
    try:
        from ..registry import component_registry
        return component_registry.get_cloner_types()
    except ImportError:
        # Fallback для случаев, когда реестр еще не инициализирован
        return []

def get_effector_types():
    """Получает список типов эффекторов из реестра"""
    try:
        from ..registry import component_registry
        return component_registry.get_effector_types()
    except ImportError:
        return []

def get_field_types():
    """Получает список типов полей из реестра"""
    try:
        from ..registry import component_registry
        return component_registry.get_field_types()
    except ImportError:
        return []

def get_available_cloners():
    """Получает словарь доступных клонеров"""
    try:
        from ..registry import component_registry
        return {
            meta.component_id: component_registry.get_cloner_class(meta.component_id)
            for meta in component_registry._cloner_meta.values()
        }
    except ImportError:
        return {}

def get_available_effectors():
    """Получает словарь доступных эффекторов"""
    try:
        from ..registry import component_registry
        return {
            meta.component_id: component_registry.get_effector_class(meta.component_id)
            for meta in component_registry._effector_meta.values()
        }
    except ImportError:
        return {}

def get_available_fields():
    """Получает словарь доступных полей"""
    try:
        from ..registry import component_registry
        return {
            meta.component_id: component_registry.get_field_class(meta.component_id)
            for meta in component_registry._field_meta.values()
        }
    except ImportError:
        return {}

def get_cloner_group_names():
    """Получает имена групп узлов для клонеров"""
    try:
        from ..registry import component_registry
        return component_registry.get_cloner_group_names()
    except ImportError:
        return {}

def get_cloner_mod_names():
    """Получает имена модификаторов для клонеров"""
    try:
        from ..registry import component_registry
        return component_registry.get_cloner_mod_names()
    except ImportError:
        return {}

def get_effector_group_names():
    """Получает имена групп узлов для эффекторов"""
    try:
        from ..registry import component_registry
        return {
            meta.component_id: f"{meta.display_name.replace(' ', '')}"
            for meta in component_registry._effector_meta.values()
        }
    except ImportError:
        return {}

def get_effector_mod_names():
    """Получает имена модификаторов для эффекторов"""
    try:
        from ..registry import component_registry
        return {
            meta.component_id: meta.display_name
            for meta in component_registry._effector_meta.values()
        }
    except ImportError:
        return {}

def get_field_group_names():
    """Получает имена групп узлов для полей"""
    try:
        from ..registry import component_registry
        return {
            meta.component_id: f"{meta.display_name.replace(' ', '')}"
            for meta in component_registry._field_meta.values()
        }
    except ImportError:
        return {}

def get_field_mod_names():
    """Получает имена модификаторов для полей"""
    try:
        from ..registry import component_registry
        return {
            meta.component_id: meta.display_name
            for meta in component_registry._field_meta.values()
        }
    except ImportError:
        return {}

def get_cloner_node_group_prefixes():
    """Получает префиксы групп узлов для клонеров"""
    return list(get_cloner_group_names().values())

def get_effector_node_group_prefixes():
    """Получает префиксы групп узлов для эффекторов"""
    return list(get_effector_group_names().values())

def get_field_node_group_prefixes():
    """Получает префиксы групп узлов для полей"""
    return list(get_field_group_names().values())

# Функции создания для обратной совместимости
def get_node_group_creators():
    """Получает функции создания групп узлов"""
    try:
        from ..registry import component_registry
        creators = {}

        # Клонеры
        for component_id in component_registry._cloners:
            cloner_class = component_registry.get_cloner_class(component_id)
            creators[component_id] = cloner_class.create_node_group

        # Эффекторы
        for component_id in component_registry._effectors:
            effector_class = component_registry.get_effector_class(component_id)
            creators[component_id] = effector_class.create_node_group

        # Поля
        for component_id in component_registry._fields:
            field_class = component_registry.get_field_class(component_id)
            creators[component_id] = field_class.create_node_group

        return creators
    except ImportError:
        return {}

# Динамические функции создания нод-групп (генерируются из реестра)
def gridcloner3d_node_group():
    """Создает Grid Cloner node group через универсальную фабрику"""
    try:
        from ..factories.universal_factory import universal_factory
        return universal_factory.create_cloner("GRID")
    except ImportError:
        return None

def advancedlinearcloner_node_group():
    """Создает Linear Cloner node group через универсальную фабрику"""
    try:
        from ..factories.universal_factory import universal_factory
        return universal_factory.create_cloner("LINEAR")
    except ImportError:
        return None

def circlecloner_node_group():
    """Создает Circle Cloner node group через универсальную фабрику"""
    try:
        from ..factories.universal_factory import universal_factory
        return universal_factory.create_cloner("CIRCLE")
    except ImportError:
        return None

def create_random_effector():
    """Создает Random Effector node group через класс эффектора"""
    try:
        from ...models.effectors.random import RandomEffector
        return RandomEffector.create_node_group()
    except ImportError:
        return None

def create_noise_effector():
    """Создает Noise Effector node group через класс эффектора"""
    try:
        from ...models.effectors.noise import NoiseEffector
        return NoiseEffector.create_node_group()
    except ImportError:
        return None

def spherefield_node_group():
    """Создает Sphere Field node group через универсальную фабрику"""
    try:
        from ..factories.universal_factory import universal_factory
        return universal_factory.create_field("SPHERE")
    except ImportError:
        return None

# Функции создания для каждого типа эффектора
def get_effector_creators():
    """Получает словарь функций создания эффекторов"""
    return {
        "RANDOM": create_random_effector,
        "NOISE": create_noise_effector,
    }

# Функции создания для каждого типа поля
def get_field_creators():
    """Получает словарь функций создания полей"""
    return {
        "SPHERE": spherefield_node_group,
    }

# Статические имена групп узлов для эффекторов (пока не в реестре)
def get_static_effector_group_names():
    """Получает статические имена групп узлов для эффекторов"""
    return {
        "RANDOM": "RandomEffector",
        "NOISE": "NoiseEffector",
    }

# Статические имена модификаторов для эффекторов (пока не в реестре)
def get_static_effector_mod_names():
    """Получает статические имена модификаторов для эффекторов"""
    return {
        "RANDOM": "Random Effector",
        "NOISE": "Noise Effector",
    }
