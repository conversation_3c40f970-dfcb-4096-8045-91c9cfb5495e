"""
Duplication utilities for Advanced Cloners addon.

This module provides functionality for duplicating objects and managing
the duplication cache for cloner operations.
"""

import bpy
import time
from collections import OrderedDict

# Import all functionality from sub-modules
from .chain_tracker import (
    get_or_create_duplicate_for_cloner,
    get_cloner_chain_for_object,
    cloner_hierarchy_map
)
from .collection_manager import (
    create_cloner_collection,
    get_parent_collection
)
from .cleanup_manager import (
    restore_original_object,
    cleanup_empty_cloner_collections
)

# Cache of already created duplicates for optimization
mesh_duplicates_cache = OrderedDict()
# Maximum cache size
MAX_CACHE_SIZE = 20


def get_mesh_duplicate(obj, target_collection=None, hide_original=True):
    """
    Creates a duplicate of the given object for use with cloners.

    Args:
        obj (bpy.types.Object): Object to duplicate
        target_collection (bpy.types.Collection): Collection to add duplicate to
        hide_original (bool): Whether to hide the original object in viewport

    Returns:
        bpy.types.Object: Duplicate object
    """
    # Используем кэшированный импорт для оптимизации
    from ..optimization import get_create_cloner_collection

    create_cloner_collection = get_create_cloner_collection()
    if not create_cloner_collection:
        # Fallback на прямой импорт
        from .collection_manager import create_cloner_collection

    # Check if object exists
    if not obj:
        return None

    # Generate cache key based on object and modifier
    cache_key = (obj.name, str(id(target_collection)))

    # Check if we already have a duplicate for this object/collection
    if cache_key in mesh_duplicates_cache:
        # Return existing duplicate if its object still exists
        cached_obj, cached_collection = mesh_duplicates_cache[cache_key]
        # Быстрая проверка через try/except вместо поиска в bpy.data.objects
        if cached_obj:
            try:
                # Проверяем, что объект все еще валиден
                _ = cached_obj.name
                _ = cached_obj.data
                return cached_obj
            except ReferenceError:
                # Объект был удален, убираем из кэша
                del mesh_duplicates_cache[cache_key]

    # Create target collection if not provided
    if not target_collection:
        target_collection = create_cloner_collection(f"cloner_{obj.name}")

    # Create duplicate data based on object type
    duplicate_data = None
    if obj.type == 'MESH':
        # Duplicate mesh data
        duplicate_data = obj.data.copy()
        duplicate_data.name = f"{obj.data.name}_cloner_{int(time.time())}"
    elif obj.type == 'CURVE':
        # Duplicate curve data
        duplicate_data = obj.data.copy()
        duplicate_data.name = f"{obj.data.name}_cloner_{int(time.time())}"
    elif obj.type == 'FONT':
        # Duplicate text data
        duplicate_data = obj.data.copy()
        duplicate_data.name = f"{obj.data.name}_cloner_{int(time.time())}"
    else:
        # Unsupported object type, use original data (may cause issues)
        duplicate_data = obj.data

    # Create new object with duplicated data
    duplicate_obj = bpy.data.objects.new(f"{obj.name}_dup", duplicate_data)

    # Store reference to original object - this is critical
    duplicate_obj["original_obj"] = obj.name

    # Сохраняем текущее состояние видимости оригинала
    duplicate_obj["original_hide_viewport"] = obj.hide_viewport
    duplicate_obj["original_hide_render"] = obj.hide_render

    # Copy transformations
    duplicate_obj.matrix_world = obj.matrix_world.copy()

    # Copy materials if possible
    if hasattr(obj, 'material_slots') and hasattr(duplicate_obj.data, 'materials'):
        for mat_slot in obj.material_slots:
            if mat_slot.material:
                duplicate_obj.data.materials.append(mat_slot.material)

    # Assign appropriate collection
    if target_collection:
        # Add to target collection
        target_collection.objects.link(duplicate_obj)
    else:
        # Add to same collection(s) as original
        for collection in bpy.data.collections:
            if obj.name in collection.objects:
                collection.objects.link(duplicate_obj)
                break
        else:
            # Fallback to scene collection
            bpy.context.scene.collection.objects.link(duplicate_obj)

    # Hide original object if requested
    if hide_original:
        obj.hide_viewport = True
        obj.hide_render = True  # Также скрываем из рендера

    # Add to cache
    mesh_duplicates_cache[cache_key] = (duplicate_obj, target_collection)

    return duplicate_obj


# Public API
__all__ = [
    # Main functions
    'get_mesh_duplicate',
    'mesh_duplicates_cache',
    'MAX_CACHE_SIZE',

    # From chain_tracker
    'get_or_create_duplicate_for_cloner',
    'get_cloner_chain_for_object',
    'cloner_hierarchy_map',

    # From collection_manager
    'create_cloner_collection',
    'get_parent_collection',

    # From cleanup_manager
    'restore_original_object',
    'cleanup_empty_cloner_collections'
]


def register():
    """Register duplication components"""
    from .chain_tracker import register as register_chain_tracker
    from .collection_manager import register as register_collection_manager
    from .cleanup_manager import register as register_cleanup_manager

    register_chain_tracker()
    register_collection_manager()
    register_cleanup_manager()


def unregister():
    """Unregister duplication components"""
    from .chain_tracker import unregister as unregister_chain_tracker
    from .collection_manager import unregister as unregister_collection_manager
    from .cleanup_manager import unregister as unregister_cleanup_manager

    unregister_cleanup_manager()
    unregister_collection_manager()
    unregister_chain_tracker()
