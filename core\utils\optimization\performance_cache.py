"""
Система кэширования для улучшения производительности.

Кэширует часто используемые операции и данные для ускорения работы аддона.
"""

import bpy
import time
from typing import Dict, Any, Optional, Callable
from functools import wraps

# Глобальные кэши
_component_cache: Dict[str, Any] = {}
_ui_cache: Dict[str, Any] = {}
_node_group_cache: Dict[str, Any] = {}
_collection_cache: Dict[str, Any] = {}
_object_cache: Dict[str, Any] = {}  # Кэш для пакетной обработки объектов

# Настройки кэширования
CACHE_TIMEOUT = 30.0  # Время жизни кэша в секундах
MAX_CACHE_SIZE = 100  # Максимальный размер кэша


class CacheEntry:
    """Запись в кэше с временной меткой"""
    def __init__(self, value: Any, timeout: float = CACHE_TIMEOUT):
        self.value = value
        self.timestamp = time.time()
        self.timeout = timeout

    def is_expired(self) -> bool:
        """Проверяет, истек ли срок действия кэша"""
        return time.time() - self.timestamp > self.timeout

    def is_valid(self) -> bool:
        """Проверяет, валидна ли запись"""
        return not self.is_expired()


def cached_function(cache_dict: Dict[str, CacheEntry], timeout: float = CACHE_TIMEOUT):
    """
    Декоратор для кэширования результатов функций.

    Args:
        cache_dict: Словарь для хранения кэша
        timeout: Время жизни кэша в секундах
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Создаем ключ кэша из аргументов
            cache_key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"

            # Проверяем кэш
            if cache_key in cache_dict:
                entry = cache_dict[cache_key]
                if entry.is_valid():
                    return entry.value
                else:
                    # Удаляем устаревшую запись
                    del cache_dict[cache_key]

            # Вычисляем результат
            result = func(*args, **kwargs)

            # Сохраняем в кэш
            cache_dict[cache_key] = CacheEntry(result, timeout)

            # Очищаем кэш если он слишком большой
            if len(cache_dict) > MAX_CACHE_SIZE:
                cleanup_cache(cache_dict)

            return result
        return wrapper
    return decorator


def cleanup_cache(cache_dict: Dict[str, CacheEntry]):
    """Очищает устаревшие записи из кэша"""
    current_time = time.time()
    expired_keys = [
        key for key, entry in cache_dict.items()
        if current_time - entry.timestamp > entry.timeout
    ]

    for key in expired_keys:
        del cache_dict[key]

    # Если кэш все еще слишком большой, удаляем самые старые записи
    if len(cache_dict) > MAX_CACHE_SIZE:
        sorted_items = sorted(
            cache_dict.items(),
            key=lambda x: x[1].timestamp
        )

        # Удаляем половину самых старых записей
        items_to_remove = len(cache_dict) - MAX_CACHE_SIZE // 2
        for key, _ in sorted_items[:items_to_remove]:
            del cache_dict[key]


def clear_all_caches():
    """Очищает все кэши"""
    global _component_cache, _ui_cache, _node_group_cache, _collection_cache, _object_cache
    _component_cache.clear()
    _ui_cache.clear()
    _node_group_cache.clear()
    _collection_cache.clear()
    _object_cache.clear()


def get_cache_stats() -> Dict[str, int]:
    """Возвращает статистику использования кэшей"""
    return {
        "component_cache": len(_component_cache),
        "ui_cache": len(_ui_cache),
        "node_group_cache": len(_node_group_cache),
        "collection_cache": len(_collection_cache),
        "object_cache": len(_object_cache),
        "total": len(_component_cache) + len(_ui_cache) + len(_node_group_cache) + len(_collection_cache) + len(_object_cache)
    }


# Специализированные кэши для часто используемых операций

@cached_function(_component_cache, timeout=60.0)
def get_cached_cloner_types():
    """Кэшированное получение типов клонеров"""
    try:
        from ...registry import component_registry
        return component_registry.get_cloner_types()
    except ImportError as e:
        print(f"[ERROR] Could not import component_registry: {e}")
        # Возвращаем пустой список как fallback
        return []


@cached_function(_ui_cache, timeout=10.0)
def get_cached_collections():
    """Кэшированное получение списка коллекций"""
    return [(col.name, col.name, "") for col in bpy.data.collections]


@cached_function(_node_group_cache, timeout=30.0)
def get_cached_node_groups():
    """Кэшированное получение списка групп узлов"""
    return [ng.name for ng in bpy.data.node_groups if ng.type == 'GEOMETRY']


@cached_function(_collection_cache, timeout=5.0)
def is_object_valid(obj_name: str) -> bool:
    """Кэшированная проверка существования объекта"""
    return obj_name in bpy.data.objects


def invalidate_cache_on_scene_update(_scene=None, _depsgraph=None):
    """Инвалидирует кэши при изменении сцены"""
    # Очищаем только кэши, которые зависят от состояния сцены
    _ui_cache.clear()
    _collection_cache.clear()
    _object_cache.clear()  # Очищаем кэш пакетной обработки объектов


# Регистрируем обработчик обновления сцены
def register_cache_handlers():
    """Регистрирует обработчики для автоматической очистки кэша"""
    if invalidate_cache_on_scene_update not in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.append(invalidate_cache_on_scene_update)


def unregister_cache_handlers():
    """Отменяет регистрацию обработчиков кэша"""
    if invalidate_cache_on_scene_update in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.remove(invalidate_cache_on_scene_update)


# Утилиты для профилирования производительности

class PerformanceTimer:
    """Контекстный менеджер для измерения времени выполнения"""
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        elapsed = time.time() - self.start_time
        print(f"[PERF] {self.operation_name}: {elapsed:.4f}s")


def performance_monitor(func: Callable):
    """Декоратор для мониторинга производительности функций"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        with PerformanceTimer(f"{func.__module__}.{func.__name__}"):
            return func(*args, **kwargs)
    return wrapper


# Оптимизированные версии часто используемых операций

def fast_object_exists(obj_name: str) -> bool:
    """Быстрая проверка существования объекта с кэшированием"""
    return is_object_valid(obj_name)


def fast_get_collections():
    """Быстрое получение списка коллекций с кэшированием"""
    return get_cached_collections()


def fast_get_cloner_types():
    """Быстрое получение типов клонеров с кэшированием"""
    return get_cached_cloner_types()


# Экспорт публичного API
__all__ = [
    'cached_function',
    'clear_all_caches',
    'get_cache_stats',
    'PerformanceTimer',
    'performance_monitor',
    'fast_object_exists',
    'fast_get_collections',
    'fast_get_cloner_types',
    'register_cache_handlers',
    'unregister_cache_handlers'
]
