"""
Sphere Field module.

This module contains the sphere field implementation,
which provides spherical influence areas for effectors.
"""

# Import the sphere field class and functions
from .sphere_field import (
    SphereField,
    spherefield_node_group,
    advanced_spherefield_node_group,
    simplest_spherefield_node_group
)

# Public API
__all__ = [
    'SphereField',
    'spherefield_node_group',
    'advanced_spherefield_node_group',
    'simplest_spherefield_node_group'
]


def register():
    """Register sphere field components"""
    pass


def unregister():
    """Unregister sphere field components"""
    pass
