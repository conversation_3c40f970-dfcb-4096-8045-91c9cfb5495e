"""
Linear Cloner Logic Builder

This module contains the core logic for creating the linear cloner node group.
It handles linear interpolation, random transforms, and instance picking.
"""

import bpy
from ..base import ClonerB<PERSON>


def create_logic_group(name_suffix=""):
    """Create a node group with the core linear cloner logic"""

    # Create new node group
    logic_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=f"LinearClonerLogic{name_suffix}")

    # --- Interface ---
    # Output
    logic_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Changed: Removed direct Geometry input, added Instance Source input
    logic_group.interface.new_socket(name="Instance Source", in_out='INPUT', socket_type='NodeSocketGeometry')

    # Basic Settings
    count_input = logic_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
    count_input.default_value = 5
    count_input.min_value = 1
    count_input.max_value = 1000

    offset_input = logic_group.interface.new_socket(name="Offset", in_out='INPUT', socket_type='NodeSocketVector')
    offset_input.default_value = (1.0, 0.0, 0.0)

    # Scale Start/End Settings
    scale_start_input = logic_group.interface.new_socket(name="Scale Start", in_out='INPUT', socket_type='NodeSocketVector')
    scale_start_input.default_value = (1.0, 1.0, 1.0)

    scale_end_input = logic_group.interface.new_socket(name="Scale End", in_out='INPUT', socket_type='NodeSocketVector')
    scale_end_input.default_value = (1.0, 1.0, 1.0)

    # Rotation Start/End Settings
    rotation_start_input = logic_group.interface.new_socket(name="Rotation Start", in_out='INPUT', socket_type='NodeSocketVector')
    rotation_start_input.default_value = (0.0, 0.0, 0.0)
    rotation_start_input.subtype = 'EULER'

    rotation_end_input = logic_group.interface.new_socket(name="Rotation End", in_out='INPUT', socket_type='NodeSocketVector')
    rotation_end_input.default_value = (0.0, 0.0, 0.0)
    rotation_end_input.subtype = 'EULER'

    # Random Settings
    random_position_input = logic_group.interface.new_socket(name="Random Position", in_out='INPUT', socket_type='NodeSocketVector')
    random_position_input.default_value = (0.0, 0.0, 0.0)

    random_rotation_input = logic_group.interface.new_socket(name="Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    random_rotation_input.default_value = (0.0, 0.0, 0.0)

    random_scale_input = logic_group.interface.new_socket(name="Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    random_scale_input.default_value = 0.0
    random_scale_input.min_value = 0.0
    random_scale_input.max_value = 1.0

    seed_input = logic_group.interface.new_socket(name="Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
    seed_input.default_value = 0
    seed_input.min_value = 0
    seed_input.max_value = 10000

    # Switch between normal instancing and random pick
    pick_instance_input = logic_group.interface.new_socket(name="Pick Random Instance", in_out='INPUT', socket_type='NodeSocketBool')
    pick_instance_input.default_value = False

    # --- Nodes ---
    nodes = logic_group.nodes
    links = logic_group.links

    # Add group input and output
    group_input = nodes.new('NodeGroupInput')
    group_output = nodes.new('NodeGroupOutput')

    # Setup base linear distribution
    mesh_line, offset_multiplier = _setup_linear_distribution(nodes, links, group_input)

    # Setup interpolation factors
    index, interpolation_factor = _setup_interpolation_logic(nodes, links, group_input)

    # Setup scale and rotation interpolation
    interpolated_scale, interpolated_rotation = _setup_interpolation_mixing(
        nodes, links, group_input, interpolation_factor
    )

    # Setup random transforms
    random_nodes = _setup_random_transforms(nodes, links, group_input, index)

    # Setup instancing with pick random option
    final_instances = _setup_instancing_logic(
        nodes, links, group_input, mesh_line, index, random_nodes
    )

    # Apply all transforms
    final_output = _apply_all_transforms(
        nodes, links, final_instances, interpolated_scale, interpolated_rotation, random_nodes
    )

    # Connect to output
    links.new(final_output, group_output.inputs['Geometry'])

    return logic_group


def _setup_linear_distribution(nodes, links, group_input):
    """Setup the base linear distribution using mesh line"""
    # Используем значения напрямую без множителей
    # Это позволит применять значения из конфигурации без дополнительных преобразований

    # Для обратной совместимости сохраняем ноды, но устанавливаем множитель 1.0
    offset_multiplier = nodes.new('ShaderNodeVectorMath')
    offset_multiplier.operation = 'MULTIPLY'
    offset_multiplier.inputs[1].default_value = (1.0, 1.0, 1.0)  # Множитель 1.0 (без изменений)
    links.new(group_input.outputs['Offset'], offset_multiplier.inputs[0])

    # Base cloner elements
    mesh_line = nodes.new('GeometryNodeMeshLine')
    mesh_line.mode = 'OFFSET'
    mesh_line.count_mode = 'TOTAL'

    # Basic cloning setup
    links.new(group_input.outputs['Count'], mesh_line.inputs['Count'])
    links.new(offset_multiplier.outputs['Vector'], mesh_line.inputs['Offset'])

    return mesh_line, offset_multiplier


def _setup_interpolation_logic(nodes, links, group_input):
    """Setup interpolation factor calculation"""
    index = nodes.new('GeometryNodeInputIndex')
    
    math_subtract = nodes.new('ShaderNodeMath')
    math_subtract.operation = 'SUBTRACT'
    math_subtract.inputs[1].default_value = 1.0

    math_max = nodes.new('ShaderNodeMath')
    math_max.operation = 'MAXIMUM'
    math_max.inputs[1].default_value = 1.0

    math_divide = nodes.new('ShaderNodeMath')
    math_divide.operation = 'DIVIDE'

    # Map Range for factor (0-1)
    map_range = nodes.new('ShaderNodeMapRange')
    map_range.inputs['From Min'].default_value = 0.0
    map_range.inputs['From Max'].default_value = 1.0
    map_range.inputs['To Min'].default_value = 0.0
    map_range.inputs['To Max'].default_value = 1.0

    # Calculate interpolation factor
    links.new(index.outputs['Index'], math_divide.inputs[0])
    links.new(group_input.outputs['Count'], math_subtract.inputs[0])
    links.new(math_subtract.outputs['Value'], math_max.inputs[0])
    links.new(math_max.outputs['Value'], math_divide.inputs[1])
    links.new(math_divide.outputs['Value'], map_range.inputs['Value'])

    return index, map_range.outputs['Result']


def _setup_interpolation_mixing(nodes, links, group_input, interpolation_factor):
    """Setup scale and rotation interpolation mixing"""
    # Mix nodes for interpolation
    mix_scale = nodes.new('ShaderNodeMix')
    mix_scale.data_type = 'VECTOR'
    mix_scale.clamp_factor = True

    mix_rotation = nodes.new('ShaderNodeMix')
    mix_rotation.data_type = 'VECTOR'
    mix_rotation.clamp_factor = True

    # Scale interpolation
    links.new(group_input.outputs['Scale Start'], mix_scale.inputs['A'])
    links.new(group_input.outputs['Scale End'], mix_scale.inputs['B'])
    links.new(interpolation_factor, mix_scale.inputs['Factor'])

    # Rotation interpolation
    links.new(group_input.outputs['Rotation Start'], mix_rotation.inputs['A'])
    links.new(group_input.outputs['Rotation End'], mix_rotation.inputs['B'])
    links.new(interpolation_factor, mix_rotation.inputs['Factor'])

    return mix_scale.outputs['Result'], mix_rotation.outputs['Result']


def _setup_random_transforms(nodes, links, group_input, index):
    """Setup random transform nodes"""
    # Random value nodes
    random_position = nodes.new('FunctionNodeRandomValue')
    random_position.data_type = 'FLOAT_VECTOR'

    random_rotation = nodes.new('FunctionNodeRandomValue')
    random_rotation.data_type = 'FLOAT_VECTOR'

    random_scale = nodes.new('FunctionNodeRandomValue')
    random_scale.data_type = 'FLOAT'

    # Vector math for negative ranges (to center the random range around 0)
    vector_math_neg_pos = nodes.new('ShaderNodeVectorMath')
    vector_math_neg_pos.operation = 'MULTIPLY'
    vector_math_neg_pos.inputs[1].default_value = (-1.0, -1.0, -1.0)

    vector_math_neg_rot = nodes.new('ShaderNodeVectorMath')
    vector_math_neg_rot.operation = 'MULTIPLY'
    vector_math_neg_rot.inputs[1].default_value = (-1.0, -1.0, -1.0)

    math_neg_scale = nodes.new('ShaderNodeMath')
    math_neg_scale.operation = 'MULTIPLY'
    math_neg_scale.inputs[1].default_value = -1.0

    # For combining random scale into vector
    combine_xyz_scale = nodes.new('ShaderNodeCombineXYZ')

    # Random values setup
    links.new(group_input.outputs['Random Seed'], random_position.inputs['Seed'])
    links.new(group_input.outputs['Random Seed'], random_rotation.inputs['Seed'])
    links.new(group_input.outputs['Random Seed'], random_scale.inputs['Seed'])

    links.new(index.outputs['Index'], random_position.inputs['ID'])
    links.new(index.outputs['Index'], random_rotation.inputs['ID'])
    links.new(index.outputs['Index'], random_scale.inputs['ID'])

    # Random position range
    links.new(group_input.outputs['Random Position'], vector_math_neg_pos.inputs[0])
    links.new(vector_math_neg_pos.outputs['Vector'], random_position.inputs['Min'])
    links.new(group_input.outputs['Random Position'], random_position.inputs['Max'])

    # Random rotation range
    links.new(group_input.outputs['Random Rotation'], vector_math_neg_rot.inputs[0])
    links.new(vector_math_neg_rot.outputs['Vector'], random_rotation.inputs['Min'])
    links.new(group_input.outputs['Random Rotation'], random_rotation.inputs['Max'])

    # Random scale range
    links.new(group_input.outputs['Random Scale'], math_neg_scale.inputs[0])
    links.new(math_neg_scale.outputs['Value'], random_scale.inputs['Min'])
    links.new(group_input.outputs['Random Scale'], random_scale.inputs['Max'])

    # Convert single random scale to vector
    links.new(random_scale.outputs['Value'], combine_xyz_scale.inputs['X'])
    links.new(random_scale.outputs['Value'], combine_xyz_scale.inputs['Y'])
    links.new(random_scale.outputs['Value'], combine_xyz_scale.inputs['Z'])

    return {
        'position': random_position.outputs['Value'],
        'rotation': random_rotation.outputs['Value'],
        'scale': combine_xyz_scale.outputs['Vector']
    }


def _setup_instancing_logic(nodes, links, group_input, mesh_line, index, random_nodes):
    """Setup instancing with pick random option"""
    # Instance on points
    instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')

    # --- Pick Random Instance Logic ---
    pick_instance_random = nodes.new('GeometryNodeInstanceOnPoints')
    pick_instance_random.name = "Pick Random Instance"

    # Random Index for picking instance
    random_instance_index = nodes.new('FunctionNodeRandomValue')
    random_instance_index.data_type = 'INT'

    # Switch between normal instancing and random pick instancing
    switch_instancing = nodes.new('GeometryNodeSwitch')
    switch_instancing.name = "Switch Instance Mode"
    switch_instancing.input_type = 'GEOMETRY'

    # Basic instancing setup
    links.new(mesh_line.outputs['Mesh'], instance_on_points.inputs['Points'])
    links.new(group_input.outputs['Instance Source'], instance_on_points.inputs['Instance'])

    # Pick random instance setup
    links.new(group_input.outputs['Random Seed'], random_instance_index.inputs['Seed'])
    links.new(index.outputs['Index'], random_instance_index.inputs['ID'])
    links.new(mesh_line.outputs['Mesh'], pick_instance_random.inputs['Points'])
    links.new(group_input.outputs['Instance Source'], pick_instance_random.inputs['Instance'])

    # Switch between normal instancing and random pick instancing
    links.new(group_input.outputs['Pick Random Instance'], switch_instancing.inputs['Switch'])
    links.new(instance_on_points.outputs['Instances'], switch_instancing.inputs[False])
    links.new(pick_instance_random.outputs['Instances'], switch_instancing.inputs[True])

    return switch_instancing.outputs['Output']


def _apply_all_transforms(nodes, links, instances_input, interpolated_scale, interpolated_rotation, random_nodes):
    """Apply all transforms to instances"""
    # Add vector math nodes for combining interpolated and random values
    add_random_rotation = nodes.new('ShaderNodeVectorMath')
    add_random_rotation.operation = 'ADD'

    add_random_scale = nodes.new('ShaderNodeVectorMath')
    add_random_scale.operation = 'ADD'

    # Transform instances
    set_position = nodes.new('GeometryNodeSetPosition')
    rotate_instances = nodes.new('GeometryNodeRotateInstances')
    scale_instances = nodes.new('GeometryNodeScaleInstances')

    # Apply transforms
    links.new(instances_input, set_position.inputs['Geometry'])
    links.new(random_nodes['position'], set_position.inputs['Offset'])

    # Apply rotation (base interpolated + random)
    links.new(set_position.outputs['Geometry'], rotate_instances.inputs['Instances'])
    links.new(interpolated_rotation, add_random_rotation.inputs[0])
    links.new(random_nodes['rotation'], add_random_rotation.inputs[1])
    links.new(add_random_rotation.outputs['Vector'], rotate_instances.inputs['Rotation'])

    # Apply scale (base interpolated + random)
    links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
    links.new(interpolated_scale, add_random_scale.inputs[0])
    links.new(random_nodes['scale'], add_random_scale.inputs[1])
    links.new(add_random_scale.outputs['Vector'], scale_instances.inputs['Scale'])

    return scale_instances.outputs['Instances']
