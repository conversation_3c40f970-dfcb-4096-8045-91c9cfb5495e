"""
Effector management system for Advanced Cloners addon.

This module provides a comprehensive system for managing effectors in cloners,
including application, linking, connection management, and synchronization.

The module is divided into specialized components:
- connection_management: Safe node connections and link restoration
- update_synchronization: Main update logic and anti-recursion support
- effector_linking: Effector chain creation and insertion point management
- effector_application: Parameter copying and effector application logic
- stacked_cloner_integration: Specialized logic for stacked cloners
"""

# Import main update functions
from .update_synchronization import (
    update_cloner_with_effectors,
    update_standard_cloner_with_effectors,
    get_effector_modifiers,
    restore_realize_node_for_anti_recursion
)

# Import connection management utilities
from .connection_management import (
    safe_link_new,
    restore_direct_connection_improved,
    restore_anti_recursion_connections,
    restore_direct_output_connection,
    restore_connections_bypassing_realize,
    restore_direct_connection
)

# Import effector linking functionality
from .effector_linking import (
    link_effector_to_cloner,
    unlink_effector_from_cloner,
    replace_problematic_nodes_with_effectors,
    create_standard_effector_chain,
    find_effector_insertion_point,
    setup_anti_recursion_true_path
)

# Import effector application utilities
from .effector_application import (
    apply_effector_to_cloner,
    copy_effector_parameters,
    apply_effector_to_stacked_cloner
)

# Import stacked cloner integration
from .stacked_cloner_integration import (
    update_stacked_cloner_with_effectors,
    validate_stacked_cloner_structure,
    ensure_stacked_cloner_consistency,
    activate_stacked_cloner_effector_mode,
    get_stacked_cloner_info,
    cleanup_stacked_cloner_effectors,
    get_stacked_cloner_effector_chain,
    update_stacked_cloner_effector_parameters
)

# Public API - functions that should be available for import
__all__ = [
    # Main update functions
    'update_cloner_with_effectors',
    'update_standard_cloner_with_effectors',
    'get_effector_modifiers',
    'restore_realize_node_for_anti_recursion',

    # Connection management
    'safe_link_new',
    'restore_direct_connection_improved',
    'restore_direct_connection',  # Backward compatibility alias
    'restore_anti_recursion_connections',
    'restore_direct_output_connection',
    'restore_connections_bypassing_realize',

    # Effector linking
    'link_effector_to_cloner',
    'unlink_effector_from_cloner',
    'replace_problematic_nodes_with_effectors',
    'create_standard_effector_chain',
    'find_effector_insertion_point',
    'setup_anti_recursion_true_path',

    # Effector application
    'apply_effector_to_cloner',
    'copy_effector_parameters',
    'apply_effector_to_stacked_cloner',

    # Stacked cloner integration
    'update_stacked_cloner_with_effectors',
    'validate_stacked_cloner_structure',
    'ensure_stacked_cloner_consistency',
    'activate_stacked_cloner_effector_mode',
    'get_stacked_cloner_info',
    'cleanup_stacked_cloner_effectors',
    'get_stacked_cloner_effector_chain',
    'update_stacked_cloner_effector_parameters'
]


def register():
    """Register the effector management system."""
    print("Effector management system loaded")


def unregister():
    """Unregister the effector management system."""
    print("Effector management system unloaded")
