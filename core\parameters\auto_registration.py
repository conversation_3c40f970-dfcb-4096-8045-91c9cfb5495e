"""
Automatic registration of parameter definitions.

This module handles the automatic registration of all parameter definitions
when the addon is loaded, ensuring they are available for use by components.
"""

from .parameter_registry import parameter_registry


def register_all_parameter_definitions():
    """
    Register all parameter definitions from config/parameters.

    This function imports and registers parameter definitions for all
    component types, making them available through the parameter registry.
    """
    try:
        print("Registering parameter definitions...")

        # Register cloner parameters
        _register_cloner_parameters()

        # Register effector parameters
        _register_effector_parameters()

        # TODO: Register field parameters
        # _register_field_parameters()

        # Print statistics
        stats = parameter_registry.get_statistics()
        print(f"Parameter registration complete: {stats}")

    except Exception as e:
        print(f"Error during parameter registration: {e}")


def _register_cloner_parameters():
    """Register all cloner parameter definitions."""
    try:
        # Import and register Circle Cloner parameters
        from ...config.parameters.cloners.circle_cloner import CIRCLE_CLONER_PARAMETERS
        parameter_registry.register_parameter_set(CIRCLE_CLONER_PARAMETERS)

        # Import and register Grid Cloner parameters
        from ...config.parameters.cloners.grid_cloner import GRID_CLONER_PARAMETERS
        parameter_registry.register_parameter_set(GRID_CLONER_PARAMETERS)

        # Import and register Linear Cloner parameters
        from ...config.parameters.cloners.linear_cloner import LINEAR_CLONER_PARAMETERS
        parameter_registry.register_parameter_set(LINEAR_CLONER_PARAMETERS)

        # Import and register Spiral Cloner parameters
        from ...config.parameters.cloners.spiral_cloner import SPIRAL_CLONER_PARAMETERS
        parameter_registry.register_parameter_set(SPIRAL_CLONER_PARAMETERS)

        # TODO: Add other cloners as they are migrated

    except Exception as e:
        print(f"Error registering cloner parameters: {e}")


def _register_effector_parameters():
    """Register all effector parameter definitions."""
    try:
        # Import and register Random Effector parameters
        from ...config.parameters.effectors.random_effector_params import RANDOM_EFFECTOR_PARAMETERS
        parameter_registry.register_parameter_set(RANDOM_EFFECTOR_PARAMETERS)
        
        # Import and register Noise Effector parameters
        from ...config.parameters.effectors.noise_effector_params import NOISE_EFFECTOR_PARAMETERS
        parameter_registry.register_parameter_set(NOISE_EFFECTOR_PARAMETERS)

        # TODO: Add other effectors as they are migrated

    except Exception as e:
        print(f"Error registering effector parameters: {e}")


def _register_field_parameters():
    """Register all field parameter definitions."""
    # TODO: Implement when field parameters are defined
    pass


def unregister_all_parameter_definitions():
    """
    Unregister all parameter definitions.

    This function clears the parameter registry when the addon is unloaded.
    """
    try:
        print("Unregistering parameter definitions...")
        parameter_registry.clear_all()
        print("Parameter definitions unregistered")

    except Exception as e:
        print(f"Error during parameter unregistration: {e}")
