"""
Core classes for defining component parameters in a declarative way.

This module provides the foundation for the unified parameter system,
allowing components to define their parameters once and have them
automatically used for interface creation, value setting, and UI generation.
"""

from typing import Any, Dict, List, Optional, Union, Callable
from enum import Enum
from dataclasses import dataclass, field


class ParameterType(Enum):
    """Supported parameter types for Blender node interfaces."""
    INT = "NodeSocketInt"
    FLOAT = "NodeSocketFloat" 
    VECTOR = "NodeSocketVector"
    BOOL = "NodeSocketBool"
    STRING = "NodeSocketString"
    OBJECT = "NodeSocketObject"
    COLLECTION = "NodeSocketCollection"
    GEOMETRY = "NodeSocketGeometry"


class ParameterSubtype(Enum):
    """Subtypes for parameters (used for vectors, etc.)."""
    NONE = None
    EULER = "EULER"
    TRANSLATION = "TRANSLATION"
    DIRECTION = "DIRECTION"
    VELOCITY = "VELOCITY"
    ACCELERATION = "ACCELERATION"
    XYZ = "XYZ"
    COLOR = "COLOR"


@dataclass
class ParameterDefinition:
    """
    Defines a single parameter for a component.
    
    This class encapsulates all information needed to create a parameter
    in the node interface, set its default value, and display it in UI.
    """
    name: str
    param_type: ParameterType
    default_value: Any
    description: str = ""
    
    # Constraints
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    
    # UI properties
    subtype: ParameterSubtype = ParameterSubtype.NONE
    ui_name: Optional[str] = None  # Display name in UI (if different from name)
    ui_group: Optional[str] = None  # UI group for organization
    ui_order: int = 0  # Order within UI group
    
    # Advanced properties
    is_input: bool = True  # True for input, False for output
    is_required: bool = True  # Whether parameter is required
    is_hidden: bool = False  # Whether to hide in UI
    
    # Validation and processing
    validator: Optional[Callable[[Any], bool]] = None
    processor: Optional[Callable[[Any], Any]] = None  # Transform value before setting
    
    def __post_init__(self):
        """Validate parameter definition after creation."""
        if not self.ui_name:
            self.ui_name = self.name
            
        # Validate default value type
        if not self._validate_default_value():
            raise ValueError(f"Invalid default value {self.default_value} for type {self.param_type}")
    
    def _validate_default_value(self) -> bool:
        """Validate that default value matches parameter type."""
        if self.param_type == ParameterType.INT:
            return isinstance(self.default_value, int)
        elif self.param_type == ParameterType.FLOAT:
            return isinstance(self.default_value, (int, float))
        elif self.param_type == ParameterType.VECTOR:
            return (isinstance(self.default_value, (list, tuple)) and 
                   len(self.default_value) == 3 and
                   all(isinstance(x, (int, float)) for x in self.default_value))
        elif self.param_type == ParameterType.BOOL:
            return isinstance(self.default_value, bool)
        elif self.param_type == ParameterType.STRING:
            return isinstance(self.default_value, str)
        else:
            # For complex types (Object, Collection, Geometry), allow None or appropriate types
            return True
    
    def get_blender_socket_type(self) -> str:
        """Get the Blender socket type string."""
        return self.param_type.value
    
    def get_ui_display_name(self) -> str:
        """Get the name to display in UI."""
        return self.ui_name or self.name
    
    def apply_constraints_to_socket(self, socket):
        """Apply min/max constraints to a Blender socket."""
        if hasattr(socket, 'min_value') and self.min_value is not None:
            socket.min_value = self.min_value
        if hasattr(socket, 'max_value') and self.max_value is not None:
            socket.max_value = self.max_value
        if hasattr(socket, 'subtype') and self.subtype != ParameterSubtype.NONE:
            socket.subtype = self.subtype.value


@dataclass 
class ParameterGroup:
    """
    Defines a group of related parameters.
    
    Groups help organize parameters both in the interface and in the UI.
    """
    name: str
    parameters: List[ParameterDefinition]
    description: str = ""
    ui_expanded: bool = True  # Whether group is expanded by default in UI
    ui_order: int = 0  # Order of group in UI
    
    def get_parameter(self, name: str) -> Optional[ParameterDefinition]:
        """Get a parameter by name from this group."""
        for param in self.parameters:
            if param.name == name:
                return param
        return None
    
    def get_parameter_names(self) -> List[str]:
        """Get list of all parameter names in this group."""
        return [param.name for param in self.parameters]


@dataclass
class ComponentParameterSet:
    """
    Complete parameter set for a component (cloner, effector, or field).
    
    This is the main class that defines all parameters for a component,
    organized into logical groups.
    """
    component_type: str  # 'CLONER', 'EFFECTOR', 'FIELD'
    component_id: str    # 'GRID', 'RANDOM', 'SPHERE', etc.
    groups: List[ParameterGroup]
    
    # Metadata
    version: str = "1.0"
    description: str = ""
    
    def get_group(self, name: str) -> Optional[ParameterGroup]:
        """Get a parameter group by name."""
        for group in self.groups:
            if group.name == name:
                return group
        return None
    
    def get_parameter(self, name: str) -> Optional[ParameterDefinition]:
        """Get a parameter by name from any group."""
        for group in self.groups:
            param = group.get_parameter(name)
            if param:
                return param
        return None
    
    def get_all_parameters(self) -> List[ParameterDefinition]:
        """Get all parameters from all groups."""
        all_params = []
        for group in self.groups:
            all_params.extend(group.parameters)
        return all_params
    
    def get_parameter_names(self) -> List[str]:
        """Get list of all parameter names."""
        return [param.name for param in self.get_all_parameters()]
    
    def get_input_parameters(self) -> List[ParameterDefinition]:
        """Get only input parameters."""
        return [param for param in self.get_all_parameters() if param.is_input]
    
    def get_output_parameters(self) -> List[ParameterDefinition]:
        """Get only output parameters."""
        return [param for param in self.get_all_parameters() if not param.is_input]
