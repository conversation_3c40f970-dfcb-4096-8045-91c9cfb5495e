"""
Cloner UI operators module.

This module contains all UI operators related to cloner functionality,
including effector management, chain operations, and cloner controls.
"""

# Import all cloner operators from the main module
from .cloner_ui_ops import (
    CLONER_OT_add_effector,
    CL<PERSON>ER_OT_remove_effector,
    C<PERSON><PERSON>ER_OT_update_active_collection,
    CLONER_OT_toggle_expanded,
    CLONER_OT_link_effector,
    CLONER_OT_unlink_effector,
    CLONER_OT_add_effector_to_cloner,
    CLONER_OT_set_active_in_chain,
    CLONER_OT_refresh_effector
)

# Public API
__all__ = [
    'CLONER_OT_add_effector',
    'C<PERSON><PERSON>ER_OT_remove_effector',
    'C<PERSON><PERSON>ER_OT_update_active_collection',
    'CLONER_OT_toggle_expanded',
    'CLONER_OT_link_effector',
    '<PERSON>L<PERSON>ER_OT_unlink_effector',
    '<PERSON><PERSON><PERSON>ER_OT_add_effector_to_cloner',
    '<PERSON>L<PERSON>ER_OT_set_active_in_chain',
    'CLONER_OT_refresh_effector'
]


def register():
    """Register cloner UI operators"""
    from . import cloner_ui_ops
    cloner_ui_ops.register()


def unregister():
    """Unregister cloner UI operators"""
    from . import cloner_ui_ops
    cloner_ui_ops.unregister()
