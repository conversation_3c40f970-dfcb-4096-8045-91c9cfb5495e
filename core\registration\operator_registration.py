"""
UI Operators registration module for Advanced Cloners addon.

Handles registration and unregistration of all UI operators
used by the addon for user interface interactions.
"""

import bpy

# Импортируем UI операторы из клонеров
from ...ui.operators.cloner_operators.cloner_ui_ops import (
    CLONER_OT_add_effector,
    CLONER_OT_remove_effector,
    CLONER_OT_update_active_collection,
    CLONER_OT_toggle_expanded,
    CLONER_OT_link_effector,
    CLONER_OT_unlink_effector,
    CLONER_OT_add_effector_to_cloner,
    CLONER_OT_set_active_in_chain,
    CLONER_OT_refresh_effector
)

# Импортируем UI операторы из эффекторов
from ...ui.operators.effector_operators.effector_ui_ops import (
    EFFECTOR_OT_toggle_expanded,
    EFFECTOR_OT_add_field,
    EFFECTOR_OT_remove_field,
    EFFECTOR_OT_auto_link,
    EFFECTOR_OT_update_stacked_cloners
)

# Импортируем UI операторы из полей
from ...ui.operators.field_operators.field_ui_ops import (
    FIELD_OT_toggle_expanded,
    FIELD_OT_create_field,  # UI версия с bl_idname = "object.create_field_ui"
    FIELD_OT_select_gizmo,
    FIELD_OT_create_sphere_gizmo,
    FIELD_OT_adjust_field_strength
)

# Диагностические операторы удалены - файлы fix_recursion.py и fix_recursion_improved.py больше не существуют
# from ...ui.operators.diagnostic_operators.fix_recursion import (
#     CLONER_OT_fix_recursion_depth,
#     CLONER_OT_update_all_effectors
# )
# from ...ui.operators.diagnostic_operators.fix_recursion_improved import CLONER_OT_fix_effector_issues


# Кортеж всех UI операторов для регистрации
ui_operators_classes = (
    # Операторы клонеров
    CLONER_OT_add_effector,
    CLONER_OT_remove_effector,
    CLONER_OT_update_active_collection,
    CLONER_OT_toggle_expanded,
    CLONER_OT_link_effector,
    CLONER_OT_unlink_effector,
    CLONER_OT_add_effector_to_cloner,
    CLONER_OT_set_active_in_chain,
    CLONER_OT_refresh_effector,

    # Диагностические операторы удалены - больше не регистрируются
    # CLONER_OT_fix_recursion_depth,       # Оператор для исправления проблем с рекурсией (УДАЛЕН)
    # CLONER_OT_update_all_effectors,      # Оператор для обновления всех клонеров с эффекторами (УДАЛЕН)
    # CLONER_OT_fix_effector_issues,       # Оператор для исправления проблем с эффекторами (УДАЛЕН)

    # Операторы эффекторов
    EFFECTOR_OT_toggle_expanded,
    EFFECTOR_OT_add_field,
    EFFECTOR_OT_remove_field,
    EFFECTOR_OT_auto_link,
    EFFECTOR_OT_update_stacked_cloners,

    # Операторы полей
    FIELD_OT_toggle_expanded,
    FIELD_OT_create_field,
    FIELD_OT_select_gizmo,
    FIELD_OT_create_sphere_gizmo,
    FIELD_OT_adjust_field_strength,
)


def register_ui_operators():
    """Register all UI operators used by the Advanced Cloners addon."""
    print("Registering Advanced Cloners UI operators...")

    for cls in ui_operators_classes:
        try:
            bpy.utils.register_class(cls)
        except Exception as e:
            print(f"Error registering operator {cls.__name__}: {e}")

    print("Advanced Cloners UI operators registered successfully")


def unregister_ui_operators():
    """Unregister all UI operators used by the Advanced Cloners addon."""
    print("Unregistering Advanced Cloners UI operators...")

    # Отменяем регистрацию в обратном порядке
    for cls in reversed(ui_operators_classes):
        try:
            bpy.utils.unregister_class(cls)
        except Exception as e:
            print(f"Error unregistering operator {cls.__name__}: {e}")

    print("Advanced Cloners UI operators unregistered successfully")
