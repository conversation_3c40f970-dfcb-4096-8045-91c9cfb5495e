"""
Linear Cloner Parameter Definitions

This module defines all parameters for the Linear Cloner component using the new
unified parameter system. These definitions are used for:
- Automatic interface creation
- Automatic value setting
- UI generation
- Documentation

Linear Cloner creates instances in a linear pattern with configurable offset and gradients.
"""

from ....core.parameters import (
    ParameterDefinition,
    ParameterGroup,
    ComponentParameterSet,
    ParameterType,
    get_standard_parameter_set
)


# Basic Linear Cloner parameters
LINEAR_BASIC_GROUP = ParameterGroup(
    name="basic",
    description="Basic linear cloner settings",
    ui_order=10,
    parameters=[
        ParameterDefinition(
            name="Count",
            param_type=ParameterType.INT,
            default_value=5,
            min_value=1,
            max_value=1000,
            description="Number of instances along the line",
            ui_group="Basic Settings",
            ui_order=1
        ),
        ParameterDefinition(
            name="Offset",
            param_type=ParameterType.VECTOR,
            default_value=(3.0, 0.0, 0.0),
            description="Offset between instances",
            ui_group="Basic Settings",
            ui_order=2
        )
    ]
)

# Linear клонер не имеет градиентных параметров - это простой линейный клонер

# Input/Output sockets - using standard cloner IO parameters
# LINEAR_IO_GROUP removed - now using get_standard_parameter_set("cloner_io")

# Complete parameter set for Linear Cloner
LINEAR_CLONER_PARAMETERS = ComponentParameterSet(
    component_type="CLONER",
    component_id="LINEAR",
    description="Linear Cloner parameter set - creates instances in a linear pattern",
    version="1.0",
    groups=[
        get_standard_parameter_set("cloner_io"),  # Using standard cloner IO parameters
        LINEAR_BASIC_GROUP,
        get_standard_parameter_set("global_transform"),
        get_standard_parameter_set("instance"),  # Instance transformations (Instance Rotation, Instance Scale)
        get_standard_parameter_set("random"),
        get_standard_parameter_set("effector_control")
    ]
)
