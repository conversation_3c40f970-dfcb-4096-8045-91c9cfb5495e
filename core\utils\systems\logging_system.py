"""
Централизованная система логирования для Advanced Cloners addon.
"""

import logging
import os
from pathlib import Path

class ClonerLogger:
    """Централизованный логгер для аддона"""
    
    _instance = None
    _logger = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._logger is None:
            self._setup_logger()
    
    def _setup_logger(self):
        """Настройка логгера"""
        self._logger = logging.getLogger('advanced_cloners')
        self._logger.setLevel(logging.DEBUG)
        
        # Создаем форматтер
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Консольный обработчик
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self._logger.addHandler(console_handler)
    
    def debug(self, message):
        self._logger.debug(message)
    
    def info(self, message):
        self._logger.info(message)
    
    def warning(self, message):
        self._logger.warning(message)
    
    def error(self, message):
        self._logger.error(message)
    
    def critical(self, message):
        self._logger.critical(message)

# Глобальный экземпляр логгера
logger = ClonerLogger()
