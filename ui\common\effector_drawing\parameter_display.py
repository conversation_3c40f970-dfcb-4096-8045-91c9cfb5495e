"""
Parameter display utilities for effector interfaces.

This module provides functions for displaying and organizing effector parameters
into logical groups and categories.
"""

import bpy
from ...common.ui_utils import display_socket_prop


def draw_effector_socket_parameters(layout, mod):
    """
    Отображает сокеты эффектора, организованные по категориям
    """
    # Собираем сокеты по категориям
    categories = _categorize_effector_sockets(mod)
    
    # Отображаем параметры по категориям
    if categories['enable_strength']:
        draw_effect_parameters(layout, mod, categories['enable_strength'])
    
    if categories['transform']:
        draw_transform_parameters(layout, mod, categories['transform'])
    
    if categories['field']:
        draw_field_parameters(layout, mod, categories['field'])
    
    if categories['other']:
        draw_other_parameters(layout, mod, categories['other'])


def draw_effect_parameters(layout, mod, socket_names):
    """
    Отображает параметры эффекта (Enable, Strength)
    """
    effect_box = layout.box()
    effect_box.label(text="Effect:", icon='FORCE_FORCE')
    
    for socket_name in socket_names:
        display_socket_prop(effect_box, mod, socket_name)


def draw_transform_parameters(layout, mod, socket_names):
    """
    Отображает параметры трансформации (Position, Rotation, Scale)
    """
    transform_box = layout.box()
    transform_box.label(text="Transform:", icon='ORIENTATION_GLOBAL')
    
    for socket_name in socket_names:
        display_socket_prop(transform_box, mod, socket_name)


def draw_field_parameters(layout, mod, socket_names):
    """
    Отображает параметры полей (Field, Use Field)
    """
    field_box = layout.box()
    field_box.label(text="Field:", icon='OUTLINER_OB_FORCE_FIELD')
    
    # Параметры полей
    for socket_name in socket_names:
        display_socket_prop(field_box, mod, socket_name)
    
    # Кнопки управления полем
    _draw_field_control_buttons(field_box, mod)


def draw_other_parameters(layout, mod, socket_names):
    """
    Отображает прочие параметры эффектора
    """
    other_box = layout.box()
    other_box.label(text="Other:", icon='PREFERENCES')
    
    for socket_name in socket_names:
        display_socket_prop(other_box, mod, socket_name)


def _categorize_effector_sockets(mod):
    """
    Категоризирует сокеты эффектора по типам
    
    Returns:
        dict: Словарь с категориями сокетов
    """
    categories = {
        'enable_strength': [],
        'transform': [],
        'field': [],
        'other': []
    }
    
    if not mod.node_group or not hasattr(mod.node_group, 'interface'):
        return categories
    
    for socket in mod.node_group.interface.items_tree:
        if socket.item_type != 'SOCKET' or socket.in_out != 'INPUT' or socket.name == "Geometry":
            continue
        
        name = socket.name
        
        # Распределяем параметры по категориям
        if name in ["Enable", "Strength"]:
            categories['enable_strength'].append(name)
        elif name in ["Position", "Rotation", "Scale", "Uniform Scale"]:
            categories['transform'].append(name)
        elif name in ["Field", "Use Field"]:
            categories['field'].append(name)
        else:
            categories['other'].append(name)
    
    return categories


def _draw_field_control_buttons(layout, mod):
    """
    Отображает кнопки управления полем эффектора
    """
    # Проверяем, использует ли эффектор поле
    using_field = mod.get("Use Field", False)
    
    if using_field:
        # Кнопка отключения поля
        layout.operator("object.effector_remove_field", text="Disconnect Field", 
                       icon='X').effector_name = mod.name
    else:
        # Кнопка подключения поля
        layout.operator("object.effector_add_field", text="Connect Field", 
                       icon='ADD').effector_name = mod.name


def display_effector_parameter(layout, mod, parameter_name, display_name=None, **kwargs):
    """
    Отображает отдельный параметр эффектора
    
    Args:
        layout: UI layout для отрисовки
        mod: Модификатор эффектора
        parameter_name (str): Имя параметра
        display_name (str, optional): Отображаемое имя
        **kwargs: Дополнительные аргументы
    """
    if not display_name:
        display_name = parameter_name
    
    return display_socket_prop(layout, mod, parameter_name, text=display_name, **kwargs)


def get_effector_parameter_value(mod, parameter_name):
    """
    Получает значение параметра эффектора
    
    Args:
        mod: Модификатор эффектора
        parameter_name (str): Имя параметра
        
    Returns:
        Значение параметра или None
    """
    from ...common.ui_utils import find_socket_by_name
    
    socket_id = find_socket_by_name(mod, parameter_name)
    if not socket_id:
        return None
    
    try:
        return mod[socket_id]
    except KeyError:
        return None


def set_effector_parameter_value(mod, parameter_name, value):
    """
    Устанавливает значение параметра эффектора
    
    Args:
        mod: Модификатор эффектора
        parameter_name (str): Имя параметра
        value: Новое значение
        
    Returns:
        bool: True если значение установлено успешно
    """
    from ...common.ui_utils import find_socket_by_name
    
    socket_id = find_socket_by_name(mod, parameter_name)
    if not socket_id:
        return False
    
    try:
        mod[socket_id] = value
        return True
    except (KeyError, TypeError):
        return False


def get_all_effector_parameters(mod):
    """
    Получает список всех параметров эффектора
    
    Args:
        mod: Модификатор эффектора
        
    Returns:
        list: Список имен параметров
    """
    parameters = []
    
    if not mod.node_group or not hasattr(mod.node_group, 'interface'):
        return parameters
    
    for socket in mod.node_group.interface.items_tree:
        if (socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and 
            socket.name != "Geometry"):
            parameters.append(socket.name)
    
    return parameters


def register():
    """Register parameter display components"""
    pass


def unregister():
    """Unregister parameter display components"""
    pass
