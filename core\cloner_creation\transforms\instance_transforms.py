"""
Унифицированные instance трансформации для всех типов клонеров.

Содержит логику instance трансформаций, которая используется
всеми типами клонеров (object, collection, stacked).

Все типы клонеров используют одинаковую логику instance трансформаций.
"""

import bpy
from typing import Optional

def apply_instance_rotation(nodes, links, instance_node, group_in,
                          mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет базовое вращение к инстансам.

    Унифицирует логику из object, collection и stacked модулей.
    Все типы клонеров используют одинаковую логику RotateInstances узла.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов
        group_in: Входной узел группы
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел RotateInstances с примененным вращением или None
    """
    try:
        # Создаем узел вращения инстансов
        # Логика одинакова для всех типов клонеров
        rotate_instances = nodes.new('GeometryNodeRotateInstances')

        # Устанавливаем позицию в зависимости от режима
        if mode == "object":
            # Из object_cloner_modules/transforms/instance_transforms.py (строка 31)
            rotate_instances.location = (100, 0)
        elif mode == "stacked":
            # Для stacked клонеров позиция устанавливается относительно входного узла
            rotate_instances.location = (instance_node.location.x + 100, instance_node.location.y)
        elif mode == "collection":
            # Для collection клонеров
            rotate_instances.location = (200, 0)
        else:
            # Дефолтная позиция
            rotate_instances.location = (100, 0)

        # Подключаем инстансы и вращение
        # Логика одинакова для всех типов клонеров
        links.new(instance_node.outputs['Instances'], rotate_instances.inputs['Instances'])
        links.new(group_in.outputs['Instance Rotation'], rotate_instances.inputs['Rotation'])

        return rotate_instances

    except Exception as e:
        print(f"Ошибка при применении вращения инстансов ({mode}): {e}")
        return instance_node

def apply_instance_scale(nodes, links, instance_node, group_in,
                       mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет базовый масштаб к инстансам.

    Унифицирует логику из object, collection и stacked модулей.
    Все типы клонеров используют одинаковую логику ScaleInstances узла.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов
        group_in: Входной узел группы
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел ScaleInstances с примененным масштабом или None
    """
    try:
        # Создаем узел масштабирования инстансов
        # Логика одинакова для всех типов клонеров
        scale_instances = nodes.new('GeometryNodeScaleInstances')

        # Устанавливаем позицию в зависимости от режима
        if mode == "object":
            # Из object_cloner_modules/transforms/instance_transforms.py (строка 58)
            scale_instances.location = (200, 0)
        elif mode == "stacked":
            # Для stacked клонеров позиция устанавливается относительно входного узла
            scale_instances.location = (instance_node.location.x + 200, instance_node.location.y)
        elif mode == "collection":
            # Для collection клонеров
            scale_instances.location = (300, 0)
        else:
            # Дефолтная позиция
            scale_instances.location = (200, 0)

        # Подключаем инстансы и масштаб
        # Логика одинакова для всех типов клонеров
        links.new(instance_node.outputs['Instances'], scale_instances.inputs['Instances'])
        links.new(group_in.outputs['Instance Scale'], scale_instances.inputs['Scale'])

        return scale_instances

    except Exception as e:
        print(f"Ошибка при применении масштаба инстансов ({mode}): {e}")
        return instance_node

def apply_circle_face_center_rotation(nodes, links, instance_node,
                                    mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет поворот к центру для Circle клонера (лицом внутрь).

    Специфичная функция для Circle клонеров всех типов.
    Сохраняет оригинальную логику из CircleCloner logic_builder.py.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел RotateInstances с примененным поворотом к центру или None
    """
    try:
        # Создаем узел поворота к центру
        # Логика одинакова для всех типов клонеров
        face_center = nodes.new('GeometryNodeRotateInstances')

        # Устанавливаем позицию в зависимости от режима
        if mode == "object":
            # Из object_cloner_modules/transforms/instance_transforms.py (строка 86)
            face_center.location = (50, 0)
        elif mode == "stacked":
            # Для stacked клонеров
            face_center.location = (instance_node.location.x + 50, instance_node.location.y)
        elif mode == "collection":
            # Для collection клонеров
            face_center.location = (150, 0)
        else:
            # Дефолтная позиция
            face_center.location = (50, 0)

        # Создаем узел для комбинирования углов поворота
        # Логика одинакова для всех типов клонеров
        combine_face_center = nodes.new('ShaderNodeCombineXYZ')
        combine_face_center.inputs[0].default_value = 0.0  # X
        combine_face_center.inputs[1].default_value = 0.0  # Y
        combine_face_center.inputs[2].default_value = 90.0  # Z - поворот на 90 градусов
        combine_face_center.location = (face_center.location.x - 100, face_center.location.y)

        # Подключаем узлы
        # Логика одинакова для всех типов клонеров
        links.new(instance_node.outputs['Instances'], face_center.inputs['Instances'])
        links.new(combine_face_center.outputs['Vector'], face_center.inputs['Rotation'])

        return face_center

    except Exception as e:
        print(f"Ошибка при применении поворота к центру ({mode}): {e}")
        return instance_node

def apply_instance_transforms(nodes, links, instance_node, group_in,
                            cloner_type: str = "LINEAR", mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Применяет базовые трансформации инстансов в зависимости от типа клонера.

    Унифицирует логику из object, collection и stacked модулей.
    Поддерживает все типы клонеров и все режимы.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов
        group_in: Входной узел группы
        cloner_type: Тип клонера (LINEAR, GRID, CIRCLE)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел с примененными трансформациями инстансов или None
    """
    try:
        current_node = instance_node

        # Для Circle клонера сначала применяем поворот к центру
        # Логика одинакова для всех режимов
        if cloner_type == "CIRCLE":
            current_node = apply_circle_face_center_rotation(nodes, links, current_node, mode)

        # Применяем базовое вращение инстансов
        # Логика одинакова для всех типов клонеров и режимов
        current_node = apply_instance_rotation(nodes, links, current_node, group_in, mode)

        # Применяем базовый масштаб инстансов
        # Логика одинакова для всех типов клонеров и режимов
        current_node = apply_instance_scale(nodes, links, current_node, group_in, mode)

        return current_node

    except Exception as e:
        print(f"Ошибка при применении трансформаций инстансов ({cloner_type}, {mode}): {e}")
        return instance_node

def create_instance_transforms(nodes, links, instance_node, group_in,
                             cloner_type: str = "LINEAR", mode: str = "object") -> Optional[bpy.types.Node]:
    """
    Создает и применяет instance трансформации.

    Алиас для apply_instance_transforms для обратной совместимости.

    Args:
        nodes: Коллекция узлов
        links: Коллекция связей
        instance_node: Узел инстансов
        group_in: Входной узел группы
        cloner_type: Тип клонера (LINEAR, GRID, CIRCLE)
        mode: Режим клонера ("object", "collection", "stacked")

    Returns:
        Node: Узел с примененными трансформациями инстансов или None
    """
    return apply_instance_transforms(nodes, links, instance_node, group_in, cloner_type, mode)
