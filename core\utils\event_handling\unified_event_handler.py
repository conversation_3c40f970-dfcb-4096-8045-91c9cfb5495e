"""
Объединенный высокопроизводительный обработчик событий.

Заменяет множественные обработчики одним оптимизированным,
который эффективно обрабатывает все типы событий клонеров.
"""

import bpy
import time
from typing import Set, Dict, Any
from ..optimization import PerformanceTimer

# Глобальные переменные для оптимизации
_last_update_time = 0.0
_update_cooldown = 0.1  # Минимальный интервал между обновлениями (100ms)
_handler_blocked = False
_processed_updates: Set[str] = set()

# Кэш для быстрой идентификации типов модификаторов
_modifier_type_cache: Dict[str, str] = {}
_object_type_cache: Dict[str, bool] = {}


class UnifiedEventHandler:
    """Объединенный обработчик всех событий клонеров"""

    @staticmethod
    @bpy.app.handlers.persistent
    def depsgraph_update_handler(scene, depsgraph):
        """
        Единый обработчик обновлений depsgraph.
        Заменяет все отдельные обработчики для максимальной производительности.
        """
        global _last_update_time, _handler_blocked, _processed_updates

        # Защита от слишком частых обновлений
        current_time = time.time()
        if current_time - _last_update_time < _update_cooldown:
            return

        # Защита от рекурсивных вызовов
        if _handler_blocked:
            return

        _handler_blocked = True

        try:
            with PerformanceTimer("unified_event_handler"):
                # Быстрая предварительная проверка
                if not depsgraph.id_type_updated('OBJECT'):
                    return

                # Собираем все изменения за один проход
                changes = UnifiedEventHandler._collect_changes(depsgraph)

                if changes:
                    # Обрабатываем изменения пакетно
                    UnifiedEventHandler._process_changes_batch(scene, changes)

                    # Обновляем время последнего обновления
                    _last_update_time = current_time

                    # Очищаем кэш обработанных обновлений
                    _processed_updates.clear()

        except Exception as e:
            print(f"[ERROR] UnifiedEventHandler: {e}")

        finally:
            _handler_blocked = False

    @staticmethod
    def _collect_changes(depsgraph) -> Dict[str, Any]:
        """
        Собирает все изменения за один проход по depsgraph.
        Возвращает структурированную информацию об изменениях.
        """
        changes = {
            'cloner_changes': [],
            'effector_changes': [],
            'collection_changes': [],
            'selection_changes': []
        }

        # Оптимизированный проход по обновлениям
        for update in depsgraph.updates:
            if not hasattr(update.id, 'original'):
                continue

            obj = update.id.original

            # Быстрая проверка типа объекта с кэшированием
            if not UnifiedEventHandler._is_object_cached(obj):
                continue

            # Анализируем изменения в модификаторах
            UnifiedEventHandler._analyze_object_changes(obj, changes)

        return changes

    @staticmethod
    def _is_object_cached(obj) -> bool:
        """Кэшированная проверка типа объекта"""
        obj_id = str(id(obj))

        if obj_id not in _object_type_cache:
            # Проверяем, что это Object с модификаторами
            is_valid = (isinstance(obj, bpy.types.Object) and
                       hasattr(obj, 'modifiers') and
                       obj.type == 'MESH')
            _object_type_cache[obj_id] = is_valid

            # Ограничиваем размер кэша
            if len(_object_type_cache) > 1000:
                # Очищаем половину кэша
                keys_to_remove = list(_object_type_cache.keys())[:500]
                for key in keys_to_remove:
                    del _object_type_cache[key]

        return _object_type_cache[obj_id]

    @staticmethod
    def _analyze_object_changes(obj, changes):
        """Анализирует изменения в объекте и его модификаторах"""
        try:
            obj_name = obj.name

            # Проверяем, что объект имеет модификаторы (только Object)
            if not hasattr(obj, 'modifiers'):
                return

            # Проверяем изменения в модификаторах
            for mod in obj.modifiers:
                mod_key = f"{obj_name}__{mod.name}"

                # Избегаем дублирования обработки
                if mod_key in _processed_updates:
                    continue

                mod_type = UnifiedEventHandler._get_modifier_type_cached(mod)

                if mod_type == 'CLONER':
                    changes['cloner_changes'].append((obj, mod))
                    _processed_updates.add(mod_key)
                elif mod_type == 'EFFECTOR':
                    changes['effector_changes'].append((obj, mod))
                    _processed_updates.add(mod_key)

            # Проверяем изменения выделения
            if obj == bpy.context.active_object:
                changes['selection_changes'].append(obj)

        except Exception as e:
            # Тихо игнорируем ошибки для объектов без модификаторов
            pass

    @staticmethod
    def _get_modifier_type_cached(mod) -> str:
        """Кэшированное определение типа модификатора"""
        mod_id = str(id(mod))

        if mod_id not in _modifier_type_cache:
            mod_type = 'UNKNOWN'

            if mod.type == 'NODES' and mod.node_group:
                # Быстрая проверка через метаданные
                component_type = mod.node_group.get("component_type")
                if component_type in ['CLONER', 'EFFECTOR', 'FIELD']:
                    mod_type = component_type
                else:
                    # Fallback на проверку по имени
                    node_group_name = mod.node_group.name
                    if any(name in node_group_name for name in ['Cloner', 'Grid', 'Linear', 'Circle']):
                        mod_type = 'CLONER'
                    elif any(name in node_group_name for name in ['Effector', 'Random', 'Noise']):
                        mod_type = 'EFFECTOR'
                    elif any(name in node_group_name for name in ['Field', 'Sphere']):
                        mod_type = 'FIELD'

            _modifier_type_cache[mod_id] = mod_type

            # Ограничиваем размер кэша
            if len(_modifier_type_cache) > 1000:
                keys_to_remove = list(_modifier_type_cache.keys())[:500]
                for key in keys_to_remove:
                    del _modifier_type_cache[key]

        return _modifier_type_cache[mod_id]

    @staticmethod
    def _process_changes_batch(scene, changes):
        """Обрабатывает все изменения пакетно для максимальной эффективности"""

        # Обрабатываем изменения клонеров
        if changes['cloner_changes']:
            UnifiedEventHandler._process_cloner_changes(scene, changes['cloner_changes'])

        # Обрабатываем изменения эффекторов
        if changes['effector_changes']:
            UnifiedEventHandler._process_effector_changes(scene, changes['effector_changes'])

        # Обрабатываем изменения выделения
        if changes['selection_changes']:
            UnifiedEventHandler._process_selection_changes(scene, changes['selection_changes'])

        # Обрабатываем изменения коллекций
        if changes['collection_changes']:
            UnifiedEventHandler._process_collection_changes(scene, changes['collection_changes'])

    @staticmethod
    def _process_cloner_changes(_scene, cloner_changes):
        """Обрабатывает изменения в клонерах"""
        # Объединяем логику из cloner_chain_update_handler и ClonerChainUpdateHandler
        for obj, mod in cloner_changes:
            try:
                # Обновляем цепочку клонеров
                UnifiedEventHandler._update_cloner_chain(obj, mod)

                # Синхронизируем параметры в цепочке
                UnifiedEventHandler._sync_cloner_parameters(obj, mod)

            except Exception as e:
                print(f"[ERROR] Processing cloner change for {obj.name}.{mod.name}: {e}")

    @staticmethod
    def _process_effector_changes(_scene, effector_changes):
        """Обрабатывает изменения в эффекторах"""
        # Объединяем логику из effector_parameter_update_handler
        for obj, mod in effector_changes:
            try:
                # Обновляем связанные клонеры
                UnifiedEventHandler._update_linked_cloners(obj, mod)

            except Exception as e:
                print(f"[ERROR] Processing effector change for {obj.name}.{mod.name}: {e}")

    @staticmethod
    def _process_selection_changes(scene, selection_changes):
        """Обрабатывает изменения выделения"""
        # Логика из cloner_chain_update_handler для отслеживания выделения
        for obj in selection_changes:
            try:
                UnifiedEventHandler._update_active_cloner_display(scene, obj)
            except Exception as e:
                print(f"[ERROR] Processing selection change for {obj.name}: {e}")

    @staticmethod
    def _process_collection_changes(scene, collection_changes):
        """Обрабатывает изменения коллекций"""
        # Логика из cloner_collection_update_handler
        for change in collection_changes:
            try:
                UnifiedEventHandler._update_collection_selection(scene, change)
            except Exception as e:
                print(f"[ERROR] Processing collection change: {e}")

    # Реализация методов обработки (перенесено из старых обработчиков)
    @staticmethod
    def _update_cloner_chain(_obj, mod):
        """Обновляет цепочку клонеров (из cloner_chain_update_handler)"""
        try:
            # Проверяем, есть ли следующие клонеры в цепочке
            if "next_cloners" in mod and mod["next_cloners"]:
                # Используем отложенное обновление для предотвращения блокировки
                def update_next_cloners():
                    # Проверяем, существует ли ключ next_cloners
                    if "next_cloners" not in mod:
                        return None

                    for next_cloner_name in mod["next_cloners"]:
                        if next_cloner_name in bpy.data.objects:
                            next_cloner_obj = bpy.data.objects[next_cloner_name]
                            # Минимальное обновление для запуска recalc
                            current_loc = next_cloner_obj.location.copy()
                            next_cloner_obj.location = current_loc

                    # Обновляем depsgraph
                    bpy.context.view_layer.update()
                    return None

                # Регистрируем таймер с небольшой задержкой
                if hasattr(bpy.app, "timers"):
                    bpy.app.timers.register(update_next_cloners, first_interval=0.05)

        except Exception as e:
            print(f"[ERROR] _update_cloner_chain: {e}")

    @staticmethod
    def _sync_cloner_parameters(obj, mod):
        """Синхронизирует параметры клонеров в цепочке (из ClonerChainUpdateHandler)"""
        try:
            # Проверяем, является ли это цепочечным клонером
            if not mod.get("is_chained_cloner"):
                return

            # Обновляем previous_cloner_object если нужно
            if obj.name.startswith("Cloner_"):
                # Проверяем все потенциальные объекты для обновления ссылок
                for potential_obj in bpy.data.objects:
                    if potential_obj.name.startswith("Cloner_"):
                        for potential_mod in potential_obj.modifiers:
                            if potential_mod.type == 'NODES' and potential_mod.get("is_chained_cloner"):
                                # Обновляем ссылки на предыдущий клонер
                                if potential_mod.get("previous_cloner_object") == obj.name:
                                    potential_mod["previous_cloner_object"] = obj.name

        except Exception as e:
            print(f"[ERROR] _sync_cloner_parameters: {e}")

    @staticmethod
    def _update_linked_cloners(_obj, mod):
        """Обновляет клонеры, связанные с эффектором (из effector_parameter_update_handler)"""
        try:
            # Поиск связанных клонеров
            UnifiedEventHandler._find_and_update_linked_cloners(mod)

            # Обновляем видимость сцены
            UnifiedEventHandler._update_scene_visibility()

        except Exception as e:
            print(f"[ERROR] _update_linked_cloners: {e}")

    @staticmethod
    def _update_active_cloner_display(scene, obj):
        """Обновляет отображение активного клонера (из cloner_chain_update_handler)"""
        try:
            # Проверяем, есть ли активный клонер в цепочке
            if not hasattr(scene, "active_cloner_in_chain") or not scene.active_cloner_in_chain:
                return

            # Получаем информацию об активном клонере
            active_cloner_info = scene.active_cloner_in_chain
            parts = active_cloner_info.split("|")
            if len(parts) != 2:
                return

            cloner_obj_name, _cloner_mod_name = parts

            # Проверяем, соответствует ли текущий объект активному клонеру
            if obj.name == cloner_obj_name:
                # Обновляем выделение объекта
                if obj != bpy.context.active_object:
                    try:
                        bpy.context.view_layer.objects.active = obj
                        obj.select_set(True)
                    except:
                        pass

        except Exception as e:
            print(f"[ERROR] _update_active_cloner_display: {e}")

    @staticmethod
    def _update_collection_selection(scene, _change):
        """Обновляет выбор коллекции (из cloner_collection_update_handler)"""
        try:
            # Проверяем, есть ли информация о последней созданной коллекции
            if hasattr(scene, "last_cloned_collection") and scene.last_cloned_collection:
                # Устанавливаем выбранную коллекцию
                scene.collection_to_clone = scene.last_cloned_collection
                # Сбрасываем сохраненное значение
                scene.last_cloned_collection = ""

                # Обновляем UI только в областях VIEW_3D
                UnifiedEventHandler._update_view3d_areas()

        except Exception as e:
            print(f"[ERROR] _update_collection_selection: {e}")

    # Вспомогательные методы
    @staticmethod
    def _find_and_update_linked_cloners(effector_mod):
        """Находит и обновляет клонеры, связанные с эффектором"""
        try:
            # Ищем все объекты с клонерами
            for obj in bpy.data.objects:
                for mod in obj.modifiers:
                    if mod.type == 'NODES' and mod.node_group:
                        # Проверяем, является ли это клонером
                        if UnifiedEventHandler._is_cloner_modifier(mod):
                            # ВАЖНО: Пропускаем клонеры коллекций
                            if UnifiedEventHandler._is_collection_cloner(mod):
                                print(f"[DEBUG] Пропускаем обновление клонера коллекции {mod.name} при изменении эффектора")
                                print(f"[DEBUG] Состояние клонера коллекции {mod.name}: viewport={mod.show_viewport}, render={mod.show_render}")
                                continue

                            # Обновляем только обычные клонеры
                            UnifiedEventHandler._update_cloner_modifier(obj, mod, effector_mod)

        except Exception as e:
            print(f"[ERROR] _find_and_update_linked_cloners: {e}")

    @staticmethod
    def _is_cloner_modifier(mod) -> bool:
        """Проверяет, является ли модификатор клонером"""
        if not mod.node_group:
            return False

        node_group_name = mod.node_group.name.lower()
        cloner_keywords = ['cloner', 'grid', 'linear', 'circle', 'spiral']
        return any(keyword in node_group_name for keyword in cloner_keywords)

    @staticmethod
    def _is_collection_cloner(mod) -> bool:
        """Проверяет, является ли модификатор клонером коллекции"""
        if not mod.node_group:
            return False

        # Проверяем по метаданным
        if "source_type" in mod and mod["source_type"] == "COLLECTION":
            return True

        if mod.node_group.get("is_collection_cloner", False):
            return True

        # Проверяем по имени node group
        node_group_name = mod.node_group.name.lower()
        collection_keywords = ['collectioncloner', 'collection_cloner']
        return any(keyword in node_group_name for keyword in collection_keywords)

    @staticmethod
    def _update_cloner_modifier(target_obj, target_mod, effector_mod):
        """Обновляет модификатор клонера"""
        try:
            # ВАЖНО: Не обновляем клонеры коллекций
            if UnifiedEventHandler._is_collection_cloner(target_mod):
                print(f"[DEBUG] Пропускаем обновление клонера коллекции {target_mod.name}")
                return

            # Проверяем, является ли клонер стековым
            is_stacked = target_mod.get("is_stacked_cloner", False) or target_mod.node_group.get("is_stacked_cloner", False)

            if is_stacked:
                # Применяем эффектор к стековому клонеру
                UnifiedEventHandler._apply_effector_to_stacked_cloner(target_obj, target_mod, effector_mod)

            # Принудительное обновление клонера (только для не-коллекционных)
            UnifiedEventHandler._force_update_cloner(target_obj, target_mod)

        except Exception as e:
            print(f"[ERROR] _update_cloner_modifier: {e}")

    @staticmethod
    def _apply_effector_to_stacked_cloner(target_obj, target_mod, effector_mod):
        """Применяет эффектор к стековому клонеру"""
        try:
            # Импортируем функцию применения эффектора
            from ..effector_management import apply_effector_to_stacked_cloner
            apply_effector_to_stacked_cloner(target_obj, target_mod, effector_mod)
        except Exception as e:
            print(f"[ERROR] _apply_effector_to_stacked_cloner: {e}")

    @staticmethod
    def _force_update_cloner(target_obj, _target_mod):
        """Принудительно обновляет клонер"""
        try:
            # Минимальное изменение для запуска обновления
            current_loc = target_obj.location.copy()
            target_obj.location = current_loc
        except Exception as e:
            print(f"[ERROR] _force_update_cloner: {e}")

    @staticmethod
    def _update_scene_visibility():
        """Обновляет видимость всей сцены"""
        try:
            bpy.context.view_layer.update()
        except Exception as e:
            print(f"[ERROR] _update_scene_visibility: {e}")

    @staticmethod
    def _update_view3d_areas():
        """Обновляет области VIEW_3D"""
        try:
            for area in bpy.context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
        except Exception as e:
            print(f"[ERROR] _update_view3d_areas: {e}")


def register_unified_handler():
    """Регистрирует объединенный обработчик событий"""
    if UnifiedEventHandler.depsgraph_update_handler not in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.append(UnifiedEventHandler.depsgraph_update_handler)
        print("Зарегистрирован объединенный обработчик событий")
    else:
        print("Объединенный обработчик событий уже зарегистрирован")


def unregister_unified_handler():
    """Отменяет регистрацию объединенного обработчика событий"""
    if UnifiedEventHandler.depsgraph_update_handler in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.remove(UnifiedEventHandler.depsgraph_update_handler)
        print("Отменена регистрация объединенного обработчика событий")
    else:
        print("Объединенный обработчик событий не был зарегистрирован")


def clear_handler_caches():
    """Очищает кэши обработчика"""
    global _modifier_type_cache, _object_type_cache, _processed_updates
    _modifier_type_cache.clear()
    _object_type_cache.clear()
    _processed_updates.clear()


def get_handler_stats():
    """Возвращает статистику обработчика"""
    return {
        "modifier_cache_size": len(_modifier_type_cache),
        "object_cache_size": len(_object_type_cache),
        "processed_updates": len(_processed_updates),
        "handler_blocked": _handler_blocked,
        "last_update_time": _last_update_time
    }
