"""
Модуль управления цепочками клонеров.

Содержит функции для отображения и управления цепочками клонеров,
включая активацию клонеров и редактирование их параметров.
"""

import bpy
from ...common.ui_utils import is_element_expanded
from ...common.cloner_drawing.settings_panels import (
    draw_collection_cloner_settings
    # Removed unused imports: draw_grid_cloner_settings, draw_linear_cloner_settings, draw_circle_cloner_settings
    # All cloners now use the unified draw_collection_cloner_settings function
)
# Removed unused import: draw_common_cloner_settings
# from ...common.cloner_drawing.common_controls import draw_common_cloner_settings

from ....core.registry import component_registry


def draw_cloner_chain(context, layout, obj, cloner_chain):
    """
    Отображает UI для цепочки клонеров.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
        obj: Активный объект
        cloner_chain: Цепочка клонеров
    """
    if not cloner_chain:
        return

    # Create a compact box for the chain
    box = layout.box()

    # Compact header
    header_row = box.row(align=True)
    header_row.scale_y = 1.1
    header_row.label(text="Cloner Chain", icon="LINKED")
    header_row.label(text=f"({len(cloner_chain)})", icon="DECORATE_OVERRIDE")

    # Get the active cloner in the chain from scene property
    active_in_chain = context.scene.active_cloner_in_chain

    # If chain is short (1-2 items), use a row, otherwise use a column
    is_short_chain = len(cloner_chain) <= 3
    if is_short_chain:
        # Horizontal layout for short chains
        draw_short_chain(context, box, cloner_chain, active_in_chain)
    else:
        # Vertical layout with compact buttons for longer chains
        draw_long_chain(context, box, cloner_chain, active_in_chain)

    # If there's an active cloner in the chain, draw its settings
    if active_in_chain:
        draw_active_cloner_settings(context, box, active_in_chain)


def draw_short_chain(context, layout, cloner_chain, active_in_chain):
    """
    Отображает короткую цепочку клонеров (до 3 элементов) в горизонтальном виде.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
        cloner_chain: Цепочка клонеров
        active_in_chain: Активный клонер в цепочке
    """
    # Horizontal layout for short chains
    chain_row = layout.row(align=True)
    chain_row.scale_y = 1.0

    for i, link in enumerate(cloner_chain):
        obj_name = link["object"]
        mod_name = link["modifier"]

        # Check if this is the active cloner in chain
        is_active = active_in_chain == f"{obj_name}|{mod_name}"

        # Add separator between items
        if i > 0:
            chain_row.label(text="→", icon="BLANK1")

        # Button to activate this cloner
        button = chain_row.operator(
            "object.set_cloner_active_in_chain",
            text=f"{mod_name.split('.')[0]}" if "." in mod_name else mod_name,
            icon="RADIOBUT_ON" if is_active else "RADIOBUT_OFF",
            depress=is_active
        )
        button.object_name = obj_name
        button.modifier_name = mod_name


def draw_long_chain(context, layout, cloner_chain, active_in_chain):
    """
    Отображает длинную цепочку клонеров (более 3 элементов) в вертикальном виде.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
        cloner_chain: Цепочка клонеров
        active_in_chain: Активный клонер в цепочке
    """
    # Vertical layout with compact buttons for longer chains
    chain_col = layout.column(align=True)

    for i, link in enumerate(cloner_chain):
        obj_name = link["object"]
        mod_name = link["modifier"]

        # Check if this is the active cloner in chain
        is_active = active_in_chain == f"{obj_name}|{mod_name}"

        # Определяем тип клонера
        is_collection = False
        is_object_cloner = False

        # Проверить тип клонера по имени группы узлов и свойствам
        if obj_name in bpy.data.objects and mod_name in bpy.data.objects[obj_name].modifiers:
            mod = bpy.data.objects[obj_name].modifiers[mod_name]
            if mod.node_group:
                if "CollectionCloner_" in mod.node_group.name or "original_collection" in mod:
                    is_collection = True
                elif "ObjectCloner_" in mod.node_group.name:
                    is_object_cloner = True
                # Treat standard object cloners as object cloners too
                # Check node_group_name for cloner prefixes
                else:
                    cloner_group_names = component_registry.get_cloner_group_names()
                    if any(prefix in mod.node_group.name for prefix in cloner_group_names.values()):
                        is_object_cloner = True
        # Также проверить по свойству из цепочки
        elif link.get("is_collection_cloner", False):
            is_collection = True
        elif link.get("is_chained_cloner", False) and not link.get("is_collection_cloner", False):
            is_object_cloner = True
        # Если не определен конкретный тип, но есть в цепочке - считаем объектным клонером по умолчанию
        elif not is_collection and not is_object_cloner:
            is_object_cloner = True

        row = chain_col.row(align=True)

        # Show index number and icon
        icon = "OUTLINER_COLLECTION" if is_collection else "OBJECT_DATA" if is_object_cloner else "OUTLINER_OB_MESH"
        prefix = f"{i+1}."
        row.label(text=prefix, icon=icon)

        # Button to activate this cloner - simplified text
        cloner_name = f"{mod_name.split('.')[0]}" if "." in mod_name else mod_name
        if is_collection:
            cloner_name += " (Collection)"
        elif is_object_cloner:
            cloner_name += " (Object)"

        op = row.operator(
            "object.set_cloner_active_in_chain",
            text=cloner_name,
            icon="RADIOBUT_ON" if is_active else "RADIOBUT_OFF",
            depress=is_active
        )
        op.object_name = obj_name
        op.modifier_name = mod_name


def draw_active_cloner_settings(context, layout, active_in_chain):
    """
    Отображает настройки активного клонера в цепочке.

    Args:
        context: Контекст Blender
        layout: Элемент UI для добавления интерфейса
        active_in_chain: Активный клонер в цепочке (формат "object_name|modifier_name")
    """
    parts = active_in_chain.split("|")
    if len(parts) == 2:
        active_obj_name, active_mod_name = parts

        # Only proceed if the object and modifier exist
        if active_obj_name in bpy.data.objects:
            active_obj = bpy.data.objects[active_obj_name]
            if active_mod_name in active_obj.modifiers:
                active_mod = active_obj.modifiers[active_mod_name]

                # Draw a separator
                layout.separator()

                # Draw the settings for this cloner
                settings_box = layout.box()
                col = settings_box.column()

                # Compact header showing what we're editing
                header_row = col.row()
                header_row.label(text=f"Editing: {active_mod_name}", icon="TOOL_SETTINGS")

                # Determine cloner type
                if active_mod.node_group:
                    node_group_name = active_mod.node_group.name
                    cloner_type = None
                    is_collection_cloner = False

                    # Check if this is a collection cloner
                    if "CollectionCloner_" in node_group_name:
                        is_collection_cloner = True
                        # Extract type (format: CollectionCloner_TYPE_name...)
                        parts = node_group_name.split('_')
                        if len(parts) > 1:
                            cloner_type = parts[1]  # GRID, LINEAR, CIRCLE
                    # Check if this is an object cloner - обрабатываем Object клонеры так же, как Collection
                    elif "ObjectCloner_" in node_group_name:
                        is_collection_cloner = True  # используем тот же вид UI, что и для коллекций
                        # Extract type (format: ObjectCloner_TYPE_name...)
                        parts = node_group_name.split('_')
                        if len(parts) > 1:
                            cloner_type = parts[1]  # GRID, LINEAR, CIRCLE
                    else:
                        # Check node_group_name for cloner prefixes
                        cloner_group_names = component_registry.get_cloner_group_names()
                        for c_type, prefix in cloner_group_names.items():
                            parts = node_group_name.split('.')
                            if parts[0] == prefix:
                                cloner_type = c_type
                                break

                    # Draw appropriate settings based on cloner type
                    if cloner_type:
                        col.separator()

                        # Use unified settings function for all cloner types
                        # All cloners now use the same draw_collection_cloner_settings function
                        # which internally calls generate_ui_from_parameters() with the correct parameter set
                        draw_collection_cloner_settings(col, active_mod, cloner_type)

                        # REMOVED: draw_common_cloner_settings() call to prevent parameter duplication
                        # The new automatic UI generation system already includes all common parameters:
                        # - Global Transform, Instance, Random, Effector Control
                        # col.separator()
                        # draw_common_cloner_settings(col, active_mod, context, is_chain_menu=True)
