"""
Универсальная фабрика компонентов, работающая через реестр.

Заменяет множественные проверки if/elif единой системой,
автоматически работающей с любыми зарегистрированными компонентами.
"""

import bpy
from typing import Optional, Dict, Any
from ..registry import component_registry
from ..utils.node_operations.node_utils import create_independent_node_group

class UniversalComponentFactory:
    """
    Универсальная фабрика для создания любых компонентов.
    Автоматически работает с любыми зарегистрированными клонерами, эффекторами и полями.
    """

    @classmethod
    def create_cloner(cls, cloner_type: str, use_custom_group: bool = True, **kwargs) -> Optional[bpy.types.NodeGroup]:
        """
        Создает группу узлов клонера указанного типа.

        Args:
            cloner_type: Тип клонера (например, 'GRID', 'LINEAR', 'CIRCLE', 'SPIRAL')
            use_custom_group: Использовать ли уникальную группу для каждого экземпляра
            **kwargs: Дополнительные параметры для создания клонера

        Returns:
            Созданная группа узлов или None, если создание не удалось
        """
        return cls._create_component('CLONER', cloner_type, use_custom_group, **kwargs)

    @classmethod
    def create_effector(cls, effector_type: str, use_custom_group: bool = True, **kwargs) -> Optional[bpy.types.NodeGroup]:
        """
        Создает группу узлов эффектора указанного типа.

        Args:
            effector_type: Тип эффектора (например, 'RANDOM', 'NOISE')
            use_custom_group: Использовать ли уникальную группу для каждого экземпляра
            **kwargs: Дополнительные параметры для создания эффектора

        Returns:
            Созданная группа узлов или None, если создание не удалось
        """
        return cls._create_component('EFFECTOR', effector_type, use_custom_group, **kwargs)

    @classmethod
    def create_field(cls, field_type: str, use_custom_group: bool = True, **kwargs) -> Optional[bpy.types.NodeGroup]:
        """
        Создает группу узлов поля указанного типа.

        Args:
            field_type: Тип поля (например, 'SPHERE')
            use_custom_group: Использовать ли уникальную группу для каждого экземпляра
            **kwargs: Дополнительные параметры для создания поля

        Returns:
            Созданная группа узлов или None, если создание не удалось
        """
        return cls._create_component('FIELD', field_type, use_custom_group, **kwargs)

    @classmethod
    def _create_component(cls, component_type: str, component_id: str, use_custom_group: bool, **kwargs) -> Optional[bpy.types.NodeGroup]:
        """
        Универсальный метод создания компонента любого типа.

        Args:
            component_type: Тип компонента ('CLONER', 'EFFECTOR', 'FIELD')
            component_id: ID компонента
            use_custom_group: Использовать ли уникальную группу
            **kwargs: Дополнительные параметры

        Returns:
            Созданная группа узлов или None
        """
        try:
            # Получаем класс компонента из реестра
            component_class = cls._get_component_class(component_type, component_id)
            if not component_class:
                print(f"Unknown {component_type.lower()} type: {component_id}")
                return None

            # Получаем метаданные компонента
            meta = cls._get_component_meta(component_type, component_id)
            if not meta:
                print(f"No metadata found for {component_type.lower()} type: {component_id}")
                return None

            # Создаем группу узлов
            if use_custom_group:
                # Создаем уникальную группу с суффиксом
                obj = kwargs.get('obj')
                suffix = ""
                if obj:
                    suffix = f".{obj.name}.{hash(obj.name) % 1000:03d}"

                # Создаем группу с суффиксом
                node_group = component_class.create_node_group(name_suffix=suffix)
            else:
                # Создаем базовую группу
                node_group = component_class.create_node_group()

            # Добавляем метаданные в группу узлов для идентификации
            if node_group:
                node_group["component_type"] = component_type
                node_group["component_id"] = component_id
                node_group["display_name"] = meta.display_name

            return node_group

        except Exception as e:
            print(f"Error creating {component_type.lower()} {component_id}: {e}")
            return None

    @classmethod
    def _get_component_class(cls, component_type: str, component_id: str):
        """Получает класс компонента из реестра"""
        if component_type == 'CLONER':
            return component_registry.get_cloner_class(component_id)
        elif component_type == 'EFFECTOR':
            return component_registry.get_effector_class(component_id)
        elif component_type == 'FIELD':
            return component_registry.get_field_class(component_id)
        return None

    @classmethod
    def _get_component_meta(cls, component_type: str, component_id: str):
        """Получает метаданные компонента из реестра"""
        if component_type == 'CLONER':
            return component_registry._cloner_meta.get(component_id)
        elif component_type == 'EFFECTOR':
            return component_registry._effector_meta.get(component_id)
        elif component_type == 'FIELD':
            return component_registry._field_meta.get(component_id)
        return None

    @classmethod
    def get_available_cloners(cls) -> Dict[str, Any]:
        """Получает словарь доступных клонеров"""
        return {
            component_id: component_registry.get_cloner_class(component_id)
            for component_id in component_registry._cloners.keys()
        }

    @classmethod
    def get_available_effectors(cls) -> Dict[str, Any]:
        """Получает словарь доступных эффекторов"""
        return {
            component_id: component_registry.get_effector_class(component_id)
            for component_id in component_registry._effectors.keys()
        }

    @classmethod
    def get_available_fields(cls) -> Dict[str, Any]:
        """Получает словарь доступных полей"""
        return {
            component_id: component_registry.get_field_class(component_id)
            for component_id in component_registry._fields.keys()
        }

    @classmethod
    def get_cloner_mod_name(cls, cloner_type: str) -> str:
        """Получает имя модификатора для клонера"""
        meta = component_registry._cloner_meta.get(cloner_type)
        return meta.display_name if meta else f"Unknown Cloner ({cloner_type})"

    @classmethod
    def get_effector_mod_name(cls, effector_type: str) -> str:
        """Получает имя модификатора для эффектора"""
        meta = component_registry._effector_meta.get(effector_type)
        return meta.display_name if meta else f"Unknown Effector ({effector_type})"

    @classmethod
    def get_field_mod_name(cls, field_type: str) -> str:
        """Получает имя модификатора для поля"""
        meta = component_registry._field_meta.get(field_type)
        return meta.display_name if meta else f"Unknown Field ({field_type})"

    @classmethod
    def get_component_type_from_modifier(cls, modifier) -> str:
        """
        Определяет тип компонента из модификатора.
        
        Args:
            modifier: Модификатор компонента
            
        Returns:
            str: Тип компонента или "UNKNOWN"
        """
        if not modifier or not modifier.node_group:
            return "UNKNOWN"
        
        # Проверяем метаданные в группе узлов
        component_id = modifier.node_group.get("component_id")
        if component_id:
            return component_id
        
        # Проверяем по имени группы узлов
        node_group_name = modifier.node_group.name
        
        # Проверяем клонеры
        for cloner_id, meta in component_registry._cloner_meta.items():
            if meta.display_name.replace(" ", "") in node_group_name:
                return cloner_id
        
        # Проверяем эффекторы
        for effector_id, meta in component_registry._effector_meta.items():
            if meta.display_name.replace(" ", "") in node_group_name:
                return effector_id
        
        # Проверяем поля
        for field_id, meta in component_registry._field_meta.items():
            if meta.display_name.replace(" ", "") in node_group_name:
                return field_id
        
        return "UNKNOWN"

# Создаем глобальный экземпляр фабрики
universal_factory = UniversalComponentFactory()
